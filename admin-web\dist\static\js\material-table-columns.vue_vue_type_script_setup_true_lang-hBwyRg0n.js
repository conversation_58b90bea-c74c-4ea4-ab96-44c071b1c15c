import{c as n,f as p,q as l,B as a,h as i,J as d,i as w,F as h,o as t}from"./.pnpm-Kv7TmmH8.js";const u=n({name:"undefined"}),m=n({...u,props:{autoWidth:{type:Boolean,default:!1},showCode:{type:Boolean,default:!0},showDetail:{type:Boolean,default:!0}},setup(r){const o=r;return(s,f)=>{const e=w("el-table-column");return t(),p(h,null,[o.showCode?(t(),l(e,{key:0,prop:"code",label:"物料编码",align:"left",width:o.autoWidth?void 0:180,"show-overflow-tooltip":""},null,8,["width"])):a("",!0),i(e,{prop:"name",label:"名称",align:"left",width:o.autoWidth?void 0:200,"show-overflow-tooltip":""},null,8,["width"]),i(e,{prop:"model",label:"型号",align:"left","min-width":o.autoWidth?void 0:200,"show-overflow-tooltip":""},null,8,["min-width"]),o.showDetail?(t(),l(e,{key:1,prop:"size",label:"尺寸",align:"left",width:200,"show-overflow-tooltip":""})):a("",!0),o.showDetail?(t(),l(e,{key:2,prop:"material",label:"材质",align:"left",width:120,"show-overflow-tooltip":""})):a("",!0),o.showDetail?(t(),l(e,{key:3,prop:"process",label:"工艺",align:"left",width:120,"show-overflow-tooltip":""})):a("",!0),o.showDetail?(t(),l(e,{key:4,prop:"coverColor",label:"颜色",align:"left",width:80,"show-overflow-tooltip":""})):a("",!0),d(s.$slots,"columns")],64)}}});export{m as _};
