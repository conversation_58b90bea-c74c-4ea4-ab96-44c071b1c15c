import{k as w}from"./index-BuqCFB-b.js";import{c as p,r as h,U as k,b as y,f as C,y as N,h as o,w as s,i as n,j as x,E as c,o as U}from"./.pnpm-Kv7TmmH8.js";import{a as B}from"./index-BAHxID_w.js";import{u as E}from"./page-config-BwmDs9Iw.js";const I={class:"view-my"},P=N("div",{class:"title"}," 基本信息 ",-1),S=p({name:"my-info"}),z=p({...S,setup(j){const{service:f,route:_}=B(),{user:r}=w(),{addPageConfig:b}=E();b(_.path,{isShowSlider:!1});const e=h(k(r.info)),d=y(!1);async function v(){const{headImg:u,nickName:a,password:m}=e;d.value=!0,await f.base.comm.personUpdate({headImg:u,nickName:a,password:m}).then(()=>{e.password="",c.success("修改成功"),r.get()}).catch(l=>{c.error(l.message)}),d.value=!1}return(u,a)=>{const m=n("cl-upload"),l=n("el-form-item"),i=n("el-input"),V=n("el-button"),g=n("el-form");return U(),C("div",I,[P,o(g,{"label-width":"100px",model:e,disabled:d.value},{default:s(()=>[o(l,{label:"头像"},{default:s(()=>[o(m,{modelValue:e.headImg,"onUpdate:modelValue":a[0]||(a[0]=t=>e.headImg=t),"is-private":!1},null,8,["modelValue"])]),_:1}),o(l,{label:"昵称"},{default:s(()=>[o(i,{modelValue:e.nickName,"onUpdate:modelValue":a[1]||(a[1]=t=>e.nickName=t),placeholder:"请填写昵称"},null,8,["modelValue"])]),_:1}),o(l,{label:"密码"},{default:s(()=>[o(i,{modelValue:e.password,"onUpdate:modelValue":a[2]||(a[2]=t=>e.password=t),type:"password"},null,8,["modelValue"])]),_:1}),o(l,null,{default:s(()=>[o(V,{type:"primary",disabled:d.value,onClick:v},{default:s(()=>[x(" 保存修改 ")]),_:1},8,["disabled"])]),_:1})]),_:1},8,["model","disabled"])])}}});export{z as default};
