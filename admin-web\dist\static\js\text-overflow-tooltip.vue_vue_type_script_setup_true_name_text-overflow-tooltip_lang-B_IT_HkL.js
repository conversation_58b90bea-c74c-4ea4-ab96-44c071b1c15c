import{c as n,b as c,z as l,f as r,h as p,i,w as _,y as f,t as d,o as u}from"./.pnpm-Kv7TmmH8.js";const v={style:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis"}},m=n({name:"text-overflow-tooltip"}),x=n({...m,props:{content:{}},setup(s){const e=s,t=c(e.content);return l(()=>e.content,o=>{t.value=o}),(o,w)=>{const a=i("el-tooltip");return u(),r("div",null,[p(a,{effect:"dark",content:t.value},{default:_(()=>[f("div",v,d(t.value),1)]),_:1},8,["content"])])}}});export{x as _};
