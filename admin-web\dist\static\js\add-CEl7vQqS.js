import{c as se,b as I,e as _e,Z as Ge,bc as He,A as ye,i as h,H as Pe,f as z,o as y,y as p,h as e,w as r,t as P,j as g,F as ge,s as be,W as X,G as K,q as M,Y as Ze,E as S,af as Ve,ag as ke,ae as Je,z as Ke,U as Xe,S as et,B as R,v as O,I as Se,T as fe,a0 as Ce}from"./.pnpm-Kv7TmmH8.js";import{u as tt,_ as lt}from"./bom-B8yQin8t.js";import{n as ie}from"./index-CBanFtSc.js";import{s as A}from"./index-BuqCFB-b.js";import{_ as Te}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{a as at}from"./index-BAHxID_w.js";const Y=$=>(Ve("data-v-d13d4077"),$=$(),ke(),$),ot={class:"contract"},rt=Y(()=>p("div",{class:"contract-header"},[p("div",{class:"contract-header-left"},[p("p",null,"深圳市绿烟科技有限公司"),p("p",null,"深圳市宝安区西乡街道固戍二路汇潮工业区B栋4楼")]),p("div",{class:"contract-header-right"},[p("p",null,"采购订单"),p("p",null,"PURCHASE ORDER")])],-1)),nt={class:"contract-infomation"},it={class:"grid-content ep-bg-purple"},st={class:"grid-content ep-bg-purple-light"},ut={class:"grid-content ep-bg-purple"},ct={class:"grid-content ep-bg-purple-light"},dt=Y(()=>p("div",{class:"grid-content ep-bg-purple"}," 采购Buyer：李小姐 ",-1)),pt=Y(()=>p("div",{class:"grid-content ep-bg-purple-light"}," 币别Currency：人民币 ",-1)),mt={class:"grid-content ep-bg-purple"},ht={class:"grid-content ep-bg-purple-light"},vt={class:"grid-content ep-bg-purple-light"},ft={class:"grid-content ep-bg-purple"},_t={class:"contract-materials"},yt=Y(()=>p("span",{style:{color:"#f56c6c"}},"*",-1)),gt=Y(()=>p("span",{style:{color:"#f56c6c"}},"*",-1)),bt=Y(()=>p("span",{style:{color:"#f56c6c"}},"*",-1)),It=se({name:"purchase-order-contract"}),wt=se({...It,props:{modelValue:{type:Object,default:()=>({})},isValidateContract:{type:Boolean,default:!1}},setup($){const U=$,ee=[{label:"采购下单",value:0,type:"success"},{label:"人工录入",value:1,type:"warning"}],W=[{label:"月结",value:0},{label:"现结",value:1}],C=I([]),d=I(U.modelValue),E=_e(()=>d.value.createTime),V=13,j=Ge().add(7,"days").format("YYYY-MM-DD");function G(x,v){const b=v/100,D=(x/(1+b)).toFixed(4);return D.endsWith(".00")?D:`≈${D}`}function Q(x){return(x.unitPrice*x.quantity).toFixed(2)}const te=_e(()=>{const x=d.value.contracts.reduce((v,b)=>v+Number(Q(b)),0).toFixed(2);return[{title:"合计",subtotal:x,space:"",capitalTitle:"大写Capital：",capital:Number(x)>0?He.cn.encodeB(x):""}]}),B=I(!1);function le(x){const{materialId:v,supplierId:b}=x;if(!v){S.error("物料ID错误");return}if(!b){S.error("供应商ID错误");return}B.value=!0,ue(),A.pms.material.price.getPrices({materialId:v,supplierId:b}).then(T=>{C.value=T,x.showPriceSelector=!0}).catch(T=>{S.error((T==null?void 0:T.message)||"获取物料价格失败")}).finally(()=>{B.value=!1})}function H(x){d.value.contracts.forEach(v=>{v.materialId===x.materialId&&v.supplierId===x.supplierId&&(v.unitPrice=x.price,v.showPriceSelector=!1)})}function ue(){C.value=[]}return ye(()=>{var x;d.value.contracts=(x=d.value.contracts)==null?void 0:x.map(v=>((!v.taxRate||v.taxRate<=0)&&(v.taxRate=V),v.deliveryDate||(v.deliveryDate=j.toString()),v.unitPrice||(v.unitPrice=v.defaultUnitPrice||0),v.showPriceSelector=!1,v))}),(x,v)=>{const b=h("el-col"),T=h("cl-date-text"),D=h("el-row"),L=h("el-option"),ae=h("el-select"),f=h("el-table-column"),oe=h("el-input-number"),Z=h("cl-svg"),J=h("el-button"),ce=h("el-tag"),F=h("el-table"),q=h("el-popover"),de=h("el-date-picker"),pe=h("el-input"),me=h("el-form"),he=Pe("loading");return y(),z("div",ot,[rt,p("div",nt,[e(D,null,{default:r(()=>[e(b,{span:8},{default:r(()=>{var _;return[p("div",it," 供应商："+P(((_=d.value)==null?void 0:_.supplier.name)||""),1)]}),_:1}),e(b,{span:8},{default:r(()=>[p("div",st," 订单号码PO: "+P(d.value.po),1)]),_:1}),e(b,{span:7},{default:r(()=>[p("div",ut,[g(" 订单日期："),e(T,{modelValue:E.value,"onUpdate:modelValue":v[0]||(v[0]=_=>E.value=_),format:"YYYY-MM-DD"},null,8,["modelValue"])])]),_:1})]),_:1}),e(D,null,{default:r(()=>[e(b,{span:8},{default:r(()=>{var _;return[p("div",ct," 联系人："+P(((_=d.value)==null?void 0:_.supplier.contact)||""),1)]}),_:1}),e(b,{span:8},{default:r(()=>[dt]),_:1}),e(b,{span:7},{default:r(()=>[pt]),_:1})]),_:1}),e(D,null,{default:r(()=>[e(b,{span:8},{default:r(()=>{var _;return[p("div",mt," 电话Tel："+P(((_=d.value)==null?void 0:_.supplier.phone)||""),1)]}),_:1}),e(b,{span:8},{default:r(()=>{var _;return[p("div",ht," 传真Fax："+P(((_=d.value)==null?void 0:_.supplier.fax)||""),1)]}),_:1}),e(b,{span:7},{default:r(()=>[p("div",vt,[g(" 付款条件Payment Terms： "),e(ae,{modelValue:d.value.paymentTerm,"onUpdate:modelValue":v[1]||(v[1]=_=>d.value.paymentTerm=_),size:"small",style:{width:"200px"}},{default:r(()=>[(y(),z(ge,null,be(W,_=>e(L,{key:_.value,label:_.label,value:_.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])])]),_:1})]),_:1}),e(D,null,{default:r(()=>[e(b,{span:8},{default:r(()=>{var _;return[p("div",ft," 地址Address："+P(((_=d.value)==null?void 0:_.supplier.address)||""),1)]}),_:1}),e(b,{span:8}),e(b,{span:7})]),_:1})]),p("div",_t,[e(me,null,{default:r(()=>{var _;return[e(F,{data:(_=d.value)==null?void 0:_.contracts,border:""},{default:r(()=>[e(f,{type:"index",label:"序号",width:"60",align:"center"}),e(f,{prop:"material.code",label:"物料编码",align:"center",width:"150","show-overflow-tooltip":""}),e(f,{prop:"material.name",label:"名称","show-overflow-tooltip":""}),e(f,{prop:"material.model",label:"型号","show-overflow-tooltip":""}),e(f,{prop:"material.size",label:"尺寸/封装",align:"center",width:"100","show-overflow-tooltip":""}),e(f,{prop:"material.material",label:"材质",align:"center",width:"100","show-overflow-tooltip":""}),e(f,{prop:"material.process",label:"工艺",align:"center",width:"80","show-overflow-tooltip":""}),e(f,{prop:"material.cover_color",label:"颜色",align:"center",width:"80","show-overflow-tooltip":""}),e(f,{prop:"unitPrice",label:"含税单价",width:"220",align:"center"},{header:r(({column:m})=>[p("span",null,[g(P(m.label)+" ",1),yt])]),default:r(({row:m})=>[e(oe,{modelValue:m.unitPrice,"onUpdate:modelValue":k=>m.unitPrice=k,precision:4,class:X($.isValidateContract&&!(m.unitPrice>0)?"contract-input-error":""),size:"small",style:{width:"140px"}},null,8,["modelValue","onUpdate:modelValue","class"]),e(q,{visible:m.showPriceSelector,"onUpdate:visible":k=>m.showPriceSelector=k,width:500,teleported:"",placement:"right",trigger:"click"},{reference:r(()=>[e(J,{text:"",class:"SelectPriceBtn",type:m.showPriceSelector?"success":"primary",onClick:Ze(k=>le(m),["stop"])},{default:r(()=>[e(Z,{name:"quillActivity",font:"~ size-16px bold"})]),_:2},1032,["type","onClick"])]),default:r(()=>[K((y(),M(F,{hover:"",data:C.value,"max-height":"250",onRowClick:H},{default:r(()=>[e(f,{align:"center",property:"type",label:"类型",width:"100"},{default:r(({row:k})=>{var re;return[e(ce,{type:(re=ee[k.type])==null?void 0:re.type,size:"small",effect:"plain"},{default:r(()=>{var ne;return[g(P((ne=ee[k.type])==null?void 0:ne.label),1)]}),_:2},1032,["type"])]}),_:2},1024),e(f,{width:"120","show-overflow-tooltip":"",align:"center",property:"price",label:"价格"}),e(f,{align:"center",property:"remark",label:"备注","show-overflow-tooltip":""}),e(f,{width:"160",align:"center",property:"createTime","show-overflow-tooltip":"",label:"创建时间"})]),_:2},1032,["data"])),[[he,B.value]])]),_:2},1032,["visible","onUpdate:visible"])]),_:1}),e(f,{prop:"unitPriceWithoutTax",label:"不含税单价",width:"100",align:"center"},{default:r(({row:m})=>[g(P(G(m.unitPrice,m.taxRate)),1)]),_:1}),e(f,{prop:"quantity",label:"数量",width:"80",align:"center"}),e(f,{prop:"material.unit",label:"单位",width:"80",align:"center"}),e(f,{prop:"subtotal",label:"小计",width:"100",align:"center"},{default:r(({row:m})=>[g(P(Q(m)),1)]),_:1}),e(f,{prop:"taxRate",label:"税率",width:"130",align:"center",required:""},{header:r(({column:m})=>[p("span",null,[g(P(m.label)+" ",1),gt])]),default:r(({row:m})=>[e(oe,{modelValue:m.taxRate,"onUpdate:modelValue":k=>m.taxRate=k,class:X($.isValidateContract&&!(m.taxRate>0)?"contract-input-error":""),size:"small",style:{width:"80px"}},null,8,["modelValue","onUpdate:modelValue","class"]),g(" % ")]),_:1}),e(f,{prop:"deliveryDate",label:"交货日期",width:"130",align:"center"},{header:r(({column:m})=>[p("span",null,[g(P(m.label)+" ",1),bt])]),default:r(({row:m})=>[e(de,{modelValue:m.deliveryDate,"onUpdate:modelValue":k=>m.deliveryDate=k,class:X($.isValidateContract&&!m.deliveryDate?"contract-input-error":""),style:{width:"100px"},type:"date",size:"small","disabled-date":k=>k.getTime()<Date.now()-864e5},null,8,["modelValue","onUpdate:modelValue","class","disabled-date"])]),_:1}),e(f,{prop:"remark",label:"备注",width:"100",align:"center"},{default:r(({row:m})=>[e(pe,{modelValue:m.remark,"onUpdate:modelValue":k=>m.remark=k,size:"small",type:"textarea",autosize:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"]),e(F,{data:te.value,border:"","show-header":!1},{default:r(()=>[e(f,{prop:"title",align:"center",width:"80"}),e(f,{prop:"subtotal",align:"center",width:"100"}),e(f,{prop:"space",align:"center"}),e(f,{prop:"capitalTitle",align:"center",width:"190"}),e(f,{prop:"capital",align:"center",width:"620"})]),_:1},8,["data"])]}),_:1})])])}}}),xt=Te(wt,[["__scopeId","data-v-d13d4077"]]),St=$=>(Ve("data-v-0f4bcd3f"),$=$(),ke(),$),Ct={class:"cl-crud purchase-order-create"},Pt={class:"purchase-order-create-body"},Vt={key:0,class:"purchase-order-create-step-one"},kt=St(()=>p("div",{class:"purchase-order-create-header"}," 基础信息 ",-1)),Tt={class:"purchase-order-create-header"},Dt={flex:"~ justify-end"},Mt={class:"purchase-order-create-header-buttons"},Rt={class:"purchase-order-create-header-buttons-left"},$t={key:0},zt={key:1},Ut={key:1,class:"purchase-order-create-step-two"},Bt={key:0,"bg-white":"",class:"purchase-order-create-header",style:{border:"none"}},Qt={mr1:"",class:"i-material-symbols:keyboard-double-arrow-down-rounded"},Lt={mr1:"",class:"i-material-symbols:keyboard-double-arrow-right"},At={"element-loading-text":"采购合同生成中...",style:{"min-height":"500px"}},qt={key:2},Nt=se({name:"undefined"}),Ot=se({...Nt,setup($){const{router:U}=at(),{height:ee}=Je(),W=I([]),C=I({id:0,parentOrderId:0,orderNo:"",productMaterials:W.value,customMaterials:[]}),d=I([]),E=I([]),V=I(1),j=I(!1),G=I(!1),Q=_e(()=>C.value.parentOrderId>0),te=I([]),B=I([]),le=I({}),H=I(!0),{getBomSummaryCellClassName:ue,getBomSummaryRowStyle:x,getAllUsedColors:v}=tt(le),b=I(!1),T=I([]),D=I([]),L=I(),ae=I(),f=I(!0);function oe(){var t;(t=ae.value)==null||t.open({title:"选择供应商",dialog:{controls:["close"]},op:{saveButtonText:"确认选择"},items:[{label:"供应商",prop:"supplierId",required:!0,value:null,component:{name:"slot-supplier-selector"}}],on:{submit:async(o,{done:a,close:l})=>{var n,c,u;const{supplierId:i}=o;if(!i){S.error("请选择供应商"),a();return}(c=(n=L.value)==null?void 0:n.getSelectionRows())==null||c.map(N=>N.supplierId=i),(u=L.value)==null||u.clearSelection(),l()}}})}const Z=I(!1),J=I(!1),ce=I(!1),F=[],q=I(!1);function de(){if(q.value=!0,Z.value=!0,!d.value.every(l=>l.supplierId)){S.error("请为所有物料选择供应商");return}if(ce.value=!0,!d.value.every(l=>{var i,n;if(l.isSplit){if(l.total===0)return!1;if((i=l.splitDetails)==null||i.forEach(c=>{if(c.total===0)return!1}),!l.isChild){const c=((n=l.splitDetails)==null?void 0:n.reduce((u,N)=>u+N.total,0))||0;return l.originTotal===c+l.total}}return l.total>0})){S.error("拆分的数量有误，请检查");return}if(J.value=!0,!d.value.every(l=>l.purchasePrepareQuantity!==null&&l.purchasePrepareQuantity!==void 0&&l.purchasePrepareQuantity>=0)){S.error("请为所有物料填写采购备货数量");return}q.value=!0,fe.confirm("确认物料信息无误并下一步？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{var i;Z.value=!1,J.value=!1;const l=(i=d.value)==null?void 0:i.filter(n=>!n.isSplit||n.isSplit&&!n.isChild).map(n=>{var c;return{po:n.po,updatePo:n.updatePo,contractId:n.contractId,materialId:n.materialId,supplierId:n.supplierId,purchasePrepareQuantity:n.purchasePrepareQuantity,splitDetails:n.isSplit&&!n.isChild?(c=n.splitDetails)==null?void 0:c.map(u=>({updatePo:u.updatePo,po:u.po,contractId:u.contractId,purchasePrepareQuantity:u.purchasePrepareQuantity,supplierId:u.supplierId,total:u.total})):null}});A.pms.purchase.order.createContract({id:C.value.id,summary:l,delList:F}).then(()=>{const n=V.value+1;U.push({query:{id:C.value.id,step:n}}),V.value=n}).catch(n=>{S.error(n.message||"保存采购订单失败，请重试")}).finally(()=>{q.value=!1})}).catch(()=>{q.value=!1})}function pe(t){return new Promise((o,a)=>{A.pms.bom.GetBomMaterialByProductId({productId:t}).then(l=>{var i,n;return!l||!(l!=null&&l.materials)||!l.materials.length?a(new Error(`产品【${(i=l.product)==null?void 0:i.name} - ${l.product.sku}】未维护BOM`)):((l==null?void 0:l.id)>0&&((n=l==null?void 0:l.changeLog)==null?void 0:n.length)>0&&(le.value[l.id]=l==null?void 0:l.changeLog),o("操作成功"))}).catch(l=>a(l))})}function me(t){var o;(o=t==null?void 0:t.reverse())==null||o.map(a=>(a.generateType===1&&pe(a.generateId),a))}async function he(t){A.pms.purchase.order.info({id:t}).then(o=>{if(!o)return;if(o.status!==0)return S.warning("当前采购订单已经提交，无法编辑");if(C.value.id=Number.parseInt(t),C.value.orderNo=o.orderNo||"",C.value.parentOrderId=o.parentOrderId||0,!o.objects||o.objects.length===0){S.warning("当前获取到的采购订单物料清单为空, 请重新生成");return}if(me(o.objects),U.currentRoute.value.query.step){let l=Number.parseInt(U.currentRoute.value.query.step);l>3&&(l=3),V.value=l}else{const l=o.step||1;V.value=l,U.push({query:{id:C.value.id,step:l}})}}).catch(o=>{S.error(o.message||"获取采购订单失败，请重试")})}function _(){const t=V.value-1;U.push({query:{id:C.value.id,step:t}}),V.value=t}function m(){var o;const t={};(o=W.value)==null||o.map(a=>(a.materials.forEach(i=>{t[i.materialId]===void 0&&(t[i.materialId]=i.inventory);let n=t[i.materialId];i.availableInventory=n;const c=i.quantity*a.generateQuantity+i.prepareQuantity;let u=c;i.isDeductInventory&&(u=c-n,n-=c,n=n>0?n:0,t[i.materialId]=n),i.needQuantity=c,i.total=u>0?u:0}),a))}async function k(){try{const t=await A.pms.supplier.list();te.value=t,B.value=t==null?void 0:t.map(o=>({label:o.supplierName,value:o.id}))}catch(t){S.error(t.message||"获取供应商列表失败")}}function re(){var a;if(Ce(()=>{G.value=!0}),T.value.some(l=>l.contracts.some(i=>!(i.unitPrice>0&&i.taxRate>0&&i.deliveryDate)))){S.error("请完善合同信息，包括单价、税率、数量、交货日期");return}Ce(()=>{G.value=!1});const o=[];(a=T.value)==null||a.map(l=>{var n;const i=(n=l.contracts)==null?void 0:n.map(c=>({id:c.id,materialId:c.materialId,unitPrice:c.unitPrice,taxRate:c.taxRate,quantity:c.quantity,deliveryDate:c.deliveryDate,paymentTerm:l.paymentTerm}));return o.push(...i)}),A.pms.purchase.order.saveContract({id:C.value.id,contracts:o}).then(()=>{const l=V.value+1;V.value=l,U.push({query:{id:C.value.id,step:l}}),S.success("保存合同成功")}).catch(l=>{S.error(l.message||"保存合同失败，请重试")})}function ne(){U.push({path:"/pms/purchase/order",query:{expand:C.value.id}})}function De(){V.value=1,U.push({query:{id:C.value.id}})}function Me(t,o){var l;const a=(o==null?void 0:o.getColumnIndex())||0;a===0||a>=9||t.isSplit||(l=L.value)==null||l.toggleRowSelection(t)}function Re(t){return!t.isSplit}function $e({row:t,columnIndex:o}){var l;if(!t.isSplit)return;if(!(Q.value?[9,10,11,12,14]:[9,10,11,12,14]).includes(o)){const i=((l=t.splitDetails)==null?void 0:l.length)||0;return i>0?{rowspan:i+1,colspan:1}:{rowspan:0,colspan:0}}}function ze(t){t.isSplit&&Ie(t,2)}function Ue(t){let o=0;fe.prompt("请输入拆分数量","提示",{inputPattern:/^\d+$/,inputErrorMessage:"请输入数字"}).then(({value:a})=>{o=Number.parseInt(a),o<=1&&S.error("拆分数量必须大于1"),o>te.value.length&&S.error("拆分数量不能大于供应商数量"),Ie(t,o)}).catch(()=>{})}function Ie(t,o){const a=d.value.findIndex(u=>u.index===t.index&&!u.isChild);if(a===-1)return;const l=o-1,i=d.value[a],n=ie(Math.floor(i.total/o)),c=Array.from({length:l},()=>{const u={...i};return u.total=n,u.supplierId=null,u.index=new Date().getTime()+Math.floor(Math.random()*1e3),u.isChild=!0,u.isSplit=!0,u.purchasePrepareQuantity=0,u.contractId=0,u.po="",u});i.isSplit=!0,i.isChild=!1,d.value.splice(a+1,0,...c)}async function Be(t){const o=t.materialId,a=d.value.find(n=>n.materialId===o&&n.isSplit&&!n.isChild);if(!a)return;const l=d.value.findIndex(n=>n.index===t.index);if(l===-1)return;a.total+=t.total;const i=d.value.splice(l,1);i.length>0&&i[0].contractId&&i[0].contractId>0&&F.push(i[0]),S.warning("请点击下一步保存数据")}function Qe(t,o){const a=o.supplierId,l=o.materialId;return d.value.filter(n=>n.materialId===l).map(n=>n.supplierId).includes(t.value)&&a!==t.value}function Le(){var t;return(t=d.value)==null?void 0:t.filter(o=>!o.isChild).length}Ke(W,()=>{m()},{deep:!0}),ye(()=>{var t,o;f.value=((t=L.value)==null?void 0:t.getSelectionRows().length)===0,d.value=d.value.sort((a,l)=>a.materialId-l.materialId),d.value=d.value.sort((a,l)=>a.materialId===l.materialId?a.isChild?1:-1:0),(o=d.value)==null||o.map(a=>{var i,n;a.splitDetails=a.isSplit&&!a.isChild?d.value.filter(c=>c.materialId===a.materialId&&c.isChild).map(c=>({...c,purchasePrepareQuantity:c.purchasePrepareQuantity,supplierId:c.supplierId,total:c.total})):void 0,a.isSplit&&!a.isChild&&((i=a==null?void 0:a.splitDetails)==null?void 0:i.length)===0&&(a.isSplit=!1);let l=a.originTotal+a.purchasePrepareQuantity;if(a.orderTotal=l,a.isSplit&&!a.isChild){const c=a.originTotal,u=((n=a.splitDetails)==null?void 0:n.reduce((N,ve)=>N+ve.total,0))||0;a.total=c-u,l=a.total+a.purchasePrepareQuantity,a.orderTotal=l}else a.isSplit&&a.isChild&&(a.orderTotal=a.total+a.purchasePrepareQuantity);return a})}),ye(async()=>{V.value===1&&C.value.id&&(j.value=!0,A.pms.purchase.order.summary({id:C.value.id}).then(t=>{d.value=t==null?void 0:t.map(o=>(o.supplierId=o.supplierId===0?null:o.supplierId,o.index=new Date().getTime()+Math.floor(Math.random()*1e3),o.originTotal=o.total,o)),E.value=Xe(d.value)}).catch(t=>{S.error(t.message||"获取采购订单汇总失败")}).finally(()=>{j.value=!1}),await k()),V.value===2&&(T.value=[],b.value=!0,A.pms.purchase.order.contract({id:C.value.id}).then(t=>{(!t||!t.length)&&S.error("当前采购订单没有合同信息，请重新生成"),T.value=t==null?void 0:t.map((o,a)=>({index:a+1,...o}))}).catch(t=>{S.error(t.message||"获取采购订单失败，请重试")}).finally(()=>{b.value=!1}))});function Ae(t){t.contractId>0&&fe.confirm("修改供应商会影响PO号，是否继续？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const o=E.value.find(a=>a.supplierId===t.supplierId);o?t.po=o.po:t.po="",t.updatePo=!0}).catch(()=>{const o=E.value.find(a=>a.contractId===t.contractId);o&&(t.supplierId=o.supplierId)})}return et(async()=>{const t=U.currentRoute.value.query.id;t&&await he(t)}),(t,o)=>{const a=h("el-step"),l=h("el-steps"),i=h("el-tag"),n=h("el-form-item"),c=h("el-button"),u=h("el-table-column"),N=h("el-option"),ve=h("el-select"),we=h("el-input-number"),qe=h("el-table"),Ne=h("el-collapse-item"),Oe=h("el-collapse"),Ee=h("el-result"),Fe=h("el-select-v2"),Ye=h("el-empty"),We=h("cl-form"),xe=Pe("loading");return y(),z("div",null,[p("div",Ct,[p("div",Pt,[e(l,{active:V.value,"finish-status":"success","align-center":"",style:{margin:"10px 0"}},{default:r(()=>[e(a,{title:"汇总物料信息",description:"检查物料清单，确认物料信息"}),e(a,{title:"合同预览",description:"请检查并完善合同信息"}),e(a,{title:"保存采购单到草稿"})]),_:1},8,["active"]),V.value===1?K((y(),z("div",Vt,[kt,e(n,{label:"订单号",prop:"orderNo",style:{margin:"10px 0"}},{default:r(()=>[Q.value?(y(),M(i,{key:0,type:"success",class:"mr-3",size:"large"},{default:r(()=>[g(" 子 ")]),_:1})):R("",!0),e(i,{size:"large"},{default:r(()=>[g(P(C.value.orderNo),1)]),_:1})]),_:1}),p("div",Tt,[g(" 物料汇总 - 共 "+P(Le())+" 种物料 - 总数量 "+P(O(ie)(d.value.reduce((s,w)=>s+w.orderTotal,0)||0))+" ",1),p("div",Dt,[Q.value?R("",!0):(y(),M(lt,{key:0,modelValue:H.value,"onUpdate:modelValue":o[0]||(o[0]=s=>H.value=s),colors:O(v)()},null,8,["modelValue","colors"])),e(c,{type:"success",size:"default",loading:q.value,onClick:de},{default:r(()=>[g(" 下一步 ")]),_:1},8,["loading"])])]),p("div",Mt,[p("div",Rt,[B.value?(y(),M(c,{key:0,type:"warning",disabled:f.value,onClick:oe},{default:r(()=>[g(" 批量设置供应商 ")]),_:1},8,["disabled"])):R("",!0),B.value?R("",!0):(y(),M(i,{key:1,size:"small",type:"danger",style:{"margin-left":"10px"}},{default:r(()=>[g(" 未获取到供应商列表，请先添加供应商 ")]),_:1}))])]),e(qe,{ref_key:"MaterialTable",ref:L,class:"bom-material-table","row-style":H.value?O(x):()=>{},"cell-class-name":O(ue),data:d.value,"max-height":Math.max(O(ee)-522,200),size:"small",style:{width:"100%"},"span-method":$e,border:"",onRowClick:Me},{default:r(()=>[e(u,{type:"selection",width:"50",align:"center",selectable:Re}),e(u,{prop:"code",label:"物料编码",align:"left",width:"150","show-overflow-tooltip":""}),e(u,{prop:"name",label:"物料名称","show-overflow-tooltip":""}),e(u,{prop:"name",label:"名称","show-overflow-tooltip":""}),e(u,{prop:"model",label:"型号","show-overflow-tooltip":""}),e(u,{prop:"size",label:"尺寸/封装",align:"center",width:"100","show-overflow-tooltip":""}),e(u,{prop:"material",label:"材质",align:"center",width:"100","show-overflow-tooltip":""}),e(u,{prop:"process",label:"工艺",align:"center",width:"80","show-overflow-tooltip":""}),e(u,{prop:"cover_color",label:"颜色",align:"center",width:"80","show-overflow-tooltip":""}),e(u,{label:"供应商",width:"200",align:"center"},{default:r(({row:s})=>[e(ve,{modelValue:s.supplierId,"onUpdate:modelValue":w=>s.supplierId=w,placeholder:"请选择供应商",size:"small",filterable:"",style:{width:"100%"},class:X(Z.value&&!s.supplierId?"custom-material-input-error":""),onChange:w=>Ae(s)},{default:r(()=>[(y(!0),z(ge,null,be(B.value,w=>(y(),M(N,{key:w.value,disabled:Qe(w,s),label:w.label,value:w.value},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","class","onChange"])]),_:1}),e(u,{prop:"total",label:"需求数量",align:"center",width:"120"},{default:r(({row:s})=>[s.isSplit&&s.isChild?(y(),z("div",$t,[e(we,{modelValue:s.total,"onUpdate:modelValue":w=>s.total=w,min:0,size:"small",max:s.originTotal,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","max"])])):(y(),z("div",zt,P(O(ie)(s.total)),1))]),_:1}),Q.value?R("",!0):(y(),M(u,{key:0,label:"采购备货",align:"center",width:"150"},{default:r(({row:s})=>[e(we,{modelValue:s.purchasePrepareQuantity,"onUpdate:modelValue":w=>s.purchasePrepareQuantity=w,min:0,size:"small",style:{width:"100%"},class:X(J.value&&!(s.purchasePrepareQuantity!==null&&s.purchasePrepareQuantity!==void 0&&s.purchasePrepareQuantity>=0)?"custom-material-input-error":"")},null,8,["modelValue","onUpdate:modelValue","class"])]),_:1})),e(u,{prop:"orderTotal",label:"合计数量",align:"center",width:"100"},{default:r(({row:s})=>[g(P(O(ie)(s.orderTotal||0)),1)]),_:1}),e(u,{prop:"unit",label:"单位",width:"80",align:"center"}),Q.value?R("",!0):(y(),M(u,{key:1,label:"操作",width:"80",align:"center",fixed:"right"},{default:r(({row:s})=>[s.isSplit?R("",!0):(y(),M(c,{key:0,type:"danger",size:"small",onClick:w=>Ue(s)},{default:r(()=>[g(" 拆分 ")]),_:2},1032,["onClick"])),s.isSplit&&!s.isChild?(y(),M(c,{key:1,type:"success",size:"small",onClick:w=>ze(s)},{default:r(()=>[g(" 添加 ")]),_:2},1032,["onClick"])):R("",!0),s.isSplit&&s.isChild?(y(),M(c,{key:2,type:"primary",size:"small",onClick:w=>Be(s)},{default:r(()=>[g(" 删除 ")]),_:2},1032,["onClick"])):R("",!0)]),_:1}))]),_:1},8,["row-style","cell-class-name","data","max-height"])])),[[xe,j.value]]):R("",!0),V.value===2?(y(),z("div",Ut,[b.value?R("",!0):(y(),z("div",Bt,[g(" 合同预览 - 共计 "+P(T.value.length)+" 份合同 ",1),p("div",null,[e(c,{type:"primary",text:"",bg:"",size:"small",onClick:o[1]||(o[1]=s=>D.value=D.value.length?[]:T.value.map(w=>w.supplierId))},{default:r(()=>[K(p("div",Qt,null,512),[[Se,D.value.length]]),K(p("div",Lt,null,512),[[Se,!D.value.length]]),g(" "+P(D.value.length?"收起":"展开")+"所有合同 ",1)]),_:1})]),p("div",null,[e(c,{type:"warning",size:"default",onClick:_},{default:r(()=>[g(" 上一步 ")]),_:1}),e(c,{type:"success",size:"default",onClick:re},{default:r(()=>[g(" 下一步 ")]),_:1})])])),K((y(),z("div",At,[e(Oe,{modelValue:D.value,"onUpdate:modelValue":o[2]||(o[2]=s=>D.value=s)},{default:r(()=>[(y(!0),z(ge,null,be(T.value,s=>{var w;return y(),M(Ne,{key:s.supplierId,title:`${s.index}：${((w=s.supplier)==null?void 0:w.name)||`供应商 ${s.supplierId}`} - 采购合同`,name:s.supplierId,class:"purchase-order-contract-item"},{default:r(()=>[V.value===2?(y(),M(xt,{key:s.id,modelValue:s,"onUpdate:modelValue":je=>s=je,"is-validate-contract":G.value},null,8,["modelValue","onUpdate:modelValue","is-validate-contract"])):R("",!0)]),_:2},1032,["title","name"])}),128))]),_:1},8,["modelValue"])])),[[xe,b.value]])])):R("",!0),V.value===3?(y(),z("div",qt,[e(Ee,{icon:"success",title:"当前采购订单信息已完善","sub-title":"如果有错误信息，可以重新编辑该采购订单"},{extra:r(()=>[e(c,{type:"danger",onClick:De},{default:r(()=>[g(" 重新编辑 ")]),_:1}),e(c,{type:"primary",onClick:ne},{default:r(()=>[g(" 返回到采购订单列表 ")]),_:1})]),_:1})])):R("",!0)])]),e(We,{ref_key:"SupplierSelectMaterialForm",ref:ae},{"slot-supplier-selector":r(({scope:s})=>[B.value?(y(),M(Fe,{key:0,modelValue:s.supplierId,"onUpdate:modelValue":w=>s.supplierId=w,options:B.value,placeholder:"请选择供应商",filterable:"",clearable:""},null,8,["modelValue","onUpdate:modelValue","options"])):(y(),M(Ye,{key:1,description:"暂时没有供应商数据，请先添加供应商"}))]),_:1},512)])}}}),Ht=Te(Ot,[["__scopeId","data-v-0f4bcd3f"]]);export{Ht as default};
