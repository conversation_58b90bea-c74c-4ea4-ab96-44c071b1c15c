package admin

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/imhuso/lookah-erp/admin/modules/pms/service"
	"github.com/imhuso/lookah-erp/admin/yc"
)

type PmsJobInstructionController struct {
	*yc.Controller
}

// DownloadJobInstructionReq 下载附件
type DownloadJobInstructionReq struct {
	g.Meta `path:"/download" method:"POST" summary:"下载附件" tags:"作业指导"`
	Id     int64 `json:"id" v:"required#作业指导ID不能为空"`
}

// DownloadJobInstruction Download下载附件
func (c *PmsJobInstructionController) DownloadJobInstruction(ctx context.Context, req *DownloadJobInstructionReq) (res *yc.BaseRes, err error) {
	content, fileName, err := service.NewPmsJobInstructionService().DownloadJobInstruction(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	response := g.RequestFromCtx(ctx).Response
	// 设置响应头
	response.Header().Set("Content-Disposition", "attachment; filename="+fileName)
	response.Header().Set("Content-Type", "application/octet-stream")

	response.WriteExit(content)
	return nil, nil
}

func init() {
	var pmsJobInstructionController = &PmsJobInstructionController{
		&yc.Controller{
			Prefix:  "/admin/pms/jobInstruction",
			Api:     []string{"Add", "Delete", "Update", "Info", "List", "Page"},
			Service: service.NewPmsJobInstructionService(),
		},
	}
	// 注册路由
	yc.RegisterController(pmsJobInstructionController)
}
