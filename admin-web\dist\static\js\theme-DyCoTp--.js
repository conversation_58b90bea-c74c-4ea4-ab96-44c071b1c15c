import{c as g,b as h,aN as E,r as V,i as n,f,o as r,y as c,h as o,q as S,B as F,w as s,v as i,aO as I,aP as L,F as b,s as M,V as O,G as T,aQ as $,I as j,t as q,E as P}from"./.pnpm-Kv7TmmH8.js";import{k as Q,v as A,w as H,x as v}from"./index-BuqCFB-b.js";import{_ as J}from"./_plugin-vue_export-helper-DlAUqK2U.js";const K={class:"cl-theme"},R={class:"cl-theme__drawer"},W={class:"cl-theme__comd"},X=["onClick"],Y=g({name:"cl-theme"}),Z=g({...Y,setup(ee){const{menu:k}=Q(),u=h(E()),m=V(A.get("theme")),a=V({color:m.color||"",theme:m}),d=h(!1);function w(){d.value=!0}function y(){u.value=!1}function C(t){Object.assign(a.theme,t),a.color=t.color,v(t),y(),P.success(`切换主题：${t.label}`)}function x(t){v({isGroup:t}),k.setMenu()}function B(t){v({transition:t})}return(t,l)=>{const D=n("cl-svg"),G=n("el-badge"),_=n("el-switch"),p=n("el-form-item"),N=n("el-form"),U=n("el-drawer");return r(),f(b,null,[c("div",K,[o(G,{type:"primary","is-dot":"",onClick:w},{default:s(()=>[o(D,{name:"icon-discover",size:15})]),_:1}),m.name==="default"?(r(),S(_,{key:0,modelValue:u.value,"onUpdate:modelValue":l[0]||(l[0]=e=>u.value=e),style:{marginLeft:"15px"},"inline-prompt":"","active-icon":i(L),"inactive-icon":i(I)},null,8,["modelValue","active-icon","inactive-icon"])):F("",!0)]),o(U,{modelValue:d.value,"onUpdate:modelValue":l[3]||(l[3]=e=>d.value=e),title:"设置主题",size:"350px","append-to-body":""},{default:s(()=>[c("div",R,[o(N,{"label-position":"top"},{default:s(()=>[o(p,{label:"推荐"},{default:s(()=>[c("ul",W,[(r(!0),f(b,null,M(i(H),(e,z)=>(r(),f("li",{key:z,onClick:oe=>C(e)},[c("div",{class:"w",style:O({backgroundColor:e.color})},[T(o(i($),null,null,512),[[j,e.color===a.theme.color]])],4),c("span",null,q(e.label),1)],8,X))),128))])]),_:1}),o(p,{label:"菜单分组显示"},{default:s(()=>[o(_,{modelValue:a.theme.isGroup,"onUpdate:modelValue":l[1]||(l[1]=e=>a.theme.isGroup=e),onChange:x},null,8,["modelValue"])]),_:1}),o(p,{label:"转场动画"},{default:s(()=>[o(_,{modelValue:a.theme.transition,"onUpdate:modelValue":l[2]||(l[2]=e=>a.theme.transition=e),"active-value":"slide","inactive-value":"none",onChange:B},null,8,["modelValue"])]),_:1})]),_:1})])]),_:1},8,["modelValue"])],64)}}}),ne=J(Z,[["__scopeId","data-v-6b087692"]]);export{ne as default};
