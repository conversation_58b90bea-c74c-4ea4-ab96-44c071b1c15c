import{c as ne,b as v,e as Y,z as Ve,A as Ee,ae as Pe,f as D,B as b,q as u,V as X,h as s,w as n,F as U,s as A,i as c,v as f,y as T,G as Le,H as Re,j as _,t as S,Y as g,Z as ee,o as l,m as He,aS as Ue,T as N,E as y,U as te,K as oe,af as Ae,ag as We}from"./.pnpm-Kv7TmmH8.js";import{g as qe,i as B,r as M,c as je}from"./index-BuqCFB-b.js";import{a as Qe}from"./index-BAHxID_w.js";/* empty css              */import{u as ze}from"./table-ops-B_rNWTRT.js";import{_ as Fe}from"./material-table-columns.vue_vue_type_script_setup_true_lang-hBwyRg0n.js";import{_ as Ke}from"./AuditLog.vue_vue_type_script_setup_true_name_AuditLog_lang-YueHTY7t.js";import{V as Ge}from"./index-CBanFtSc.js";import{INBOUND_TYPE as ae}from"./constant-KeG9iMW7.js";import{g as W}from"./index-wptcDEeL.js";import{M as Je}from"./MaterialInbound-BRqGyXen.js";import{_ as Ze}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./AuditLogTable.vue_vue_type_script_setup_true_name_AuditLogTable_lang-CvM6UG7-.js";const Xe=O=>(Ae("data-v-d9eaa406"),O=O(),We(),O),et={key:0},tt={key:1},ot={key:1},at={class:"table-summary"},nt={class:"table-summary-container"},lt=Xe(()=>T("span",{class:"cl-table__expand-footer-title"},"数量总计：",-1)),rt={class:"outbound-images-preview"},st=ne({name:"pms-warehouse-inbound"}),it=ne({...st,setup(O){const{dict:le}=qe(),re=le.get("inbound_outbound_key"),I=-2,i=v(0),{service:p}=Qe(),$=v([{label:"草稿",value:0,type:"info",count:0},{label:"待审批",value:4,type:"info",count:0},{label:"已入库",value:3,type:"success",count:0},{label:"入库明细",value:I}]),V=v({id:0}),q=v({"slot-btn-confirm":{width:80,permission:p.pms.material.inbound.permission.confirm,show:Y(()=>i.value===0)},"slot-audit-log":{width:120,permission:p.pms.material.inbound.permission.confirm,show:!0},"slot-btn-start":{width:110,permission:p.pms.material.inbound.permission.start,show:Y(()=>i.value===1)},"slot-btn-edit":{width:80,permission:p.pms.material.inbound.permission.update,show:Y(()=>i.value===0)},"slot-btn-delete":{width:80,permission:p.pms.material.inbound.permission.delete,show:Y(()=>i.value===0)},"slot-btn-complete":{width:120,permission:p.pms.material.inbound.permission.complete,show:Y(()=>i.value===2)},"slot-btn-revoke":{width:110,permission:p.pms.material.inbound.permission.revoke,show:Y(()=>i.value!==0)},"slot-btn-print":{width:80,permission:p.pms.material.inbound.permission.info,show:!0}}),{getOpWidth:se,checkOpButtonIsAvaliable:w,getOpIsHidden:ie}=ze(q),j=v(),Q=v(!1);Ve(i,()=>{j.value=se(),Q.value=ie()},{immediate:!0});const z=B.useTable({columns:[{label:"#",prop:"products",type:"expand"},{label:"入库单号",prop:"no",width:220},{label:"入库类型",prop:"type",dict:ae},{label:"关联单号",prop:"orderNo",width:220},{label:"入库总数量",prop:"totalQuantity",width:120},{label:"创建时间",prop:"createTime",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD HH:mm:ss"}}},{label:"入库凭证",prop:"voucher",width:120,component:{name:"cl-image",props:{fit:"cover",lazy:!0,size:[50,50]}}},{label:"单据时间",prop:"inboundTime",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}}},{label:"完成时间",prop:"completeTime",component:{name:"cl-date-text",props:{format:"YYYY-MM-DD HH:mm:ss"}}},{label:"备注",prop:"remark",width:150,showOverflowTooltip:!0},{type:"op",label:"操作",width:j,hidden:Q,buttons:Object.keys(q.value)}]}),h=B.useCrud({service:p.pms.material.inbound,async onRefresh(e,{next:t,render:a}){const{count:m,list:k,pagination:r}=await t(e);$.value.forEach(x=>{x.count=m&&m[x.value]||0}),a(k,r)}},e=>{e.refresh({status:i})});function F(e){var t;i.value=e,e!==I&&((t=h.value)==null||t.refresh())}function ue(e,t){var a;(t==null?void 0:t.type)==="expand"||(t==null?void 0:t.type)==="op"||(t==null?void 0:t.property)==="voucher"||(a=z.value)==null||a.toggleRowExpansion(e)}const K=B.useForm();function G(e){var t;if(!e.id)return!1;e.status===0&&((t=K.value)==null||t.open({form:{...e},title:"完善入库信息",width:"400px",dialog:{controls:["close"]},items:[{label:"单据日期",prop:"inboundTime",required:!0,component:{name:"el-date-picker",props:{type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",clearable:!0,disabledDate:a=>a.getTime()>Date.now()}}},{label:"入库凭证",prop:"voucher",required:!0,component:{name:"cl-upload",props:{multiple:!0,limit:5,accept:"image/jpg,image/jpeg,image/png",text:"上传入库凭证",type:"image",disabled:!1,isPrivate:!1}}}],on:{submit:Ue(async(a,{close:m,done:k})=>{p.pms.material.inbound.submit({...e,...a,id:e.id}).then(r=>{var x;Object.prototype.hasOwnProperty.call(r,"status")&&(y.success("提交成功"),m(),i.value=(r==null?void 0:r.status)||0,(x=h.value)==null||x.refresh())}).catch(r=>{y.error(r.message||"提交失败")}),k()},1e3)}}))}const E=v(!1);function pe(e){N.confirm("确定要完成入库吗？<br /><span text-red>注意，提交后将无法撤销，请谨慎操作！！！</span><br />","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).then(()=>{E.value=!0,p.pms.material.inbound.complete({id:e.id}).then(a=>{var m;y.success("完成入库成功"),i.value=(a==null?void 0:a.status)||0,(m=h.value)==null||m.refresh()}).catch(a=>{y.error(a.message)}).finally(()=>{E.value=!1})}).catch(()=>{})}function de(){return"primary-row"}function ce(){M.push("/pms/material/inbound/add")}function me(e){M.push(`/pms/material/inbound/add?id=${e}`)}function be(e){if(!e)return!1;N.confirm("确认删除入库单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{p.pms.material.inbound.delete({ids:[e]}).then(()=>{var t;y.success("入库单删除成功"),(t=h.value)==null||t.refresh()}).catch(t=>{y.error(t.message)})}).catch(()=>{})}const J=v([]);Ee(()=>{const e=M.currentRoute.value.query.tab;e&&(i.value=Number.parseInt(e.toString()),M.replace({query:{tab:void 0}}));const t=M.currentRoute.value.query.expand;t&&(J.value=[Number.parseInt(t.toString())],M.replace({query:{expand:void 0}}))});const fe=B.useSearch({items:[{label:"入库单号",prop:"keyWord",props:{labelWidth:"120px"},component:{name:"el-input",style:{width:"200px"},props:{clearable:!0,onChange(e){var t;(t=h.value)==null||t.refresh({keyWord:e.trim(),page:1})}}}},{label:"入库类型",prop:"type",props:{labelWidth:"80px"},component:{name:"el-select",props:{style:"width: 200px",clearable:!0,onChange(e){var a;const t={type:e,page:1};(a=h.value)==null||a.refresh(t)}},options:ae}},{label:"下单时间",prop:"dateRange",props:{labelWidth:"80px"},component:{name:"el-date-picker",props:{type:"daterange","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始日期",endPlaceholder:"结束日期",clearable:!0,onChange(e){var t;(t=h.value)==null||t.refresh({dateRange:e})}}}},{label:"关键字",prop:"inbound_outbound_key",props:{labelWidth:"80px"},component:{name:"el-select",props:{style:"width: 200px",clearable:!0,onChange(e){var a;const t={inbound_outbound_key:e,page:1};(a=h.value)==null||a.refresh(t)}},options:re}}]});function he(e){if(!e.id)return!1;V.value=te(e)}function ve(e){let t="确定撤销入库单吗？<br /> 撤销后，该入库单将更新到草稿状态。<br />";e.status===3&&e.deliveryNoteId<=0?t+="该入库单已完成入库，撤销时会扣除已入库的数量，请确保库存充足？<br />":e.status===3&&e.deliveryNoteId>0&&(t+="此入库单为送货单同步数据，撤销后，送货单将同时更改状态，并且删除入库单数据，再次提交请进入送货单列表菜单下修改！<br />",t+="该入库单已完成入库，撤销时会扣除已入库的数量，请确保库存充足？<br />"),e.status===3&&e.deliveryNoteId<=0?N.confirm(t,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).then(()=>{p.pms.material.inbound.revoke({id:e.id}).then(()=>{var a;y.success("撤销入库单成功"),(a=h.value)==null||a.refresh()}).catch(a=>{y.error(a.message||"撤销入库单失败")})}).catch(()=>{}):e.status===3&&e.deliveryNoteId>0&&N.confirm(t,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).then(()=>{p.pms.material.inbound.revokeAndSync({id:e.id}).then(()=>{var a;y.success("撤销入库单并同步数据成功"),(a=h.value)==null||a.refresh()}).catch(a=>{y.error(a.message||"撤销入库单并同步数据失败")})}).catch(a=>{console.log("e========================",a)})}const{user:P}=je();function _e(e){var a,m;const t={};if(t.inboundTime=e.inboundTime?oe(e.inboundTime).format("YYYY/MM/DD"):"",t.date=oe(new Date).format("YYYY/MM/DD"),t.user=(a=P==null?void 0:P.info)==null?void 0:a.name,((m=e.products)==null?void 0:m.length)>0){t.list=te(e.products),t.list.forEach(r=>{r.warehouse=W("warehouse_name",r.warehouseId)});const k=e.products[0];t.no=e.no,t.po=k.Po,t.supplierName=k.supplierName}sessionStorage.setItem("printData",JSON.stringify([t])),window.open(`${window.location.origin}/printMaterialInbound.html`,"_blank")}function ye(e){return e.status===0?!1:ee(e.createTime).add(60,"days").isAfter(ee())}const L=v(!1),R=v([]);function ge(e){const t=e.split(",").map(a=>a.trim());R.value=t,L.value=!0}function we(){R.value=[],L.value=!1}function ke(e){return e.orderId&&e.type===1?[{label:"采购数量",prop:"orderQuantity",align:"center",width:100,showOverflowTooltip:!0},{label:"转单数量",prop:"transfer",align:"center",width:100,showOverflowTooltip:!0},{label:"已收数量",prop:"receivedQuantity",align:"center",width:100,showOverflowTooltip:!0},{label:"供应商",prop:"supplierName",align:"left",width:300,showOverflowTooltip:!0}]:e.orderId&&e.type===3?[{label:"Bom用量",prop:"workOrderDetail.calcBomQuantity",align:"center",width:120,showOverflowTooltip:!0},{label:"已领数量",prop:"workOrderDetail.outboundQuantity",align:"center",width:120,showOverflowTooltip:!0}]:[]}function xe(e){return e.isSubmit===1&&(e.status===0||e.status===4)}const{height:Z}=Pe(),Ce=Y(()=>Z.value-240),Ye=Y(()=>Z.value-360);return(e,t)=>{const a=c("el-tab-pane"),m=c("el-tabs"),k=c("cl-refresh-btn"),r=c("el-button"),x=c("cl-flex1"),De=c("cl-search"),H=c("el-row"),C=c("el-table-column"),Te=c("el-table"),Ie=c("cl-table"),Me=c("cl-pagination"),Se=c("cl-form"),Oe=c("el-image-viewer"),Ne=c("cl-crud"),Be=Re("permission");return l(),D("div",null,[i.value===I?(l(),D("div",{key:0,"w-full":"",style:X(`height:${Ce.value}px`)},[s(m,{modelValue:i.value,"onUpdate:modelValue":t[0]||(t[0]=o=>i.value=o),type:"border-card",onTabChange:F},{default:n(()=>[(l(!0),D(U,null,A($.value,o=>(l(),u(a,{key:o.value,label:o.value===I?`${o.label}`:`${o.label}(${o.count})`,name:o.value},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),s(Je,{style:{height:"100%"},api:f(p).pms.material.inbound},null,8,["api"])],4)):b("",!0),i.value!==I?(l(),u(Ne,{key:1,ref_key:"Crud",ref:h},{default:n(()=>[s(H,null,{default:n(()=>[s(k),Le((l(),u(r,{text:"",bg:"",type:"success",onClick:ce},{default:n(()=>[_(" 创建物料入库单 ")]),_:1})),[[Be,f(p).pms.material.inbound.permission.add]]),s(x),s(De,{ref_key:"Search",ref:fe},null,512)]),_:1}),s(m,{modelValue:i.value,"onUpdate:modelValue":t[1]||(t[1]=o=>i.value=o),type:"border-card",onTabChange:F},{default:n(()=>[(l(!0),D(U,null,A($.value,o=>(l(),u(a,{key:o.value,label:o.value===I?`${o.label}`:`${o.label}(${o.count})`,name:o.value},null,8,["label","name"]))),128)),s(H,null,{default:n(()=>[s(Ie,{ref_key:"Table",ref:z,"row-key":"id","expand-row-keys":J.value,class:"table-row-pointer","auto-height":!1,style:X(`height:${Ye.value}px`),onRowClick:ue},{"column-orderNo":n(({scope:o})=>[o.row.type===3?(l(),D("span",et,S(o.row.orderSn||""),1)):(l(),D("span",tt,S(o.row.orderNo||""),1))]),"slot-btn-print":n(({scope:o})=>[f(w)("slot-btn-print")?(l(),u(r,{key:0,text:"",bg:"",type:"primary",onClick:g(d=>_e(o.row),["stop"])},{default:n(()=>[_(" 打印 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-revoke":n(({scope:o})=>[f(Ge)(o.row.createTime)&&f(w)("slot-btn-revoke")&&ye(o.row)?(l(),u(r,{key:0,text:"",bg:"",type:"danger",onClick:g(d=>ve(o.row),["stop"])},{default:n(()=>[_(" 撤销入库 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-audit-log":n(({scope:o})=>[xe(o.row)&&f(w)("slot-audit-log")?(l(),u(r,{key:0,text:"",bg:"",type:"warning",onClick:g(d=>he(o.row),["stop"])},{default:n(()=>[_(" 审核记录 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-confirm":n(({scope:o})=>[f(w)("slot-btn-confirm")?(l(),u(r,{key:0,text:"",bg:"",type:"success",onClick:g(d=>G(o.row),["stop"])},{default:n(()=>[_(" 提交 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-start":n(({scope:o})=>[f(w)("slot-btn-start")?(l(),u(r,{key:0,text:"",bg:"",type:"success",onClick:g(d=>G(o.row),["stop"])},{default:n(()=>[_(" 开始入库 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-edit":n(({scope:o})=>[f(w)("slot-btn-edit")?(l(),u(r,{key:0,text:"",bg:"",type:"primary",onClick:g(d=>me(o.row.id),["stop"])},{default:n(()=>[_(" 编辑 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-delete":n(({scope:o})=>[f(w)("slot-btn-delete")?(l(),u(r,{key:0,text:"",bg:"",type:"danger",onClick:g(d=>be(o.row.id),["stop"])},{default:n(()=>[_(" 删除 ")]),_:2},1032,["onClick"])):b("",!0)]),"slot-btn-complete":n(({scope:o})=>[f(w)("slot-btn-complete")?(l(),u(r,{key:0,loading:E.value,text:"",bg:"",type:"success",onClick:g(d=>pe(o.row),["stop"])},{default:n(()=>[_(" 完成入库 ")]),_:2},1032,["loading","onClick"])):b("",!0)]),"column-voucher":n(({scope:o})=>[o.row.voucher&&o.row.voucher.split(",").length>0?(l(),u(r,{key:0,text:"",type:"primary",onClick:g(d=>ge(o.row.voucher),["stop"])},{default:n(()=>[_(" 点击查看 ")]),_:2},1032,["onClick"])):(l(),D("span",ot,"无数据"))]),"column-products":n(({scope:o})=>[s(Te,{data:o.row.products,style:{width:"100%"},border:"","row-class-name":de},{default:n(()=>[o.row.type===1?(l(),u(C,{key:0,prop:"Po",label:"PO",width:"250","show-overflow-tooltip":""})):b("",!0),o.row.type===3?(l(),u(C,{key:1,prop:"workOrderDetail.workOrderNo",label:"工单号",width:"200","show-overflow-tooltip":""})):b("",!0),s(C,{prop:"quantity",label:"入库数量",align:"center",width:"100","show-overflow-tooltip":""}),(l(!0),D(U,null,A(ke(o.row),(d,$e)=>(l(),u(C,He({key:$e,ref_for:!0},d),null,16))),128)),s(C,{prop:"warehouseId",label:"仓位",align:"center",width:"120"},{default:n(({row:d})=>[T("span",null,S(f(W)("warehouse_name",d.warehouseId)),1)]),_:1}),s(C,{prop:"inbound_outbound_key",label:"关键字",align:"center",width:"120"},{default:n(({row:d})=>[T("span",null,S(f(W)("inbound_outbound_key",d.inbound_outbound_key)),1)]),_:1}),s(C,{prop:"address",label:"位置",align:"center",width:"120","show-overflow-tooltip":""}),s(Fe,{"auto-width":""}),s(C,{prop:"unit",label:"单位",align:"center",width:"70","show-overflow-tooltip":""})]),_:2},1032,["data"]),T("div",at,[T("div",nt,[lt,T("span",null,S(o.row.totalQuantity),1)])])]),_:1},8,["expand-row-keys","style"])]),_:1}),s(H,null,{default:n(()=>[s(x),s(Me)]),_:1})]),_:1},8,["modelValue"]),s(Se,{ref_key:"InboundForm",ref:K},null,512),T("div",rt,[L.value?(l(),u(Oe,{key:0,"url-list":R.value,teleported:"",onClose:we},null,8,["url-list"])):b("",!0)]),s(Ke,{modelValue:V.value.id,"onUpdate:modelValue":t[2]||(t[2]=o=>V.value.id=o)},null,8,["modelValue"])]),_:1},512)):b("",!0)])}}}),kt=Ze(it,[["__scopeId","data-v-d9eaa406"]]);export{kt as default};
