import{g as de,i as P,e as Z}from"./index-BuqCFB-b.js";import{a as me}from"./index-BAHxID_w.js";import{c as te,b as C,q as M,w as _,h as p,y as _e,G as U,i as c,H as fe,v as $,j,f as J,s as X,F as ee,E as f,Z as oe,M as be,N as he,o as k}from"./.pnpm-Kv7TmmH8.js";const ge=te({name:"pms-production-deduct-manhour"}),qe=te({...ge,setup(ve){const G=C(null),u=C(!1);de();const{service:b}=me(),N=C([]),z=C([]),B=C([]),L=C([]);async function re(){try{const e=await b.pms.production.schedule.request({url:"/list",method:"POST"});N.value=e,z.value=e==null?void 0:e.map(o=>({value:o.id,label:o.sn}))}catch(e){console.error(e)}}re();const T=[{label:"临时1（试产）",value:1,name:"-",nameEn:"-",type:"info"},{label:"临时2（首次量产）",value:2,name:"-",nameEn:"-",type:"warning"},{label:"正式（量产）",value:3,name:"-",nameEn:"-",type:"success"}],S=[{label:"加工段",value:1,name:"-",nameEn:"-",type:"info"},{label:"组装段",value:2,name:"-",nameEn:"-",type:"info"},{label:"老化段",value:3,name:"-",nameEn:"-",type:"info"},{label:"包装段",value:4,name:"-",nameEn:"-",type:"info"},{label:"加工段一",value:5,name:"-",nameEn:"-",type:"info"},{label:"加工段二",value:6,name:"-",nameEn:"-",type:"info"},{label:"加工段三",value:7,name:"-",nameEn:"-",type:"info"},{label:"芯子配件加工段一",value:8,name:"-",nameEn:"-",type:"info"},{label:"芯子配件加工段二",value:9,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段一",value:10,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段二",value:11,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段三",value:12,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段四",value:13,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段五",value:14,name:"-",nameEn:"-",type:"info"},{label:"芯子配件包装段",value:15,name:"-",nameEn:"-",type:"info"}],m=P.useUpsert({props:{class:"abnormal-working-form",labelWidth:"120px"},items:[{label:"日期",prop:"abnormal_date",required:!0,component:{name:"el-date-picker",props:{type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",clearable:!0,disabledDate:e=>e.getTime()>Date.now()}}},{label:"生产订单",prop:"order_id",required:!0,component:{name:"slot-order-select"}},{label:"机型",prop:"product_id",required:!0,component:{name:"slot-product-select"}},{label:"订单数量(pcs)",prop:"order_quantity",required:!0,component:{name:"slot-input-quantity"}},{label:"生产阶段",prop:"production_stages",required:!0,component:{name:"el-select",props:{filterable:!0},options:T}},{label:"工段",prop:"workshop_section",required:!0,component:{name:"el-select",props:{filterable:!0},options:S}},{label:"人数(人)",prop:"number_of_people",required:!0,component:{name:"el-input-number",props:{type:"number",placeholder:"请输入人数",step:1,formatter:K,onChange(e){var r,n;let o=parseFloat((r=m.value)==null?void 0:r.getForm("man_hour"));if(o&&e){let l=o/e;(n=m.value)==null||n.setForm("average_working_hours",V(l))}}}}},{label:"生产数量(pcs)",prop:"quantity",required:!0,component:{name:"el-input-number"}},{label:"累计工时(H)",prop:"man_hour",required:!0,component:{name:"el-input-number",props:{onChange(e){var n,l,v,s;let o=parseFloat((n=m.value)==null?void 0:n.getForm("number_of_people")),r=parseFloat((l=m.value)==null?void 0:l.getForm("labor_cost"));if(e&&o){let h=e/o;(v=m.value)==null||v.setForm("average_working_hours",V(h))}if(r&&e){let h=e*r;(s=m.value)==null||s.setForm("total_cost",V(h))}}}}},{label:"人均工时(H)",prop:"average_working_hours",required:!0,component:{name:"el-input-number",props:{disabled:!0}}},{label:"人工费用(元/H)",prop:"labor_cost",required:!0,component:{name:"el-input-number",props:{onChange(e){var r,n;let o=parseFloat((r=m.value)==null?void 0:r.getForm("man_hour"));if(e&&o){let l=o*e;(n=m.value)==null||n.setForm("total_cost",V(l))}}}}},{label:"累计费用(元)",prop:"total_cost",required:!0,component:{name:"el-input-number",props:{disabled:!0}}},{label:"责任单位",prop:"accountability_unit",required:!0,component:{name:"el-input"}},{label:"描述",prop:"description",required:!0,component:{name:"el-input",props:{type:"textarea"}}},{label:"备注",prop:"re_mark",required:!0,component:{name:"el-input",props:{type:"textarea"}}}],async onOpen(e){},async onOpened(){},async onClose(e,o){o()},async onInfo(e,{done:o}){Q(e.order_id),o(e)}}),ae=P.useTable({columns:[{type:"selection"},{label:"ID",prop:"id",width:60},{label:"订单号",prop:"order_id",width:120,formatter:e=>{var o;return((o=N.value.find(r=>r.id===Number.parseInt(e.order_id)))==null?void 0:o.sn)||"-"}},{label:"订单数量",prop:"order_quantity",width:120},{label:"异常日期",prop:"abnormal_date",width:130,component:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}}},{label:"产品名",prop:"product_id",width:160,formatter:e=>{var o;return((o=L.value.find(r=>r.value===Number.parseInt(e.product_id)))==null?void 0:o.label)||"-"}},{label:"工序",prop:"workshop_section",width:160,showOverflowTooltip:!0,formatter:e=>{var o;return((o=S.find(r=>r.value===Number.parseInt(e.workshop_section)))==null?void 0:o.label)||"-"}},{label:"生产阶段",prop:"production_stages",width:160,showOverflowTooltip:!0,formatter:e=>{var o;return((o=T.find(r=>r.value===Number.parseInt(e.production_stages)))==null?void 0:o.label)||"-"}},{label:"sku",prop:"sku",width:120,showOverflowTooltip:!0},{label:"生产数量(PCS)",prop:"quantity",width:120},{label:"责任单位",prop:"accountability_unit",width:160,showOverflowTooltip:!0},{label:"异常工时",align:"center",children:[{label:"人数(人)",prop:"number_of_people",width:120},{label:"人均工时(H)",prop:"average_working_hours",width:120},{label:"累计工时(H)",prop:"man_hour",width:120}]},{label:"异常费用",align:"center",children:[{label:"人工费用(元/H)",prop:"labor_cost",width:120},{label:"累计费用(元)",prop:"total_cost",width:120}]},{label:"描述",prop:"description",showOverflowTooltip:!0},{label:"备注",prop:"re_mark",showOverflowTooltip:!0},{type:"op",buttons:["edit","delete"]}]}),Y=P.useCrud({service:b.pms.AbnormalWorkingHours,async onRefresh(e,{next:o,render:r}){const{list:n,pagination:l}=await o(e);r(n,l)}},e=>{e.refresh({})}),W=P.useSearch({items:[{label:"SKU",prop:"keyWord",props:{labelWidth:"120px"},component:{name:"el-input",props:{clearable:!1,onChange(e){var o;(o=Y.value)==null||o.refresh({keyWord:e.trim(),page:1})}}}},{label:"订单号",prop:"order_id",props:{labelWidth:"80px"},component:{name:"el-select",props:{style:"width: 200px",clearable:!0,filterable:!0,onChange(e){var o;(o=Y.value)==null||o.refresh({order_id:e,page:1})}},options:z}},{label:"下单时间",prop:"dateRange",props:{labelWidth:"80px"},component:{name:"el-date-picker",props:{type:"daterange","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始日期",endPlaceholder:"结束日期",clearable:!0,onChange(e){var o;(o=Y.value)==null||o.refresh({dateRange:e,page:1})}}}}]});async function ne(){try{const e=await b.pms.product.request({url:"/getAllProduct",method:"GET"});L.value=e==null?void 0:e.map(o=>({group_id:o.groupId,value:o.id,sku:o.sku,label:`${o.sku} ${o.name}`}))}catch(e){console.error(e)}}ne();async function Q(e){try{B.value=await b.pms.production.schedule.request({url:"/getProductListByOrderId",method:"POST",data:{order_id:e}})}catch(o){console.error(o)}}function V(e){return Math.round(e*100)/100}function le(e){var r;const o=(r=m.value)==null?void 0:r.getForm("order_id");b.pms.production.schedule.request({url:"/getOrderProductQuantity",method:"POST",data:{order_id:o,product_id:e}}).then(n=>{var l;(l=m.value)==null||l.setForm("order_quantity",n.quantity)}).catch(n=>{f.error(n.message||"查询失败")})}function se(e){var r,n;let o=(r=B.value.find(l=>l.id===e))==null?void 0:r.sku;(n=m.value)==null||n.setForm("sku",o)}function ue(){var s,h,y;let e=(s=W.value)==null?void 0:s.getForm("keyWord"),o=(h=W.value)==null?void 0:h.getForm("order_id"),r=(y=W.value)==null?void 0:y.getForm("dateRange"),n="",l="";r&&r.length>0&&(n=oe(r[0]).format("YYYY-MM-DD"),l=oe(r[1]).format("YYYY-MM-DD"));const v={url:"/export",method:"GET",responseType:"blob",params:{start:n,end:l,keyWord:e,order_id:o}};b.pms.AbnormalWorkingHours.request(v).then(w=>{var q;Z(w)&&f.success("导出成功"),(q=Y.value)==null||q.refresh()}).catch(w=>{f.error(w.message||"导出失败")})}function K(e){return/^\d+$/.test(e)?e:e.slice(0,-1)}function pe(){const e=G.value;e&&e.click()}function i(e){e&&(e.value="")}function ie(){const e="异常工时表_模板.xlsx";fetch("/abnormal_template.xlsx").then(r=>r.blob()).then(r=>{Z(r,e)}).catch(()=>{f.error({message:"下载模板文件失败"})})}async function ce(e){const o=e.target,r=o.files,n={};S&&S.length>0&&S.forEach(s=>{n[s.label]=s.value});const l={};T&&T.length>0&&T.forEach(s=>{l[s.label]=s.value});const v={};if(N.value&&N.value.length>0&&N.value.forEach(s=>{v[s.sn]=s.id}),r&&r.length>0){u.value=!0;const s=r[0],h=new FileReader;h.onload=y=>{var E,d;const w=new Uint8Array((E=y.target)==null?void 0:E.result),q=be(w,{type:"array"}),A=q.Sheets[q.SheetNames[0]],x=he.sheet_to_json(A,{header:1}),g=[void 0,null,"","undefined","null","NaN"],R=["order_id","abnormal_date","workshop_section","production_stages","sku","quantity","accountability_unit","number_of_people","man_hour","labor_cost","description","re_mark"],O=[];if(x&&x.length>0){for(let a=3;a<x.length;a++){const D=x[a];if(D.length==0){i(o),u.value=!1;continue}const t={};for(let F=0;F<D.length;F++){const H=R[F];if(t[H]=(D[F].toString()||"").trim(),H==="order_id"){const I=t.order_id;if(t.order_id=v[t.order_id]?v[t.order_id]:0,t.order_id===0){f.error(`无效的产品名${I}`),i(o),u.value=!1;return}}if(H==="workshop_section"){const I=t.workshop_section;if(t.workshop_section=n[t.workshop_section]?n[t.workshop_section]:0,t.workshop_section===0){f.error(`无效的工序名${I}`),i(o),u.value=!1;return}}if(H==="production_stages"){const I=t.production_stages;if(t.production_stages=l[t.production_stages]?l[t.production_stages]:0,t.production_stages===0){f.error(`无效的生产阶段名${I}`),i(o),u.value=!1;return}}t.labor_cost=Number.parseFloat(t.labor_cost),t.man_hour=Number.parseFloat(t.man_hour),t.number_of_people=Number.parseInt(t.number_of_people),t.quantity=Number.parseInt(t.quantity)}if(t.product_id=(d=L.value.find(F=>F.sku===t.sku))==null?void 0:d.value,g.includes(t.order_id)){f.error("订单号不能为空"),i(o),u.value=!1;return}if(g.includes(t.man_hour)||Number.isNaN(t.man_hour)||t.man_hour===0){i(o),u.value=!1;break}if(g.includes(t.number_of_people)||Number.isNaN(t.number_of_people)||t.number_of_people===0){i(o),u.value=!1;break}if(g.includes(t.labor_cost)){i(o),u.value=!1;break}if(g.includes(t.quantity)){i(o),u.value=!1;break}if(g.includes(t.abnormal_date)){i(o),u.value=!1;break}if(g.includes(t.workshop_section)||Number.isNaN(t.workshop_section)||t.workshop_section===0){i(o),u.value=!1;break}if(g.includes(t.production_stages)||Number.isNaN(t.production_stages)||t.production_stages===0){i(o),u.value=!1;break}if(g.includes(t.sku)||t.sku===""){i(o),u.value=!1;break}O.push(t)}O.length>0?b.pms.AbnormalWorkingHours.importAbnormalData({abnormal_working_hours:O}).then(a=>{var D;(D=Y.value)==null||D.refresh(),f.success(`导入成功：导入${a}条数据！`)}).catch(a=>{f.error(a.message||"导入失败")}).finally(()=>{u.value=!1}):(u.value=!1,f.error("导入数据为空")),i(o)}},h.readAsArrayBuffer(s)}else u.value=!1,f.error("请选择文件")}return(e,o)=>{const r=c("cl-refresh-btn"),n=c("cl-add-btn"),l=c("el-button"),v=c("cl-multi-delete-btn"),s=c("cl-flex1"),h=c("cl-search"),y=c("el-row"),w=c("cl-table"),q=c("cl-pagination"),A=c("el-option"),x=c("el-select"),g=c("el-input"),R=c("cl-upsert"),O=c("cl-crud"),E=fe("permission");return k(),M(O,{ref_key:"Crud",ref:Y},{default:_(()=>[p(y,null,{default:_(()=>[p(r),p(n),_e("input",{ref_key:"fileInputRef",ref:G,type:"file",style:{display:"none"},accept:".xlsx, .xls",onChange:ce},null,544),U((k(),M(l,{size:"default",loading:u.value,type:"warning",class:"mb-10px mr-10px",ml:"20px",onClick:pe},{default:_(()=>[j(" Excel导入 ")]),_:1},8,["loading"])),[[E,$(b).pms.AbnormalWorkingHours.permission.importAbnormalData]]),U((k(),M(l,{type:"info",class:"mb-10px mr-10px",size:"default",onClick:ie},{default:_(()=>[j(" 下载Excel模板 ")]),_:1})),[[E,$(b).pms.AbnormalWorkingHours.permission.importAbnormalData]]),U((k(),M(l,{type:"success",onClick:ue},{default:_(()=>[j(" 导出 ")]),_:1})),[[E,$(b).pms.AbnormalWorkingHours.permission.export]]),U(p(v,null,null,512),[[E,$(b).pms.AbnormalWorkingHours.permission.delete]]),p(s),p(h,{ref_key:"Search",ref:W},null,512)]),_:1}),p(y,{style:{"margin-top":"10px"}},{default:_(()=>[p(w,{ref_key:"Table",ref:ae},null,512)]),_:1}),p(y,null,{default:_(()=>[p(s),p(q)]),_:1}),p(R,{ref_key:"Upsert",ref:m},{"slot-order-select":_(({scope:d})=>[p(x,{modelValue:d.order_id,"onUpdate:modelValue":a=>d.order_id=a,filterable:"",placeholder:"选择生产订单",onChange:a=>{Q(a),d.product_id=void 0,d.order_quantity=void 0}},{default:_(()=>[(k(!0),J(ee,null,X(N.value,a=>(k(),M(A,{key:a.id,label:a.sn,value:a.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),"slot-product-select":_(({scope:d})=>[p(x,{modelValue:d.product_id,"onUpdate:modelValue":a=>d.product_id=a,filterable:"",remote:"",placeholder:"选择产品",onChange:o[0]||(o[0]=a=>{se(a),le(a)})},{default:_(()=>[(k(!0),J(ee,null,X(B.value,a=>(k(),M(A,{key:a.id,label:`${a.sku} ${a.name}`,value:a.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-input-quantity":_(({scope:d})=>[p(g,{modelValue:d.order_quantity,"onUpdate:modelValue":a=>d.order_quantity=a,type:"number",placeholder:"请输入产品数量",formatter:K,step:"1",disabled:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1},512)]),_:1},512)}}});export{qe as default};
