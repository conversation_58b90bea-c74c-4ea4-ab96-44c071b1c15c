import{c as n,_ as m,e as f,$ as h,P as g,i as c,f as v,q as y,o as u,h as e,w as r,v as d,y as x,t as b,J as j,j as S,E as k}from"./.pnpm-Kv7TmmH8.js";const C={key:0,class:"cl-code-json__wrap"},N={class:"text"},w=n({name:"cl-code-json"}),V=n({...w,props:{modelValue:[String,Object],popover:Boolean,height:{type:[Number,String],default:"100%"},maxHeight:{type:[Number,String],default:300}},setup(p){const o=p,{copy:_}=m(),s=f(()=>{const t=o.modelValue;return h(t)?t:g(t)?JSON.stringify(t,null,4):""}),i=n({setup(t,{slots:a}){function l(){_(s.value),k.success("复制成功")}return()=>e("div",{class:"cl-code-json"},[e("div",{class:"op"},[e(c("el-button"),{type:"success",size:"small",onClick:l},{default:()=>[S("copy")]}),a.op&&a.op()]),e(c("el-scrollbar"),{class:"scrollbar","max-height":o.maxHeight,height:o.height},{default:()=>[e("pre",null,[e("code",null,[s.value])])]})])}});return(t,a)=>{const l=c("el-popover");return p.popover?(u(),v("div",C,[e(l,{width:"400px",placement:"right","popper-class":"cl-code-json__popper"},{reference:r(()=>[x("span",N,b(s.value),1)]),default:r(()=>[e(d(i))]),_:1})])):(u(),y(d(i),{key:1},{op:r(()=>[j(t.$slots,"op")]),_:3}))}}});export{V as default};
