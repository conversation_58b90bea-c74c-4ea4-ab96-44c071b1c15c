import{I as t}from"./inbound-outbound-add-BbW3fG_s.js";import{c as o,q as r,o as p}from"./.pnpm-Kv7TmmH8.js";import"./material-excel-import.vue_vue_type_script_setup_true_lang-xGZIJ5UI.js";import"./index-BuqCFB-b.js";import"./index-BAHxID_w.js";import"./material-selector.vue_vue_type_script_name_product-selector_setup_true_lang-B31T1lMk.js";import"./material-CnT3-AWx.js";import"./index-CBanFtSc.js";import"./material-table-columns.vue_vue_type_script_setup_true_lang-hBwyRg0n.js";import"./purchase-pending-order.vue_vue_type_script_setup_true_lang-CxJm1w1Z.js";import"./select-dict.vue_vue_type_script_setup_true_name_select-dict_lang-rWvH4Sy8.js";/* empty css              */import"./OutboundTable.vue_vue_type_script_setup_true_name_OutboundTable_lang-BGd-CvjM.js";import"./index-BQv5-z8i.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const m=o({name:"undefined"}),y=o({...m,setup(i){return(e,n)=>(p(),r(t,{type:"inbound"}))}});export{y as default};
