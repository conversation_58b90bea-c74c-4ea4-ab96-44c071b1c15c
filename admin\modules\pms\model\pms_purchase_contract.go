package model

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/imhuso/lookah-erp/admin/yc"
)

const TableNamePmsPurchaseContract = "pms_purchase_contract"

type PmsPurchaseContractMap struct {
	IdMap map[int64]*PmsPurchaseContract  `json:"idMap"`
	PoMap map[string]*PmsPurchaseContract `json:"poMap"`
}

// PmsPurchaseContractExcel 2025-1-3修改，解决数据转换错误
type PmsPurchaseContractExcel struct {
	Po               string  `json:"po"`
	ReceivedQuantity float64 `json:"receivedQuantity"`
	Quantity         float64 `json:"quantity"`
	SupplierName     string  `json:"supplierName"`
	PaymentTerm      int     `json:"payment_term"`
	UnitPrice        float64 `json:"unit_price"`
	Code             string  `json:"code"`
	Name             string  `json:"name"`
	Model            string  `json:"model"`
	Size             string  `json:"size"`
	Material         string  `json:"material"`
	Process          string  `json:"process"`
	CoverColor       string  `json:"coverColor"`
	Unit             string  `json:"unit"`
	//*PmsMaterial     `json:"material"`
}

type PmsPurchaseOrderContractInboundOutput struct {
	g.Meta `orm:"table:pms_purchase_contract"`

	ID               int64   `json:"id"`
	PurchaseId       int64   `json:"-"`
	ReceivedQuantity float64 `json:"receivedQuantity"`
	Quantity         float64 `json:"orderQuantity"`
	ExpectedQuantity float64 `json:"expectedQuantity"`
	Transfer         float64 `json:"transfer"`
	SupplierId       int64   `json:"supplierId"`
	Po               string  `json:"Po"`
	PaymentTerm      int     `json:"paymentTerm"`

	*PmsSupplierSecretOutput `orm:"with:id=supplier_id" gorm:"-"`
}

type PmsPurchaseOrderContractPrivateOutput struct {
	g.Meta `orm:"table:pms_purchase_contract"`

	ID               int64       `json:"id"`
	PurchaseId       int64       `json:"-"`
	MaterialId       int64       `json:"materialId"`
	ReceivedQuantity float64     `json:"receivedQuantity"`
	Quantity         float64     `json:"quantity"`
	Transfer         float64     `json:"transfer"`
	DeliveryDate     *gtime.Time `json:"deliveryDate"`
	ExpectedQuantity float64     `json:"expectedQuantity"`
	SupplierId       int64       `json:"supplierId"`
	Po               string      `json:"po"`
	PaymentTerm      int         `json:"paymentTerm"`

	*PmsPurchaseOrderContractMaterialOutput `orm:"with:id=material_id" gorm:"-"`
	*PmsSupplierSecretOutput                `orm:"with:id=supplier_id" gorm:"-"`
}

// PmsPurchaseOrderContractOutput 定义订单合同在采购中的关联信息
type PmsPurchaseOrderContractOutput struct {
	g.Meta `orm:"table:pms_purchase_contract"`

	ID               int64       `json:"id"`
	PurchaseId       int64       `json:"purchaseId"`
	MaterialId       int64       `json:"materialId"`
	SupplierId       int64       `json:"supplierId"`
	UnitPrice        float64     `json:"unitPrice"`
	Quantity         float64     `json:"quantity"`
	TaxRate          float64     `json:"taxRate"`
	DeliveryDate     *gtime.Time `json:"deliveryDate"`
	Remark           string      `json:"remark"`
	ReceivedQuantity float64     `json:"receivedQuantity"`
	ExpectedQuantity float64     `json:"expectedQuantity"`
	Transfer         float64     `json:"transfer"`
	Subtotal         float64     `json:"subtotal"`
	PreparesTotal    float64     `json:"preparesTotal"`
	Po               string      `json:"po"`
	PaymentTerm      int         `json:"paymentTerm"`
	// 虚拟订单
	VirtualOrder int    `json:"virtualOrder"`
	MatchedPo    string `json:"matchedPo"`
	OrderNo      string `json:"orderNo"`
	SupName      string `json:"supName"`
	// 关联扩展信息 使用 contract_id 关联
	Extra *PmsPurchaseOrderContractExtraOutput `json:"extra" orm:"with:contract_id=id" gorm:"-"`
	// 关联物料信息
	Material *PmsPurchaseOrderContractMaterialOutput `json:"material" orm:"with:id=material_id" gorm:"-"`
	// 关联供应商信息
	*PmsSupplierSecretOutput `orm:"with:id=supplier_id" gorm:"-"`
	MaterialName             string      `json:"materialName"`
	Code                     string      `json:"code" gorm:"-"`
	Model                    string      `json:"model" gorm:"-"`
	Size                     string      `json:"size" gorm:"-"`
	MaterialType             string      `json:"materialType" gorm:"-"`
	Process                  string      `json:"process" gorm:"-"`
	CoverColor               string      `json:"coverColor" gorm:"-"`
	Unit                     string      `json:"unit" gorm:"-"`
	CreateTime               *gtime.Time `json:"createTime" gorm:"-"`
	No                       string      `json:"no" gorm:"-"`
	// 生产SN
	ProductionSN     string `json:"productionSN" gorm:"-"`
	OrderStatusLabel string `json:"orderStatusLabel" gorm:"-"`
	OrderStatus      int    `json:"orderStatus" gorm:"-"`
	ParentOrderId    int64  `json:"parentOrderId" gorm:"-"`
}

type PmsPurchaseContractListOutput struct {
	OrderNo     string                       `json:"orderNo"`
	CreateTime  *gtime.Time                  `json:"createTime"`
	SupplierId  int64                        `json:"supplierId"`
	Supplier    *PmsSupplierOutput           `json:"supplier"`
	Contracts   []*PmsPurchaseContractOutput `json:"contracts"`
	PaymentTerm int                          `json:"paymentTerm"`
	Po          string                       `json:"po"`
}

type PmsPurchaseContractOutput struct {
	g.Meta `orm:"table:pms_purchase_contract"`

	ID               int64       `json:"id"`
	Remark           string      `json:"remark"`
	PurchaseId       int64       `json:"purchaseId"`
	MaterialId       int64       `json:"materialId"`
	SupplierId       int64       `json:"supplierId"`
	UnitPrice        float64     `json:"unitPrice"`
	Quantity         float64     `json:"quantity"`
	TaxRate          float64     `json:"taxRate"`
	DeliveryDate     *gtime.Time `json:"deliveryDate"`
	DefaultUnitPrice float64     `json:"defaultUnitPrice"`

	Material *PmsPurchaseOrderContractMaterialOutput `json:"material"`
}

// PmsPurchaseContract mapped from table <pms_purchase_contract>
type PmsPurchaseContract struct {
	ID               int64       `json:"id"       gorm:"column:id;type:bigint(20);not null;primary_key;auto_increment;comment:ID;"`             // ID
	Remark           string      `json:"remark"       gorm:"column:remark;type:varchar(255);not null;default:'';comment:备注;"`                   // 备注
	PaymentStatus    string      `json:"payment_status"       gorm:"column:payment_status;type:varchar(255);not null;default:'';comment:付款状态;"` // 付款状态
	PurchaseId       int64       `json:"purchaseId"   gorm:"column:purchase_id;type:bigint(20);not null;default:0;comment:采购单ID;"`              // 采购单ID
	Po               string      `json:"po"           gorm:"column:po;type:varchar(255);not null;default:'';comment:PO号;"`                      // PO号
	MaterialId       int64       `json:"materialId"   gorm:"column:material_id;type:bigint(20);not null;default:0;comment:物料ID;"`               // 物料ID
	SupplierId       int64       `json:"supplierId" gorm:"column:supplier_id;type:bigint(20);not null;default:0;comment:供应商ID;"`
	UnitPrice        float64     `json:"unitPrice"    gorm:"column:unit_price;type:decimal(14,4);not null;default:0;comment:单价;"`                   // 单价
	Quantity         float64     `json:"quantity"     gorm:"column:quantity;type:decimal(14,4);not null;default:0.0000;comment:数量;"`                // 数量
	TaxRate          float64     `json:"taxRate"    gorm:"column:tax_rate;type:decimal(10,2);not null;default:0;comment:税率;"`                       // 税率
	DeliveryDate     *gtime.Time `json:"deliveryDate" gorm:"column:delivery_date;type:date;comment:交货日期;"`                                          // 交货日期
	ReceivedQuantity float64     `json:"receivedQuantity" gorm:"column:received_quantity;type:decimal(14,4);not null;default:0.0000;comment:已收数量;"` // 已收数量
	ExpectedQuantity float64     `json:"expectedQuantity" gorm:"column:expected_quantity;type:decimal(14,4);not null;default:0.0000;comment:在途数量;"` // 在途数量
	Transfer         float64     `json:"transfer" gorm:"column:transfer;type:decimal(14,4);not null;default:0.0000;comment:转单数量;"`
	Subtotal         float64     `json:"subtotal" gorm:"column:subtotal;type:decimal(14,4);not null;default:0;comment:小计;"`               // 小计
	PreparesTotal    float64     `json:"preparesTotal" gorm:"column:prepares_total;type:decimal(14,4);not null;default:0;comment:总备货数量;"` // 总备货数量
	PaymentTerm      int         `json:"paymentTerm" gorm:"column:payment_term;type:tinyint(1);not null;default:0;comment:付款方式;"`         // 付款方式
	// 虚拟订单
	VirtualOrder int     `json:"virtualOrder" gorm:"column:virtual_order;type:tinyint(1);not null;default:0;comment:虚拟订单; 1是，0否"` // 虚拟订单
	TotalDemand  float64 `json:"totalDemand" gorm:"column:total_demand;type:decimal(14,4);not null;default:0.0000;comment:总需求量;"` // 总需求量
	// 匹配的PO
	MatchedPo     string      `json:"matchedPo" gorm:"column:matched_po;type:varchar(255);not null;default:'';comment:匹配的PO;"`                                                        // 匹配的PO
	MatchedPoType int         `json:"matchedPoType" gorm:"column:matched_po_type;type:tinyint(1);not null;default:0;comment: 1:MatchedPo采购Excel数据有，系统没有,2：MatchedPo系统有,采购Excel数据没有，"` // 匹配的PO类型
	CreateTime    *gtime.Time `json:"createTime" gorm:"column:createTime;comment:创建时间"`                                                                                               // 创建时间
	// 创建人
	CreatorID int64 `json:"creatorID" gorm:"column:creator_id;type:bigint(20);not null;default:0;comment:创建人ID;"` // 创建人ID
	// 关联供应商信息
	SupplierOutput *PmsSupplierOutput `json:"supplier" orm:"with:id=supplier_id" gorm:"-"`
	// 关联订单信息
	OrderOutput *PmsPurchaseOrderOutput `json:"purchase" orm:"with:id=purchase_id" gorm:"-"`
	// 关联物料信息
	MaterialOutput              *PmsPurchaseOrderContractMaterialOutput `json:"material" orm:"with:id=material_id" gorm:"-"`
	Code                        string                                  `json:"code" gorm:"-"` // 合同编号
	UpdatePo                    bool                                    `json:"updatePo"  gorm:"-"`
	PurchaseContractReturnList  []*PmsPurchaseContractReturn            `json:"purchaseContractReturnList" gorm:"-"`
	MaterialInboundProductList  []*PmsMaterialInboundProduct            `json:"materialInboundProductList" gorm:"-"`
	MaterialOutboundProductList []*PmsMaterialOutboundProduct           `json:"materialOutboundProductList" gorm:"-"`
}

// GroupName 返回分组名
func (m *PmsPurchaseContract) GroupName() string {
	return ""
}

// TableName PmsPurchaseContract's table name
func (*PmsPurchaseContract) TableName() string {
	return TableNamePmsPurchaseContract
}

// NewPmsPurchaseContract 创建实例
func NewPmsPurchaseContract() *PmsPurchaseContract {
	return &PmsPurchaseContract{}
}

func init() {
	_ = yc.CreateTable(NewPmsPurchaseContract())
}
