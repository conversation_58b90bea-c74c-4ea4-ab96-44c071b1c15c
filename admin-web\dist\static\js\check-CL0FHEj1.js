import{i as C,h as b}from"./index-BuqCFB-b.js";import{a as w}from"./index-BAHxID_w.js";import{c as i,b as a,z as B,i as s,f as N,o as T,y as d,h as l,w as g}from"./.pnpm-Kv7TmmH8.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";const S={class:"cl-dept-check"},U={class:"cl-dept-check__search"},z={class:"cl-dept-check__tree"},A=i({name:"cl-dept-check"}),I=i({...A,props:{modelValue:{type:Array,default:()=>[]},checkStrictly:Boolean},emits:["update:modelValue"],setup(n,{emit:u}){const p=n,m=u,{service:_}=w(),o=a(),r=a(),c=a("");async function h(){return _.base.sys.department.list().then(e=>{r.value=b(e)})}function f(e,t){return e?t.name.includes(e):!0}function k(e,{checkedKeys:t}){m("update:modelValue",t)}return B(c,e=>{o.value.filter(e)}),C.useUpsert({async onOpened(){await h(),o.value.setCheckedKeys(p.modelValue||[])}}),(e,t)=>{const y=s("el-input"),v=s("el-tree"),x=s("el-scrollbar");return T(),N("div",S,[d("div",U,[l(y,{modelValue:c.value,"onUpdate:modelValue":t[0]||(t[0]=V=>c.value=V),placeholder:"输入关键字进行过滤"},null,8,["modelValue"])]),d("div",z,[l(x,{"max-height":"200px"},{default:g(()=>[l(v,{ref_key:"Tree",ref:o,"node-key":"id","show-checkbox":"",data:r.value,props:{label:"name",children:"children"},"filter-node-method":f,"check-strictly":n.checkStrictly,onCheck:k},null,8,["data","check-strictly"])]),_:1})])])}}}),D=E(I,[["__scopeId","data-v-378aa2ba"]]);export{D as default};
