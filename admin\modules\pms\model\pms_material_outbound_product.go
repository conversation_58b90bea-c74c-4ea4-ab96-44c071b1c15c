package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	"github.com/imhuso/lookah-erp/admin/yc"
)

const TableNamePmsMaterialOutboundProduct = "pms_material_outbound_product"

type MaterialOutboundProductOutput struct {
	gmeta.Meta `orm:"table:pms_material_outbound_product"`

	ID                                     int64       `json:"id"`
	OutboundId                             int64       `json:"outboundId"`
	RestockingQty                          float64     `json:"restockingQty"`
	MaterialId                             int64       `json:"materialId"`
	Quantity                               float64     `json:"quantity"`
	TotalQuantity                          float64     `json:"total_quantity"`
	ContractId                             int64       `json:"contractId"`
	Remark                                 string      `json:"remark"`
	Address                                string      `json:"address"`
	ProductGroupId                         int64       `json:"product_group_id"`
	ProductGroupName                       string      `json:"product_group_name"`
	HandlingMethod                         string      `json:"handling_method"`
	Responsible                            string      `json:"responsible"`
	InboundOutboundKey                     int         `json:"inbound_outbound_key"`
	ScrapDate                              *gtime.Time `json:"scrap_date"`
	*PmsMaterialOutput                     `orm:"with:id=material_id"`
	*PmsPurchaseOrderContractInboundOutput `orm:"with:id=contract_id"`
}

// PmsMaterialOutboundProduct mapped from table <pms_material_outbound_product>
type PmsMaterialOutboundProduct struct {
	ID            int64   `json:"id"       gorm:"column:id;type:bigint(20);not null;primary_key;auto_increment;comment:ID;"`           // ID
	OutboundId    int64   `json:"outboundId"       gorm:"column:outbound_id;type:bigint(20);not null;default:0;comment:出库单ID;"`        // 出库单ID
	MaterialId    int64   `json:"materialId"       gorm:"column:material_id;type:bigint(20);not null;default:0;comment:物料ID;"`         // 物料ID
	ContractId    int64   `json:"contractId" gorm:"column:contract_id;type:bigint(20);not null;default:0;comment:合同ID;"`               // 合同ID
	Quantity      float64 `json:"quantity" gorm:"column:quantity;type:decimal(14,4);not null;default:0.0000;comment:出库数量;"`            // 出库数量
	RestockingQty float64 `json:"restockingQty" gorm:"column:restocking_qty;type:decimal(14,4);not null;default:0.0000;comment:补货数量;"` // 补货数量
	WorkOrderId   int64   `json:"workOrderId" gorm:"column:work_order_id;type:bigint(20);not null;default:0;comment:工单ID;"`            // 工单ID
	Po            string  `json:"po" gorm:"-"`                                                                                         // PO号
	Remark        string  `json:"remark" gorm:"column:remark;type:varchar(255);default:'';comment:备注;"`                                // 备注

	ProductGroupId     int64       `json:"product_group_id" gorm:"column:product_group_id;type:bigint(20);not null;default:0;comment:产品分组id;"`
	HandlingMethod     string      `json:"handling_method" gorm:"column:handling_method;type:varchar(255);default:'';comment:处理方式;"`
	Responsible        string      `json:"responsible" gorm:"column:responsible;type:varchar(255);default:'';comment:责任人;"`
	ScrapDate          *gtime.Time `json:"scrap_date" gorm:"column:scrap_date;type:date;comment:报废日期;"`
	InboundOutboundKey int         `json:"inbound_outbound_key" gorm:"column:inbound_outbound_key;type:int(10);not null;default:0;comment:关键字ID;"`
	Address            string      `json:"address"      gorm:"column:address;type:varchar(1000);not null;default:'';comment:地址;"`              // 地址
	TotalQuantity      float64     `json:"total_quantity" gorm:"column:total_quantity;type:decimal(14,4);not null;default:0.0000;comment:总数;"` //总数，用于计算报废率
}

// PmsMaterialOutboundPrint mapped from table <pms_material_outbound_product>
type PmsMaterialOutboundPrint struct {
	//gmeta.Meta `orm:"table:pms_material_outbound_product"`
	ID         int64 `json:"id"`         // ID
	OutboundId int64 `json:"outboundId"` // 出库单ID
	MaterialId int64 `json:"materialId"` // 物料ID
	//ContractId int64   `json:"contractId"` // 合同ID
	Quantity         float64     `json:"quantity"` // 出库数量
	No               string      `json:"no"`       // 出库单号
	OutboundTime     *gtime.Time `json:"outboundTime"`
	CreateTime       *gtime.Time `json:"createTime"`
	RestockingQty    float64     `json:"restockingQty"` // 补货数量
	WorkOrderId      int64       `json:"workOrderId"`   // 工单ID
	Po               string      `json:"po"`            // PO号
	Remark           string      `json:"remark"`        // 备注
	Name             string      `json:"name"`
	Code             string      `json:"code"`
	Model            string      `json:"model"`
	Unit             string      `json:"unit"`
	ProductGroupName string      `json:"product_group_name"`
	ProductGroupId   int64       `json:"product_group_id"`
	Responsible      string      `json:"responsible"`
	HandlingMethod   string      `json:"handling_method"`
	ScrapDate        *gtime.Time `json:"scrap_date"`
	TotalQuantity    float64     `json:"total_quantity"`
	Address          string      `json:"address"` // 地址
}

type MaterialOutVo struct {
	*PmsMaterialOutbound
	Code           string  `json:"code"`
	Quantity       float64 `json:"quantity"`
	ProductId      int64   `json:"productId"`
	MaterialId     int64   `json:"materialId"`
	MaterialName   string  `json:"materialName"`
	Sn             string  `json:"sn"`
	TypeLabel      string  `json:"typeLabel"`
	Remark         string  `json:"remark"`
	CalcProductQty float64 `json:"calcProductQty"`
}

type CalculateBomMaterialResult struct {
	PmsBomMaterialQtyMap     map[int64]float64  `json:"pmsBomMaterialQtyMap"`
	PmsBomMaterialCodeQtyMap map[string]float64 `json:"pmsBomMaterialCodeQtyMap"`
	MaterialMap              *MaterialMap       `json:"materialMap"`
	ProductMap               *PmsProductMap     `json:"productMap"`
}

//type CalcBomBomMaterialQ

// GroupName 返回分组名
func (m *PmsMaterialOutboundProduct) GroupName() string {
	return ""
}

// TableName PmsMaterialOutboundProduct's table name
func (*PmsMaterialOutboundProduct) TableName() string {
	return TableNamePmsMaterialOutboundProduct
}

// NewPmsMaterialOutboundProduct 创建实例
func NewPmsMaterialOutboundProduct() *PmsMaterialOutboundProduct {
	return &PmsMaterialOutboundProduct{}
}

func init() {
	_ = yc.CreateTable(NewPmsMaterialOutboundProduct())
}
