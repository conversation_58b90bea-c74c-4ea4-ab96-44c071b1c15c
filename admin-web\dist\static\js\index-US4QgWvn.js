import{u as A}from"./page-config-BwmDs9Iw.js";import{c as i,r as m,f as _,y as e,h as s,i as l,o as p,af as v,ag as f,by as $,bz as w,b as L,bA as W,v as z,w as c}from"./.pnpm-Kv7TmmH8.js";import{_ as h}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{s as T,i as y}from"./index-BuqCFB-b.js";import{a as B}from"./index-BAHxID_w.js";const E=a=>(v("data-v-8545bac3"),a=a(),f(),a),G={class:"category-ratio"},O=E(()=>e("div",{class:"category-ratio__header"},[e("span",null,"销售额类别占比")],-1)),P={class:"category-ratio__container"},M=i({name:"undefined"}),N=i({...M,setup(a){const t=m({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{bottom:30,left:"center",data:["手机","相机","耳机","音箱","手表"]},color:["#3AA1FF","#36CBCB","#F2637B","#975FE5","#FBD437"],series:[{name:"访问来源",type:"pie",radius:["50%","60%"],center:["50%","40%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"30",fontWeight:"bold"}},labelLine:{show:!1},data:[{value:335,name:"手机"},{value:310,name:"相机"},{value:234,name:"耳机"},{value:135,name:"音箱"},{value:500,name:"手表"}],itemStyle:{borderColor:"#fff",borderWidth:4},roundCap:1}]});return(d,n)=>{const o=l("v-chart");return p(),_("div",G,[O,e("div",P,[s(o,{option:t,autoresize:""},null,8,["option"])])])}}}),H=h(N,[["__scopeId","data-v-8545bac3"]]),V={},D={class:"count-sales"},R=$('<div class="card" data-v-7b179371><div class="card__header" data-v-7b179371><span class="label" data-v-7b179371>总销售额</span><span class="value" data-v-7b179371>￥15920</span></div><div class="card__container" data-v-7b179371><ul class="count-sales__cop" data-v-7b179371><li data-v-7b179371><span data-v-7b179371>周同比</span><div class="fall" data-v-7b179371><i class="el-icon-bottom-right" data-v-7b179371></i><span data-v-7b179371>-4%</span></div></li><li data-v-7b179371><span data-v-7b179371>日同比</span><div class="rise" data-v-7b179371><i class="el-icon-top-right" data-v-7b179371></i><span data-v-7b179371>+7%</span></div></li></ul></div><div class="card__footer" data-v-7b179371><span class="label" data-v-7b179371>日销售额</span><span class="value" data-v-7b179371>￥1298.01</span></div></div>',1),j=[R];function q(a,t){return p(),_("div",D,j)}const J=h(V,[["render",q],["__scopeId","data-v-7b179371"]]),g=a=>(v("data-v-852f7607"),a=a(),f(),a),K={class:"count-views"},Q={class:"card"},U=g(()=>e("div",{class:"card__header"},[e("span",{class:"label"},"总访问量"),e("span",{class:"value"},"8846")],-1)),X={class:"card__container"},Y=g(()=>e("div",{class:"card__footer"},[e("span",{class:"label"},"日访问量"),e("span",{class:"value"},"1351")],-1)),Z=i({name:"undefined"}),ee=i({...Z,setup(a){const t=m({grid:{left:0,top:0,right:0,bottom:0},xAxis:{type:"category",data:["00:00","2:00","4:00","6:00","8:00","10:00","12:00","14:00","16:00","18:00","20:00","22:00"],boundaryGap:!1},yAxis:{type:"value",splitLine:{show:!1},axisTick:{show:!1},axisLine:{show:!1},axisLabel:{show:!1}},series:[{name:"总访问量",type:"line",smooth:!0,showSymbol:!1,symbol:"circle",symbolSize:6,data:["1200","1400","1008","1411","1026","1288","1300","800","1100","1000","1118","1322"],areaStyle:{color:new w(0,0,0,1,[{offset:0,color:"#D1E5FF"},{offset:1,color:"#FFFFFF"}],!1)},itemStyle:{color:"#4165d7"},lineStyle:{width:2}}]});return(d,n)=>{const o=l("v-chart");return p(),_("div",K,[e("div",Q,[U,e("div",X,[s(o,{option:t,autoresize:""},null,8,["option"])]),Y])])}}}),se=h(ee,[["__scopeId","data-v-852f7607"]]),S=a=>(v("data-v-c0992c74"),a=a(),f(),a),ae={class:"count-paid"},te={class:"card"},oe=S(()=>e("div",{class:"card__header"},[e("span",{class:"label"},"支付笔数"),e("span",{class:"value"},"6560")],-1)),ce={class:"card__container"},de=S(()=>e("div",{class:"card__footer"},[e("span",{class:"label"},"转化率"),e("span",{class:"value"},"60%")],-1)),ne=i({name:"undefined"}),le=i({...ne,setup(a){const t=m({grid:{left:"10%",top:0,right:"10%",bottom:0},xAxis:{type:"category",data:["00:00","2:00","4:00","6:00","8:00","10:00","12:00","14:00"],boundaryGap:!1},yAxis:{type:"value",splitLine:{show:!1},axisTick:{show:!1},axisLine:{show:!1},axisLabel:{show:!1}},series:[{barWidth:18,name:"付款笔数",type:"bar",data:[81,24,77,13,87,92,68,55],itemStyle:{color:"#4165d7"}},{type:"bar",barWidth:18,xAxisIndex:0,barGap:"-100%",data:[100,100,100,100,100,100,100,100],itemStyle:{color:"#f1f1f9"},zlevel:-1}]});return(d,n)=>{const o=l("v-chart");return p(),_("div",ae,[e("div",te,[oe,e("div",ce,[s(o,{option:t,autoresize:""},null,8,["option"])]),de])])}}}),ie=h(le,[["__scopeId","data-v-c0992c74"]]),re={},_e=a=>(v("data-v-e86ed9ed"),a=a(),f(),a),pe={class:"count-effect"},he={class:"card"},ue=_e(()=>e("div",{class:"card__header"},[e("span",{class:"label"},"总销售额"),e("span",{class:"value"},"￥15920")],-1)),ve={class:"card__container"},fe=$('<div class="card__footer" data-v-e86ed9ed><ul class="count-effect__cop" data-v-e86ed9ed><li data-v-e86ed9ed><span data-v-e86ed9ed>周同比</span><div class="fall" data-v-e86ed9ed><i class="el-icon-bottom-right" data-v-e86ed9ed></i><span data-v-e86ed9ed>-4%</span></div></li><li data-v-e86ed9ed><span data-v-e86ed9ed>日同比</span><div class="rise" data-v-e86ed9ed><i class="el-icon-top-right" data-v-e86ed9ed></i><span data-v-e86ed9ed>+7%</span></div></li></ul></div>',1);function me(a,t){const d=l("el-progress");return p(),_("div",pe,[e("div",he,[ue,e("div",ve,[s(d,{percentage:50,"stroke-width":8})]),fe])])}const be=h(re,[["render",me],["__scopeId","data-v-e86ed9ed"]]),xe=a=>(v("data-v-3f380ca1"),a=a(),f(),a),ye={class:"tab-chart"},$e=xe(()=>e("div",{class:"tab-chart__header"},[e("ul",{class:"tab-chart__tab"},[e("li",{class:"active"}," 销售额 "),e("li",null,"访问量")]),e("span",{class:"tab-chart__year"},"2020")],-1)),we={class:"tab-chart__container"},ge=i({name:"undefined"}),Se=i({...ge,setup(a){const d=m({grid:{top:"20px",bottom:"30px",right:"10px",containLabel:!0},xAxis:{type:"category",data:[],offset:5,axisLine:{show:!1},axisTick:{show:!1}},yAxis:{type:"value",offset:20,splitLine:{show:!1},axisTick:{show:!1},axisLine:{show:!1}},tooltip:{trigger:"axis",formatter:n=>{const[o]=n;return`${o.seriesName}：${o.value}`},axisPointer:{show:!0,status:"shadow",z:-1,shadowStyle:{color:"#E6F7FF"},type:"shadow"},extraCssText:"width:120px; white-space:pre-wrap"},series:[{barWidth:15,name:"付款笔数",type:"bar",data:[],itemStyle:{color:"#4165d7"}},{type:"bar",barWidth:15,xAxisIndex:0,barGap:"-100%",data:[],itemStyle:{color:"#f1f1f9"},zlevel:-1}]});return d.xAxis.data=Array.from({length:12}).fill(1).map((n,o)=>`${o+1}月`),d.series[0].data=Array.from({length:12}).fill(1).map(()=>Number.parseInt(String(Math.random()*100))),d.series[1].data=Array.from({length:12}).fill(100),(n,o)=>{const r=l("v-chart");return p(),_("div",ye,[$e,e("div",we,[s(r,{option:d,autoresize:""},null,8,["option"])])])}}}),ke=h(Se,[["__scopeId","data-v-3f380ca1"]]),Fe={class:"count-sales"},Ce={class:"card"},Ie=e("div",{class:"card__header"},[e("span",{"font-size-15px":"","font-bold":"","c-black":""},"更新日志")],-1),Ae={"h-290px":"","overflow-y-auto":"",px20px:"",pb10px:""},Le=["innerHTML"],We={key:1},ze=i({name:"undefined"}),Te=i({...ze,setup(a){const t=L(""),d=new W;return T.base.comm.version().then(n=>{t.value=(n==null?void 0:n.changelog)??""}),(n,o)=>{const r=l("el-empty");return p(),_("div",Fe,[e("div",Ce,[Ie,e("div",Ae,[t.value?(p(),_("div",{key:0,"w-full":"",class:"markdown-container",innerHTML:z(d).render(t.value)},null,8,Le)):(p(),_("div",We,[s(r,{description:"暂无更新日志"})]))])])])}}}),b=a=>(v("data-v-32b9893e"),a=a(),f(),a),Be={class:"hot-search"},Ee=b(()=>e("div",{class:"hot-search__header"},[e("span",null,"线上热门搜索")],-1)),Ge={class:"hot-search__container"},Oe={class:"block"},Pe=b(()=>e("div",{class:"count"},[e("div",{class:"number"},[e("span",null,"搜索用户数"),e("span",null,"1242")]),e("div",{class:"rise"},[e("i",{class:"el-icon-top-right"}),e("span",null,"+7%")])],-1)),Me={class:"is-last block"},Ne=b(()=>e("div",{class:"count"},[e("div",{class:"number"},[e("span",null,"关注用户数"),e("span",null,"365")]),e("div",{class:"rise"},[e("i",{class:"el-icon-top-right"}),e("span",null,"+2%")])],-1)),He={class:"hot-search__table"},Ve=i({name:"undefined"}),De=i({...Ve,setup(a){const t=m({grid:{left:0,top:0,right:0,bottom:0},xAxis:{type:"category",data:["00:00","2:00","4:00","6:00","8:00","10:00","12:00","14:00","16:00","18:00","20:00","22:00"],boundaryGap:!1},yAxis:{type:"value",splitLine:{show:!1},axisTick:{show:!1},axisLine:{show:!1},axisLabel:{show:!1}},series:[{name:"总访问量",type:"line",smooth:!0,showSymbol:!1,symbol:"circle",symbolSize:6,data:["1200","1400","1008","1411","1026","1288","1300","800","1100","1000","1118","1322"],areaStyle:{color:new w(0,0,0,1,[{offset:0,color:"#D1E5FF"},{offset:1,color:"#FFFFFF"}],!1)},itemStyle:{color:"#4165d7"},lineStyle:{width:2}}]}),d=y.useCrud({service:{page(){return Promise.resolve({list:[{index:1,keyWord:"无线耳机",users:983,ud:5},{index:1,keyWord:"运动耳机",users:763,ud:-3},{index:1,keyWord:"蓝牙音箱",users:328,ud:7},{index:1,keyWord:"4k显示屏",users:144,ud:4},{index:1,keyWord:"罗技 G530",users:121,ud:-1}]})}}},o=>{o.refresh()}),n=y.useTable({autoHeight:!1,contextMenu:!1,columns:[{label:"排名",type:"index",width:60},{label:"搜索关键词",prop:"keyWord",minWidth:100},{label:"用户数",prop:"users",minWidth:100},{label:"周涨幅",prop:"ud",sortable:"desc",minWidth:100}]});return(o,r)=>{const u=l("v-chart"),x=l("el-col"),k=l("el-row"),F=l("cl-table"),C=l("cl-row"),I=l("cl-crud");return p(),_("div",Be,[Ee,e("div",Ge,[s(k,{class:"hot-search__chart",gutter:20},{default:c(()=>[s(x,{md:12,xs:24},{default:c(()=>[e("div",Oe,[Pe,s(u,{option:t,autoresize:""},null,8,["option"])])]),_:1}),s(x,{md:12,xs:24},{default:c(()=>[e("div",Me,[Ne,s(u,{option:t,autoresize:""},null,8,["option"])])]),_:1})]),_:1}),e("div",He,[s(I,{ref_key:"Crud",ref:d,padding:"0"},{default:c(()=>[s(C,null,{default:c(()=>[s(F,{ref_key:"Table",ref:n,border:!1},null,512)]),_:1})]),_:1},512)])])])}}}),Re=h(De,[["__scopeId","data-v-32b9893e"]]),je={class:"view-home"},qe={class:"card"},Je={class:"card"},Ke={class:"card"},Qe={class:"card"},Ue={class:"card"},Xe={class:"card"},Ye={class:"card card--last"},Ze={class:"card card--last"},es=i({name:"home"}),ds=i({...es,setup(a){const{route:t}=B(),{addPageConfig:d}=A();return d(t.path,{isShowSlider:!1,isShowAppProcess:!1}),(n,o)=>{const r=l("el-col"),u=l("el-row");return p(),_("div",je,[s(u,{gutter:15},{default:c(()=>[s(r,{lg:6,md:12,xs:24},{default:c(()=>[e("div",qe,[s(J)])]),_:1}),s(r,{lg:6,md:12,xs:24},{default:c(()=>[e("div",Je,[s(se)])]),_:1}),s(r,{lg:6,md:12,xs:24},{default:c(()=>[e("div",Ke,[s(ie)])]),_:1}),s(r,{lg:6,md:12,xs:24},{default:c(()=>[e("div",Qe,[s(be)])]),_:1})]),_:1}),s(u,{gutter:15},{default:c(()=>[s(r,{lg:14,xs:24},{default:c(()=>[e("div",Ue,[s(ke)])]),_:1}),s(r,{lg:10,xs:24},{default:c(()=>[e("div",Xe,[s(Te)])]),_:1})]),_:1}),s(u,{gutter:15},{default:c(()=>[s(r,{lg:14,sm:24},{default:c(()=>[e("div",Ye,[s(Re)])]),_:1}),s(r,{lg:10,sm:24},{default:c(()=>[e("div",Ze,[s(H)])]),_:1})]),_:1})])}}});export{ds as default};
