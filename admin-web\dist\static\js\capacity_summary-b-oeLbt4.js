import{s as x,e as xe}from"./index-BuqCFB-b.js";import{c as se,b as o,Z as y,E as q,q as O,w as v,y as W,h,G as X,i as g,j,f as I,s as ee,F as te,H as ae,v as Oe,W as Pe,ah as Ee,ai as Me,ad as re,aj as Se,o as C}from"./.pnpm-Kv7TmmH8.js";/* empty css              */import{_ as Te}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ve={class:"production-summary"},We={class:"block",style:{"margin-left":"20px"}},Le={class:"block",style:{"margin-left":"20px"}},Be={class:"block",style:{"margin-left":"20px"}},Fe=se({name:"undefined"}),Ae=se({...Fe,setup(He){const P=o(!1),le=o(1),oe=o(1),b=o(""),k=o("");o("month");const ne=o(y().format("YYYY-MM")),de=o(ne),G=o([]),ce=o([]),E=o([]),L=o([]),M=o([]),S=o([]),Z=o([]),B=o([]),F=o([]),T=o(!0),J=o([]),_=o([y().startOf("month"),y().endOf("month")]),A=o(""),H=o([]);o([]),D(),Ke();async function ie(e){e=e.trim(),e||(M.value=L.value),M.value=L.value.filter(t=>t.sn.toLowerCase().includes(e.toLowerCase()))}function ue(){const e=a=>n=>{let c="";const u=n.rowData;if(u&&u["workorder-success"]===!1?c="row-bg-warning":u&&u["workorder-success"]===!0&&u["workorder-diff_man_hour"]>20?c="row-bg-danger":u&&u["workorder-success"]===!0&&(c="row-bg-success"),a){const i=a(n);if(i&&i.props)return i.props.class=`${i.props.class||""} ${c}`.trim(),i}return h("div",{class:`${n.class||""} ${c}`.trim()},[n.cellData])},t=[{title:"机型",width:120,key:"group_name",dataKey:"group_name",align:"center",cellRenderer:e()},{title:"生产单号",width:120,key:"sn",dataKey:"sn",align:"center",cellRenderer:e()},{title:"SKU",width:130,key:"sku",dataKey:"sku",align:"center",cellRenderer:e()},{title:"颜色",width:200,key:"color_name",dataKey:"color_name",align:"center",showOverflowTooltip:!0,cellRenderer:e()},{title:"生产阶段",width:130,key:"production_stages",dataKey:"production_stages",align:"center",cellRenderer:e(a=>{const n=we(a.cellData);return h("div",{class:a.class},[n])})},{title:"标准单机成本(元)",width:130,key:"standard_unit_cost",dataKey:"standard_unit_cost",align:"center",cellRenderer:e()},{title:"实际单机成本(元)",width:160,key:"actual_unit_cost",dataKey:"actual_unit_cost",align:"center",cellRenderer:e()},{title:"订单产品数量(pcs)",width:130,key:"total_order_count",dataKey:"total_order_count",align:"center",cellRenderer:e()},{title:"剩余生产数量(pcs)",width:130,key:"expect_inbound_quantity",dataKey:"expect_inbound_quantity",align:"center",cellRenderer:e()}];let r=[];Array.isArray(B.value)&&(r=B.value.map(a=>({title:a.title,key:a.key,dataKey:a.dataKey,width:100,align:"center",headerClass:"date-header-cell",cellRenderer:e(a.cellRenderer)}))),S.value=t.concat(r);let l=[];Array.isArray(F.value)&&(l=F.value.map(a=>({title:a.title,key:a.key,dataKey:a.dataKey,width:100,align:"center",headerClass:"date-header-cell",cellRenderer:e(a.cellRenderer)}))),S.value=S.value.concat(l),Z.value=S.value.map((a,n)=>{let c;return window.innerWidth<600?c=void 0:n<9&&(c=re.LEFT),{...a,fixed:c,width:a.width}})}function he(){D()}function ye(){he()}async function D(){T.value=!0;const[e,t]=_.value,r=y(e).startOf("month").format("YYYY-MM-DD"),l=y(t).endOf("month").format("YYYY-MM-DD");le.value,oe.value,x.pms.DailyProductionReport.GetCapacitySummary({start:r,end:l,order_id:b.value,sku:k.value}).then(async a=>{G.value=a,T.value=!1,me(),pe(),_e(),ue()}).catch(a=>{q.error((a==null?void 0:a.message)||"读取数据失败，请重试!"),T.value=!1})}function pe(){const e=[],t={title:"工单是否完成",key:"workorder-success",dataKey:"workorder-success",width:150,align:"center",headerClass:"date-header-cell",cellRenderer:c=>{const u=c.cellData?"是":"否";return h("div",{class:c.class},[u])}};e.push(t);const r={title:"标准工时总计(H)",key:"workorder-standard_man_hour",dataKey:"workorder-standard_man_hour",width:150,align:"center",headerClass:"date-header-cell"};e.push(r);const l={title:"实际工时总计(H)",key:"workorder-actual_man_hour",dataKey:"workorder-actual_man_hour",width:150,align:"center",headerClass:"date-header-cell"};e.push(l);const a={title:"异常工时(H)",key:"workorder-abnormal_working_hours",dataKey:"workorder-abnormal_working_hours",width:150,align:"center",headerClass:"date-header-cell"};e.push(a);const n={title:"工时差异(标准-(实际-异常)，H)",key:"workorder-diff_man_hour",dataKey:"workorder-diff_man_hour",width:150,align:"center",headerClass:"date-header-cell"};return e.push(n),F.value=e,e}function me(){J.value=fe();const e=[],t=y().year(),r=`year-${t}-manhour`,l={title:"工时(h)",key:r,dataKey:r,width:150,align:"center",headerClass:"date-header-cell"};e.push(l);const a=`year-${t}-capacity`,n={title:"生产产能（pcs）",key:a,dataKey:a,width:150,align:"center",headerClass:"date-header-cell"};e.push(n);const c=`year-${t}-standard`,u={title:"标准制造成本(元)",key:c,dataKey:c,width:150,align:"center",headerClass:"date-header-cell"};e.push(u);const i=`year-${t}-practical`,d={title:"实际制造成本(元)",key:i,dataKey:i,width:150,align:"center",headerClass:"date-header-cell"};return e.push(d),J.value.forEach(p=>{const m=p.year,f=p.month<10?`0${p.month}`:p.month;if(`${m}${p.month}`,m===y().year()&&p.month>y().month()+1)return;const s=`date-${m}${f}-manhour`,N={title:"工时(h)",key:s,dataKey:s,width:150,align:"center",headerClass:"date-header-cell"};e.push(N);const K=`date-${m}${f}-capacity`,z={title:"生产产能（pcs）",key:K,dataKey:K,width:150,align:"center",headerClass:"date-header-cell"};e.push(z);const $=`date-${m}${f}-standard`,U={title:"标准制造成本(元)",key:$,dataKey:$,width:150,align:"center",headerClass:"date-header-cell"};e.push(U);const V=`date-${m}${f}-practical`,Y={title:"实际制造成本(元)",key:V,dataKey:V,width:150,align:"center",headerClass:"date-header-cell"};e.push(Y)}),B.value=e,e}function fe(){const e=[];if(!Array.isArray(_.value)||_.value.length<2)throw new Error("dateRange 必须是一个包含两个 Moment 对象的数组");const t=y(_.value[0]),r=y(_.value[1]);if(!t.isValid()||!r.isValid())throw new Error("dateRange 中的日期必须是有效的 Moment 对象");for(;t.isBefore(r)||t.isSame(r,"month");)e.push({year:t.year(),month:t.month()+1,day:0}),t.add(1,"month");return e}function _e(){const e=G.value;E.value=[];let t=0;e.forEach(r=>{const l={...r,index:t};t++,E.value.push(l)}),ce.value=E.value}function ve(e){const t=new Date,r=t.getFullYear(),l=e.getTime()>t.getTime(),a=e.getFullYear()!==r;return l||a}function ke(){D()}function we(e){let t="-";switch(e){case 1:t="临时1（试产）";break;case 2:t="临时2（首次量产）";break;case 3:t="正式（批量生产）";break}return t}function ge(){if(!de.value)return;P.value=!0;const[e,t]=_.value,r=y(e).startOf("month").format("YYYY-MM-DD"),l=y(t).endOf("month").format("YYYY-MM-DD"),a={url:"/exportSummary",method:"POST",responseType:"blob",data:{start:r,end:l,order_id:b.value,sku:k.value}};x.pms.DailyProductionReport.request(a).then(n=>{xe(n)&&q.success("导出成功"),P.value=!1}).catch(n=>{q.error(n.message||"导出失败"),P.value=!1})}function Ce(){return{}}function be({column:e,columnIndex:t}){const r=`hovering-col-${t}`;return{"data-key":r,onMouseenter:()=>{A.value=r},onMouseleave:()=>{A.value=""},onContextmenu:()=>{e.key.startsWith("date-")}}}const De=({cells:e,columns:t,headerIndex:r})=>{if(r===2)return e;const l=[];let a=0,n=1,c=1,u=1;return t.forEach((i,d)=>{var p,m;if(i.placeholderSign===Se)l.push(e[d]);else{const f="year-",s=t[d+1];i.key.startsWith(f);const N=s&&s.key.startsWith(f);d>0&&t[d-1].key.startsWith(f);const K="date-",z=i.key.startsWith(K);s&&s.key.startsWith(K),d>0&&t[d-1].key.startsWith(K);const $="workorder-",U=i.key.startsWith($);s&&s.key.startsWith($),d>0&&t[d-1].key.startsWith($);const V=d===t.length-1;a+=t[d].width,n++,c++,u++;let Y="";if(z&&n%4==0){const w=t[d];if(w.dataKey){const R=(p=w.dataKey.match(/\d{6}/))==null?void 0:p[0];if(!R)throw new Error("未找到日期部分");const Q=R.slice(0,4),Re=R.slice(4);Y=`${Q}年${Re.padStart(2,"0")}月`}l.push(h("div",{class:"custom-header-cell order-header-cell",role:"columnheader",style:{...e[d].props.style,width:`${i.width*4}px`}},[r===0?Y:"制造成本"])),a=0}else if(N&&c%4==0){const w=t[d];if(w.dataKey){const R=(m=w.dataKey.match(/\d{4}/))==null?void 0:m[0];if(!R)throw new Error("未找到日期部分");Y=`${R.slice(0,4)}年总计`}l.push(h("div",{class:"custom-header-cell order-header-cell",role:"columnheader",style:{...e[d].props.style,width:`${i.width*4}px`}},[r===0?Y:"制造成本"])),a=0}else if(U&&u%5==0)l.push(h("div",{class:"custom-header-cell order-header-cell",role:"columnheader",style:{...e[d].props.style,width:`${i.width*5}px`}},[r===0?"工单统计信息":""])),a=0;else if(V){const w=i.fixed===re.LEFT;l.push(h("div",{class:"custom-header-cell",role:"columnheader",style:{...e[d].props.style,width:`${a}px`}},[w&&r===0?"基础信息":""]))}}}),l};async function Ke(){try{const e=await x.pms.production.schedule.request({url:"/list",method:"POST"});L.value=e,M.value=e}catch(e){console.error(e)}}async function $e(e){try{H.value=[],k.value="",H.value=await x.pms.production.schedule.request({url:"/getProductListByOrderId",method:"POST",data:{order_id:e}})}catch(t){console.error(t)}}function Ye(){b.value="",k.value="",D()}return(e,t)=>{const r=g("el-button"),l=g("el-option"),a=g("el-select"),n=g("el-date-picker"),c=g("el-row"),u=g("el-table-v2"),i=g("el-auto-resizer"),d=ae("loading"),p=ae("permission");return C(),O(i,null,{default:v(({height:m,width:f})=>[W("div",Ve,[h(c,{class:"production-summary-header"},{default:v(()=>[h(r,{class:"refresh-button",onClick:ye},{default:v(()=>[j(" 刷新 ")]),_:1}),W("div",We,[h(a,{modelValue:b.value,"onUpdate:modelValue":t[0]||(t[0]=s=>b.value=s),style:{width:"150px"},filterable:"",remote:"",placeholder:"选择生产订单","remote-method":ie,onChange:t[1]||(t[1]=s=>{D(),$e(s)})},{default:v(()=>[(C(!0),I(te,null,ee(M.value,s=>(C(),O(l,{key:s.id,label:s.sn,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),W("div",Le,[h(a,{modelValue:k.value,"onUpdate:modelValue":t[2]||(t[2]=s=>k.value=s),style:{width:"150px"},placeholder:"选择SKU",onChange:t[3]||(t[3]=s=>{D()})},{default:v(()=>[(C(!0),I(te,null,ee(H.value,s=>(C(),O(l,{key:s.id,label:s.sku,value:s.sku},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),W("div",Be,[h(n,{modelValue:_.value,"onUpdate:modelValue":t[4]||(t[4]=s=>_.value=s),"disabled-date":ve,type:"monthrange","range-separator":"To","start-placeholder":"Start date","end-placeholder":"End date",onChange:ke},null,8,["modelValue"])]),X((C(),O(r,{style:{"margin-left":"10px"},class:"export-button",type:"warning",onClick:ge},{default:v(()=>[j(" 导出 ")]),_:1})),[[d,P.value],[p,Oe(x).pms.DailyProductionReport.export]]),h(r,{style:{"margin-left":"10px"},class:"clear-filter-button",type:"success",disabled:k.value===""&&b.value==="",onClick:Ye},{default:v(()=>[j(" 清除过滤 ")]),_:1},8,["disabled"])]),_:1}),X((C(),O(u,{columns:Z.value,"row-key":"code",data:E.value,width:f-20,height:m-60,class:Pe(`production-summary-table ${A.value}`),fixed:"","cell-props":be,"header-cell-props":Ce,"header-height":[35,35,60]},{header:v(s=>[h(De,Ee(Me(s)),null,16)]),_:2},1032,["columns","data","width","height","class"])),[[d,T.value]])])]),_:1})}}}),je=Te(Ae,[["__scopeId","data-v-50165777"]]);export{je as default};
