package service

import (
	"context"
	"fmt"
	"net/url"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/yc"
	"github.com/xuri/excelize/v2"
)

type PmsSupplierService struct {
	*yc.Service
}

func (s *PmsSupplierService) QueryAll(ctx context.Context) (data *model.PmsSupplierMapVo, err error) {
	supplierList := make([]*model.PmsSupplier, 0)
	err = yc.DBM(s.Model).Ctx(ctx).Scan(&supplierList)
	if err != nil {
		return nil, err
	}
	idMap := make(map[int64]*model.PmsSupplier)
	nameMap := make(map[string]*model.PmsSupplier)
	for _, supplier := range supplierList {
		idMap[supplier.ID] = supplier
		nameMap[supplier.Name] = supplier
	}
	return &model.PmsSupplierMapVo{IdMap: idMap, NameMap: nameMap}, nil
}

// ExportSupplier 导出供应商
func (s *PmsSupplierService) ExportSupplier(ctx context.Context) (fileName string, bytes []byte, err error) {
	supplierList := make([]model.PmsSupplier, 0)
	err = yc.DBM(s.Model).Ctx(ctx).Scan(&supplierList)
	if err != nil {
		return "", nil, err
	}

	// 创建一个新的 Excel 文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			g.Log().Error(ctx, "关闭文件失败", err)
		}
	}()

	sheetName := "供应商列表"
	// 创建一个新的工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		g.Log().Error(ctx, "创建工作表失败", err)
		return "", nil, err
	}
	// 设置当前活动的工作表
	f.SetActiveSheet(index)

	// 设置表头
	headers := []string{"序号", "供应商名称", "简称", "主要产品", "联系人", "联系电话", "传真", "地址"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		_ = f.SetCellValue(sheetName, cell, header)
	}

	// 填充数据
	startRow := 2
	for i, supplier := range supplierList {
		row := startRow + i
		_ = f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), i+1)                  // 序号
		_ = f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), supplier.Name)        // 供应商名称
		_ = f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), supplier.ShortName)   // 简称
		_ = f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), supplier.MainProduct) // 主要产品
		_ = f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), supplier.Contact)     // 联系人
		_ = f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), supplier.Phone)       // 联系电话
		_ = f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), supplier.Fax)         // 传真
		_ = f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), supplier.Address)     // 地址
	}

	// 创建临时文件夹
	dir := "temp/"
	if !gfile.IsDir(dir) {
		_ = gfile.Mkdir(dir)
	}

	// 生成文件名
	fileName = fmt.Sprintf("供应商列表_%s.xlsx", gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)
	filePath := gfile.Join(dir, fileName)

	// 保存 Excel 文件
	if err = f.SaveAs(filePath); err != nil {
		g.Log().Error(ctx, "保存Excel文件失败", err)
		return "", nil, err
	}

	// 读取文件内容
	bytes = gfile.GetBytes(filePath)
	// 删除临时文件
	_ = gfile.Remove(filePath)

	return fileName, bytes, nil
}

func NewPmsSupplierService() *PmsSupplierService {
	return &PmsSupplierService{
		&yc.Service{
			Model: model.NewPmsSupplier(),
			PageQueryOp: &yc.QueryOp{
				KeyWordField: []string{"name"},
			},
			ListQueryOp: &yc.QueryOp{
				ScanStruct: &model.PmsSupplierSecretOutput{},
			},
		},
	}
}
