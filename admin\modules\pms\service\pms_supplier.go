package service

import (
	"context"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/yc"
)

type PmsSupplierService struct {
	*yc.Service
}

func (s *PmsSupplierService) QueryAll(ctx context.Context) (data *model.PmsSupplierMapVo, err error) {
	supplierList := make([]*model.PmsSupplier, 0)
	err = yc.DBM(s.Model).Ctx(ctx).Scan(&supplierList)
	if err != nil {
		return nil, err
	}
	idMap := make(map[int64]*model.PmsSupplier)
	nameMap := make(map[string]*model.PmsSupplier)
	for _, supplier := range supplierList {
		idMap[supplier.ID] = supplier
		nameMap[supplier.Name] = supplier
	}
	return &model.PmsSupplierMapVo{IdMap: idMap, NameMap: nameMap}, nil
}

// ExportSupplier 导出供应商
func (s *PmsSupplierService) ExportSupplier(ctx context.Context) (fileName string, bytes []byte, err error) {
	supplierList := make([]model.PmsSupplier, 0)
	err = yc.DBM(s.Model).Ctx(ctx).Scan(&supplierList)
	if err != nil {
		return "", nil, err
	}
	return "", nil, err
}

func NewPmsSupplierService() *PmsSupplierService {
	return &PmsSupplierService{
		&yc.Service{
			Model: model.NewPmsSupplier(),
			PageQueryOp: &yc.QueryOp{
				KeyWordField: []string{"name"},
			},
			ListQueryOp: &yc.QueryOp{
				ScanStruct: &model.PmsSupplierSecretOutput{},
			},
		},
	}
}
