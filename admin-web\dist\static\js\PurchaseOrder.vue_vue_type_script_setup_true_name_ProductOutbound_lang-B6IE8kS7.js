import{c as E,b as w,K as x,q as Q,w as u,h as o,i as r,y as k,j as d,v as p,f as U,F as j,s as z,L as F,t as $,E as G,o as V}from"./.pnpm-Kv7TmmH8.js";import{s as P,i as T,e as R}from"./index-BuqCFB-b.js";const A={style:{"margin-right":"20px"}},H=E({name:"ProductOutbound"}),X=E({...H,props:{showPrice:{type:Boolean,default:!0},api:{default:P.pms.finance}},setup(Y){const f=Y,m=w(!1),_=w(!1),l=w({date:[],keyWord:"",status:void 0}),t=T.useCrud({dict:{api:{page:"purchaseOrderPage"}},service:f.api,async onRefresh(e,{next:a,done:s,render:c}){s(),g(e);const{list:b,pagination:y}=await a(e);c(b,y)}},e=>{e.refresh()});function g(e){if(l.value.date&&l.value.date.length>0){const a=x(l.value.date[0]).format("YYYY-MM-DD"),s=x(l.value.date[1]).format("YYYY-MM-DD");e.date=`${a},${s}`}return l.value.keyWord&&(e.keyWord=l.value.keyWord),l.value.status!==void 0&&(e.status=l.value.status),e.showPrice=f.showPrice,e}const L=[{label:"待处理",value:-1},{label:"待审批",value:1},{label:"已确认",value:2},{label:"入库中",value:3},{label:"在途",value:-2},{label:"已完成",value:4}],O={0:"info",1:"info",2:"primary",3:"primary",4:"success"},S=T.useTable({columns:[{label:"生产单号",prop:"productionSN",width:180},{label:"采购单号",prop:"no",width:180},{label:"Po",prop:"po",width:180},{label:"物料编码",prop:"code",width:180},{label:"物料名称",prop:"materialName",showOverflowTooltip:!0},{label:"规格/型号",prop:"model",width:200,showOverflowTooltip:!0},{label:"单位",prop:"unit",width:80},{label:"供应商",prop:"supName",width:200},{label:"数量",prop:"quantity",width:120},{label:"已收数量",prop:"receivedQuantity",width:120},{label:"在途数量",prop:"expectedQuantity",width:120},{label:"单价",prop:"unitPrice",width:120,hidden:f.showPrice},{label:"状态",prop:"orderStatusLabel",width:120},{label:"创建时间",prop:"createTime",width:180}]});function i(){var e,a,s;try{(a=(e=t==null?void 0:t.value)==null?void 0:e.params)!=null&&a.page&&(t.value.params.page=1),m.value=!0,(s=t==null?void 0:t.value)==null||s.refresh()}catch(c){console.error(c)}finally{m.value=!1}}function v(){var e,a;l.value.keyWord="",l.value.date=[],(e=t==null?void 0:t.value)!=null&&e.params&&(t.value.params.page=1,t.value.params.size=20),(a=t==null?void 0:t.value)==null||a.refresh()}function W(e){e?i():v()}async function q(){_.value=!0;try{const e=g({page:1,size:1e5}),a=await P.pms.finance.request({url:"/purchaseOrderExportExcel",method:"GET",responseType:"blob",params:e});R(a)&&G.success("导出成功")}catch(e){console.error(e)}finally{_.value=!1}}return(e,a)=>{const s=r("el-button"),c=r("cl-flex1"),b=r("el-option"),y=r("el-select"),B=r("el-date-picker"),D=r("el-input"),h=r("el-row"),M=r("el-tag"),N=r("cl-table"),C=r("cl-pagination"),K=r("cl-crud");return V(),Q(K,{ref_key:"Crud",ref:t},{default:u(()=>[o(h,null,{default:u(()=>[o(s,{onClick:v},{default:u(()=>[d(" 刷新 ")]),_:1}),o(s,{type:"success",icon:"Download",loading:p(_),onClick:q},{default:u(()=>[d(" 导出excel ")]),_:1},8,["loading"]),o(c),k("div",null,[o(y,{modelValue:p(l).status,"onUpdate:modelValue":a[0]||(a[0]=n=>p(l).status=n),placeholder:"请选择状态",clearable:"",class:"mr-20px w-200px",onChange:i},{default:u(()=>[(V(),U(j,null,z(L,n=>o(b,{key:n.value,label:n.label,value:n.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),k("div",A,[o(B,{modelValue:p(l).date,"onUpdate:modelValue":a[1]||(a[1]=n=>p(l).date=n),type:"daterange",clearable:"","range-separator":"~","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:W},null,8,["modelValue"])]),o(D,{modelValue:p(l).keyWord,"onUpdate:modelValue":a[2]||(a[2]=n=>p(l).keyWord=n),placeholder:"请输入产品名称或SKU或生产单号",style:{width:"500px"},clearable:"",onClear:v,onKeyup:F(i,["enter"])},null,8,["modelValue"]),o(s,{type:"primary",mx:"10px",loading:p(m),onClick:i},{default:u(()=>[d(" 搜索 ")]),_:1},8,["loading"])]),_:1}),o(h,null,{default:u(()=>[o(N,{ref_key:"Table",ref:S},{"column-orderStatusLabel":u(({scope:n})=>[o(M,{effect:"dark",type:O[n.row.orderStatus]},{default:u(()=>[d($(n.row.orderStatusLabel),1)]),_:2},1032,["type"])]),_:1},512)]),_:1}),o(h,null,{default:u(()=>[o(c),o(C)]),_:1})]),_:1},512)}}});export{X as _};
