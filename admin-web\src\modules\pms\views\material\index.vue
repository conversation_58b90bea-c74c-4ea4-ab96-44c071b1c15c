<script lang="ts" name="pms-material" setup>
import { useCrud, useSearch, useTable, useUpsert } from '@cool-vue-p/crud'
import type { InputInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import { computed, ref, watchEffect } from 'vue'
import * as XLSX from 'xlsx'
import { useMaterial } from '../../hooks/material'
import type { Material } from '../../types'
import { numberToFixed } from '../../utils'
import { checkPerm } from '/$/base'
import { useDict } from '/$/dict'

import MaterialDrawing from '/$/pms/components/material-drawing.vue'
import MaterialPrice from '/$/pms/components/material-price.vue'
import { useCool } from '/@/cool'
import { downloadBlob } from '/@/cool/utils'

const { service } = useCool()

const closeTagLoad = ref(false)
const setMaterialLoad = ref(false)
const isShowManageDrawing = ref(false)
const isShowManagePrice = ref(false)
const addressList = ref<any[]>([])
const addressTags = ref<any[]>([])
const addressInput = ref<string[]>([])
const { materialUpsertOptions } = useMaterial()
const { dict } = useDict()
const keywordList = ref<any[]>([])

// 添加位置搜索框
const addressSearchKeyword = ref('')
// 位置分组数据
const addressGroups = computed(() => {
  // 根据位置前缀(如B11, A10等)分组
  const groups: { [key: string]: string[] } = {}

  // 如果有搜索关键词，则过滤位置
  const filteredAddressTags = addressSearchKeyword.value
    ? addressTags.value.filter(tag => tag.toLowerCase().includes(addressSearchKeyword.value.toLowerCase()))
    : addressTags.value

  filteredAddressTags.forEach((tag) => {
    // 提取位置前缀，例如"B11-1"的前缀是"B11"
    const prefix = tag.split('-')[0]
    if (!groups[prefix]) {
      groups[prefix] = []
    }
    groups[prefix].push(tag)
  })

  // 按照前缀排序
  return Object.keys(groups).sort().map(prefix => ({
    prefix,
    tags: groups[prefix].sort(),
  }))
})

// 管理展开的分组
const expandedGroups = ref<string[]>([])

// 切换分组展开状态
function toggleGroup(prefix: string) {
  const index = expandedGroups.value.indexOf(prefix)
  if (index > -1) {
    expandedGroups.value.splice(index, 1)
  }
  else {
    expandedGroups.value.push(prefix)
  }
}

// 是否显示分组内容
function isGroupExpanded(prefix: string) {
  return expandedGroups.value.includes(prefix)
}

watchEffect(() => {
  keywordList.value = dict.get('inbound_outbound_key').value || []
  if (!keywordList.value?.find(item => item.value === 0)) {
    keywordList.value?.unshift({ label: '无', value: 0 })
  }
})

// cl-upsert
const Upsert = useUpsert(materialUpsertOptions)

// 重新获取物料地址列表
materialUpsertOptions.onClosed = () => {
  getMaterialAddress()
}

materialUpsertOptions.items?.push(
  {
    prop: 'inbound_outbound_key',
    label: '关键字',
    required: false,
    component: {
      name: 'el-select',
      props: {
        clearable: true,
      },
      options: keywordList,
    },
  },
)
materialUpsertOptions.items?.push({
  label: '添加位置',
  required: false,
  component: { name: 'slot-add-address' },

})
materialUpsertOptions.items?.push({
  prop: 'address_name',
  label: '输入位置',
  required: false,
  props: [],
  component: { name: 'slot-address' },
})

// 查询对象
materialUpsertOptions.onInfo = async (data, { done }) => {
  addressInput.value = data.address_name !== '' ? data.address_name.split(',') : []
  done(data)
}

// 打开表单
materialUpsertOptions.onOpen = async () => {
  // 新增
  if (Upsert.value?.mode === 'add') {
    addressInput.value = []
  }
  closeTagLoad.value = false
}

// cl-table
const Table = useTable({
  columns: [
    { prop: 'id', label: 'ID', width: 100, sortable: true },
    {
      prop: 'code',
      label: '物料编码',
      align: 'left',
      width: 150,
      showOverflowTooltip: true,
    },
    {
      prop: 'name',
      label: '名称',
      align: 'left',
      width: 180,
      showOverflowTooltip: true,
    },
    {
      prop: 'model',
      label: '型号',
      align: 'left',
      minWidth: 180,
      showOverflowTooltip: true,
    },
    {
      prop: 'size',
      label: '尺寸',
      align: 'left',
      width: 130,
      showOverflowTooltip: true,
    },
    {
      prop: 'material',
      label: '材质',
      align: 'left',
      width: 130,
      showOverflowTooltip: true,
    },
    {
      prop: 'process',
      label: '工艺',
      align: 'left',
      width: 130,
      showOverflowTooltip: true,
    },
    {
      prop: 'coverColor',
      label: '颜色',
      align: 'left',
      showOverflowTooltip: true,
    },
    { prop: 'unit', label: '单位', align: 'left', width: 100 },
    { label: '关键字', prop: 'inbound_outbound_key', dict: keywordList },
    { prop: 'address_name', label: '位置', align: 'left', width: 180 },
    { prop: 'inventory', label: '库存', sortable: true, width: 130 },
    { prop: 'expectedInbound', label: '在途', sortable: true, width: 130 },
    // { prop: 'lockedInventory', label: '锁定', sortable: true, width: 100 },
    // { prop: 'deductibleExpectedInbound', label: '可用在途', sortable: true, width: 120 },
    // { prop: 'usedExpectedInbound', label: '不可用在途', sortable: true, width: 120 },
    // { prop: 'availableInventory', label: '可用', width: 100 },
    // { prop: 'occupiedInventory', label: '订单占用', sortable: true, width: 120 },
    { prop: 'createTime', label: '创建时间', sortable: true, width: 160 },
    {
      type: 'op',
      hidden: !checkPerm({
        or: [
          service.pms.material.drawing.permission.page,
          service.pms.material.price.permission.page,
          service.pms.material.permission.update,
          service.pms.material.permission.delete,
        ],
      }),
      // 根据权限获取宽度
      width: (() => {
        let width = 0

        if (checkPerm(service.pms.material.drawing.permission.page))
          width += 120

        if (checkPerm(service.pms.material.price.permission.page))
          width += 110

        if (checkPerm(service.pms.material.permission.update))
          width += 70

        if (checkPerm(service.pms.material.permission.delete))
          width += 70

        return width
      })(),
      buttons: ['edit', 'delete', 'slot-btn-drawing', 'slot-btn-price'],
    },
  ],
})
// cl-crud
const Crud = useCrud(
  {
    service: service.pms.material,
  },
  (app) => {
    app.refresh( status )
  },
)

const Search = useSearch({
  items: [
    // 未使用物料
    {
      label: '仅显示未使用物料',
      prop: 'isShowUnused',
      props: {
        labelWidth: '150px',
      },
      component: {
        name: 'el-switch',
        props: {
          activeText: '是',
          inactiveText: '否',
          onChange(isShowUnused: boolean) {
            Crud.value?.refresh({ isShowUnused, page: 1 })
          },
        },
      },
    },
    {
      label: '编码/名称/型号',
      prop: 'keyWord',
      props: {
        labelWidth: '120px',
      },
      component: {
        name: 'el-input',
        props: {
          clearable: false,
          onChange(keyWord: string) {
            Crud.value?.refresh({ keyWord: keyWord.trim(), page: 1 })
          },
        },
      },
    },
  ],
})

// 图纸管理
const currentMaterial = ref<Material.Item>()

function handleManageDrawing(row: any) {
  currentMaterial.value = row
  isShowManageDrawing.value = true
}

// 价格管理
const currentMaterialOfPrice = ref<Material.Item>()

function handleManagePrice(row: any) {
  currentMaterialOfPrice.value = row
  isShowManagePrice.value = true
}

function getAvailableInventory(row: Material.Item) {
  return numberToFixed(row.inventory - row.lockedInventory + row.deductibleExpectedInbound - row.usedExpectedInbound)
}

// 导出
const isExportLoading = ref(false)

function handleExport() {
  isExportLoading.value = true
  // 请求export接口，返回blob文件
  const params = {
    url: '/export',
    method: 'GET',
    responseType: 'blob',
  }

  service.pms.material
    .request(params)
    .then((res: any) => {
      // 今天日期
      const today = moment().format('YY-MM-DD')
      const fileName = `物料列表-${today}.xlsx`

      if (downloadBlob(res, fileName))
        ElMessage.success('导出成功')
    })
    .catch((err: any) => {
      // 提示错误消息
      ElMessage.error(err.message || '导出失败')
    }).finally(() => {
      isExportLoading.value = false
    })
}

const fileInputRef = ref<HTMLInputElement | null>(null)
const fileInputAddressRef = ref<HTMLInputElement | null>(null)
const isLoading = ref(false)
const isAddressLoading = ref(false)
// 定义遍历记录有问题的行
const disabled = ref(false)

function openFileInput() {
  const fileInput = fileInputRef.value
  if (fileInput)
    fileInput.click()
}

function openAddressFileInput() {
  const fileInput = fileInputAddressRef.value
  if (fileInput)
    fileInput.click()
}

function clearInput(fileInput: { value: string }) {
  // 清空文件输入的值
  if (fileInput)
    fileInput.value = ''
}

//
async function handleAddressInputChange(event: Event) {
  const codeMap: any = {}
  const fileInput = event.target as HTMLInputElement
  const files = fileInput.files

  if (files && files.length > 0) {
    isAddressLoading.value = true
    const file = files[0]
    const reader = new FileReader()

    reader.onload = (e: ProgressEvent<FileReader>) => {
      const data = new Uint8Array(e.target?.result as ArrayBuffer)
      const workbook = XLSX.read(data, { type: 'array' })
      // 这里可以根据需要处理读取到的Excel数据
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      // 定义非法值
      const illegalValue = [undefined, null, '', 'undefined', 'null', 'NaN']
      // 定义列
      const columns: string[] = ['code', 'address']
      const result: any = []
      if (jsonData && jsonData.length > 0) {
        for (let i = 1; i < jsonData.length; i++) {
          const row = jsonData[i] as string[]
          const cell: any = {}
          for (let j = 0; j < row.length; j++) {
            const columnName = columns[j]
            cell[columnName] = (row[j] || '').trim()
          }

          // 校验物料编码是否重复
          const val: boolean = codeMap[cell.code]
          if (val) {
            ElMessage.error(`发现重复物料,物料编码:${cell.code}`)
            // 清空文件输入的值
            clearInput(fileInput)
            isLoading.value = false
            return
          }
          else {
            codeMap[cell.code] = true
          }
          result.push(cell)
        }
        // 校验物料编码是否重复
        if (result.length > 0) {
          service.pms.material.importMaterialAddressData({ address_data: result }).then(() => {
            Crud.value?.refresh()
            ElMessage.success('导入成功')
            getMaterialAddress()
          }).catch((e: any) => {
            ElMessage.error(e.message || '导入失败')
          }).finally(() => {
            isAddressLoading.value = false
          }).finally(() => {
            isAddressLoading.value = false
          })
        }
        else {
          isAddressLoading.value = false
          ElMessage.error('导入数据为空')
        }
        clearInput(fileInput)
      }
    }
    reader.readAsArrayBuffer(file)
  }
  else {
    isAddressLoading.value = false
    ElMessage.error('请选择文件')
  }
}

async function handleFileInputChange(event: Event) {
  const materialCodeList = []
  const codeMap: any = {}
  const materialList: any = await service.pms.material.list()
  if (materialList && materialList.length > 0) {
    materialList.forEach((item: any) => {
      codeMap[item.code] = { code: item.code, data: item, src: 'db' }
    })
  }
  const fileInput = event.target as HTMLInputElement
  const files = fileInput.files

  if (files && files.length > 0) {
    isLoading.value = true
    const file = files[0]
    const reader = new FileReader()

    reader.onload = (e: ProgressEvent<FileReader>) => {
      const data = new Uint8Array(e.target?.result as ArrayBuffer)
      const workbook = XLSX.read(data, { type: 'array' })

      // 这里可以根据需要处理读取到的Excel数据
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      // 定义非法值
      const illegalValue = [undefined, null, '', 'undefined', 'null', 'NaN']
      // 定义列
      const columns: string[] = ['', 'code', 'name', 'model', 'size', 'material', 'process', 'coverColor', 'unit']
      const result: any = []
      if (jsonData && jsonData.length > 0) {
        for (let i = 1; i < jsonData.length; i++) {
          const row = jsonData[i] as string[]
          const cell: any = {}
          for (let j = 1; j < row.length; j++) {
            const columnName = columns[j]
            cell[columnName] = (row[j] || '').trim()
          }
          if (illegalValue.includes(cell.code)) {
            ElMessage.error(`第${i}行物料编码不能为空`)
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          if (illegalValue.includes(cell.name)) {
            ElMessage.error(`第${i}行名称不能为空`)
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          // 校验物料编码是否重复
          const val: any = codeMap[cell.code]
          if (val && val.code && !illegalValue.includes(val) && Number.isNaN(val)) {
            if (val.src && val.src === 'db')
              ElMessage.error(`第${i}行物料编码${val.code}已经存在`)

            else
              ElMessage.error(`第${i}行物料编码${val.code}与第${val.rowNo}物料编号${val.code}重复`)

            // 清空文件输入的值
            clearInput(fileInput)
            isLoading.value = false
            break
          }
          else {
            codeMap[cell.code] = { code: cell.code, rowNo: i, data: cell }
          }
          materialCodeList.push(cell.code)
          result.push(cell)
        }
        // 校验物料编码是否重复
        if (result.length > 0) {
          service.pms.material.importExcel({ materialList: result }).then(() => {
            Crud.value?.refresh()
            ElMessage.success('导入成功')
          }).catch((e: any) => {
            ElMessage.error(e.message || '导入失败')
          }).finally(() => {
            isLoading.value = false
          }).finally(() => {
            isLoading.value = false
          })
        }
        else {
          isLoading.value = false
          ElMessage.error('导入数据为空')
        }
        clearInput(fileInput)
      }
      // console.log(result,"导入数据")
    }

    reader.readAsArrayBuffer(file)
  }
  else {
    isLoading.value = false
    ElMessage.error('请选择文件')
  }
}

function downloadExcelTemplate() {
  const fileName = '导入物料模板.xlsx'
  const filePath = '/material_template.xlsx'

  // 发起下载请求
  fetch(filePath)
    .then(response => response.blob())
    .then((blob) => {
      // 保存文件
      downloadBlob(blob, fileName)
    })
    .catch(() => {
      ElMessage.error({
        message: '下载模板文件失败',
      })
    })
}

function downloadImportExcelTemplate() {
  const fileName = '导入物料位置模板.xlsx'
  const filePath = '/material_import_address_template.xlsx'

  // 发起下载请求
  fetch(filePath)
    .then(response => response.blob())
    .then((blob) => {
      // 保存文件
      downloadBlob(blob, fileName)
    })
    .catch(() => {
      ElMessage.error({
        message: '下载模板文件失败',
      })
    })
}

// 获取物料地址列表
async function getMaterialAddress() {
  try {
    addressList.value = await service.pms.material.request({
      url: '/getMaterialAddress',
      method: 'GET',
    })
    addressTags.value = addressList.value.map((item: any) => item.address)

    // 默认展开包含已选位置的分组
    if (addressInput.value && addressInput.value.length) {
      const prefixes = new Set<string>()
      addressInput.value.forEach((address: string) => {
        const prefix = address.split('-')[0]
        prefixes.add(prefix)
      })
      expandedGroups.value = Array.from(prefixes)
    }
  }
  catch (e: any) {
    console.error(e)
  }
}

getMaterialAddress()

const inputValue = ref('')
const inputVisible = ref(false)
const InputRef = ref<InputInstance>()

function showInput() {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value!.input!.focus()
  })
}

async function handleInputConfirm() {
  if (inputValue.value && !addressTags.value.includes(inputValue.value)) {
    addressTags.value.push(inputValue.value)
    await addMaterialAddress(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 添加物料地址
async function addMaterialAddress(address_name: string) {
  service.pms.material.addMaterialAddress({ address_name }).then((_res) => {
    // 成功添加
  }).catch((e: any) => {
    ElMessage.error(e.message || '添加物料地址失败')
  }).finally(() => {
    getMaterialAddress()
  })
}

// 多选地址
function changeAddress(value: string[]) {
  Upsert.value?.setForm('address_name', value.join(','))
}

// 删除标签
async function handleClose(tag: string) {
  ElMessageBox.confirm('确定删除该位置吗？删除后请刷新页面！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    closeTagLoad.value = true
    service.pms.material.deleteMaterialAddress({ address_name: tag }).then((_res) => {
      Crud.value?.refresh()
      if (addressInput.value) {
        addressInput.value = addressInput.value.filter((row: string) => {
          return row !== tag
        })
        Upsert.value?.setForm('address_name', addressInput.value.join(','))
      }
    }).catch((e: any) => {
      ElMessage.error(e.message || '删除物料地址失败')
    }).finally(() => {
      getMaterialAddress()
      closeTagLoad.value = false
    })
  })
}

// 设置物料等级
function setMaterialLevel() {
  ElMessageBox.confirm('确定更新所有物料等级吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    setMaterialLoad.value = true
    service.pms.material.setMaterialLevel().then((_res) => {
      ElMessage.success("更新物料等级成功")
      Crud.value?.refresh()
    }).catch((e: any) => {
      ElMessage.error(e.message || '删除物料等级失败')
    }).finally(() => {
      setMaterialLoad.value = false
    })
  })
}
</script>

<template>
  <cl-crud ref="Crud">
    <cl-row>
      <!-- 刷新按钮 -->
      <cl-refresh-btn />
      <!-- 新增按钮 -->
      <cl-add-btn v-permission="service.pms.material.permission.add" />
      <!-- 物料导出 -->
      <el-button
        v-permission="service.pms.material.permission.export" :loading="isExportLoading" type="success"
        @click="handleExport"
      >
        导出
      </el-button>

      <input ref="fileInputRef" type="file" style="display: none" accept=".xlsx, .xls" @change="handleFileInputChange">
      <el-button
        :disabled="disabled" size="default" :loading="isLoading" type="success" class="mb-10px mr-10px"
        @click="openFileInput"
      >
        Excel导入
      </el-button>
      <!-- 下载excel模板 -->
      <el-button class="mb-10px mr-10px" size="default" @click="downloadExcelTemplate">
        下载Excel模板
      </el-button>
      <!-- 下载excel模板 -->
      <el-button class="mb-10px mr-10px" size="default" type="info" @click="downloadImportExcelTemplate">
        下载位置导入模板
      </el-button>
      <input ref="fileInputAddressRef" type="file" style="display: none" accept=".xlsx, .xls" @change="handleAddressInputChange">
      <el-button
        :disabled="disabled" size="default" :loading="isAddressLoading" type="warning" class="mb-10px mr-10px"
        @click="openAddressFileInput"
      >
        导入物料位置
      </el-button>
      <el-button class="mb-10px mr-10px" size="default" type="danger" :loading="setMaterialLoad" @click="setMaterialLevel">
        更新物料等级
      </el-button>
      <!-- 删除按钮 -->
      <cl-flex1 />
      <cl-search ref="Search" />
    </cl-row>

    <cl-row>
      <!-- 数据表格 -->
      <cl-table ref="Table">
        <!-- 可用在途 -->
        <template #column-deductibleExpectedInbound="{ scope }">
          {{ numberToFixed(Math.max(scope.row.deductibleExpectedInbound - scope.row.usedExpectedInbound, 0)) }}
        </template>

        <template #column-availableInventory="{ scope }">
          {{ getAvailableInventory(scope.row) }}
        </template>

        <template #slot-btn-drawing="{ scope }">
          <el-button
            v-permission="service.pms.material.drawing.permission.page" type="warning" text
            @click="handleManageDrawing(scope.row)"
          >
            图纸管理
          </el-button>
        </template>

        <template #slot-btn-price="{ scope }">
          <el-button
            v-permission="service.pms.material.price.permission.page" type="success" text
            @click="handleManagePrice(scope.row)"
          >
            价格管理
          </el-button>
        </template>
      </cl-table>
    </cl-row>

    <cl-row>
      <cl-flex1 />
      <!-- 分页控件 -->
      <cl-pagination />
    </cl-row>

    <!-- 新增、编辑 -->
    <cl-upsert ref="Upsert">
      <template #slot-add-address="">
        <div v-loading="closeTagLoad" class="position-container">
          <!-- 搜索位置 -->
          <div class="position-search mb-2">
            <el-input
              v-model="addressSearchKeyword"
              placeholder="搜索位置编号"
              prefix-icon="el-icon-search"
              clearable
            />
          </div>

          <!-- 位置分组展示 -->
          <div class="position-groups" style="max-height: 400px; overflow-y: auto;">
            <div v-if="addressGroups.length === 0" class="p-4 text-center text-gray-500">
              没有找到相关位置
            </div>

            <el-collapse v-model="expandedGroups" accordion>
              <el-collapse-item
                v-for="group in addressGroups"
                :key="group.prefix"
                :name="group.prefix"
              >
                <template #title>
                  <span class="font-bold">{{ group.prefix }}</span>
                  <span class="ml-2 text-gray-500">({{ group.tags.length }})</span>
                </template>

                <div class="flex flex-wrap gap-1 p-1">
                  <el-tag
                    v-for="tag in group.tags"
                    :key="tag"
                    closable
                    :disable-transitions="false"
                    style="margin: 2px;"
                    @close="handleClose(tag)"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>

          <!-- 添加地址输入框 -->
          <div class="mt-3">
            <el-input
              v-if="inputVisible"
              ref="InputRef"
              v-model="inputValue"
              class="w-60"
              size="small"
              placeholder="输入新位置编号"
              @keyup.enter="handleInputConfirm"
              @blur="handleInputConfirm"
            />
            <el-button v-else class="button-new-tag" size="small" @click="showInput">
              添加新位置
            </el-button>
          </div>
        </div>
      </template>

      <template #slot-address="">
        <div flex="~ items-center">
          <el-select
            v-model="addressInput"
            filterable
            multiple
            placeholder="请选择位置"
            style="width: 100%;"
            @change="changeAddress"
          >
            <el-option-group
              v-for="group in addressGroups"
              :key="group.prefix"
              :label="group.prefix"
            >
              <el-option
                v-for="tag in group.tags"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-option-group>
          </el-select>
        </div>
      </template>

      <template #slot-bind-user="{ scope }">
        <div flex="~ items-center">
          <el-switch
            v-model="scope.isBindUser"
            :active-text="scope?.bindUserId > 0 ? '更换绑定' : '是'"
            inactive-text="否"
            active-color="#13ce66"
          />
        </div>
      </template>
    </cl-upsert>

    <!-- 图纸管理弹窗 -->
    <el-dialog
      v-model="isShowManageDrawing"
      :title="`图纸管理 - ${currentMaterial?.code}`"
      width="60%"
      height="400"
      :close-on-click-modal="false"
    >
      <MaterialDrawing
        :material="currentMaterial!"
      />
    </el-dialog>

    <!-- 价格管理弹窗 -->
    <el-dialog
      v-model="isShowManagePrice"
      :title="`价格管理 - ${currentMaterialOfPrice?.code}`"
      width="60%"
      :close-on-click-modal="false"
    >
      <MaterialPrice
        :material="currentMaterialOfPrice!"
      />
    </el-dialog>
  </cl-crud>
</template>

<style scoped>
.position-container {
  width: 100%;
}

.position-search {
  margin-bottom: 10px;
}

.position-groups {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}
</style>
