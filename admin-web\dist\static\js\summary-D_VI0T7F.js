import{c as me,b as s,Z as u,z as Ae,E as I,h as a,i as c,ac as oe,A as ue,j as y,q as re,w as M,y as se,G as ce,H as ie,v as Ie,t as Be,W as Te,ad as qe,o as de}from"./.pnpm-Kv7TmmH8.js";import{g as Re,s as H,j as fe,e as He}from"./index-BuqCFB-b.js";/* empty css              */import{_ as Le}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ge={class:"production-summary"},Ke={class:"production-summary-title"},Ne=me({name:"undefined"}),Ue=me({...Ne,setup(We){const{dict:ve}=Re(),L=ve.get("color"),d=s("month"),Y=s(u().format("YYYY-MM")),B=s(Y),G=s([]),x=s([]),_=s([]),T=s(""),q=s([]),K=s([]),N=s([]),R=s(!0),v=s([]),U=s([]),k=s(["inbound","outbound"]),$=s(!1),S=s(!1),O=s(!1),h=s([]),W=s([]),z=s([]);Z(),j(),E(),Ae(k,(l,e)=>{if(l.length===0){const t=e[0]==="inbound"?"outbound":"inbound";k.value=[t]}});async function E(){if(!B.value)return;const l=u(B.value).year(),e=d.value==="year"?null:u(B.value).month()+1;H.pms.material.inbound.summary({year:l,month:e}).then(async t=>{G.value=t,Z(),j(),De(),R.value=!1}).catch(t=>{I.error((t==null?void 0:t.message)||"读取数据失败，请重试!"),R.value=!1})}function j(){const l=[{title:"物料名称",width:130,key:"name",dataKey:"name",align:"center",cellRenderer:e=>a(c("el-tooltip"),{class:"box-item",effect:"dark",content:e.cellData||"-"},{default:()=>[a("div",{class:e.class},[e.cellData||"-"])]})},{title:"物料编码",width:130,key:"code",dataKey:"code",align:"center",headerCellRenderer:e=>ke(e)},{title:"单位",width:60,key:"unit",dataKey:"unit",align:"center"},{title:"合计",width:120,key:"total",dataKey:"total",align:"center",headerCellRenderer:e=>P(e),cellRenderer:e=>{var g,D,m,C,F,V;const t=((g=e.rowData)==null?void 0:g["total-inbound"])||0,n=((D=e.rowData)==null?void 0:D["total-outbound"])||0,r="total",o=z.value.find(p=>p[r]),i=(m=o==null?void 0:o[r])==null?void 0:m.inbound,b=(C=o==null?void 0:o[r])==null?void 0:C.outbound,f=(F=k.value)==null?void 0:F.includes("inbound"),w=(V=k.value)==null?void 0:V.includes("outbound");return t===0&&n===0?a("div",{class:e.class},null):a("div",{class:`${e.class} data-inbound-outbound`},[a("p",{class:"date-inbound",style:{display:f&&!i?"block":"none"}},[y("入："),t]),a("p",{class:"date-outbound",style:{display:w&&!b?"block":"none"}},[y("出："),n])])}}];q.value=l.concat(N.value),K.value=q.value.map((e,t)=>{let n;return window.innerWidth<600?n=void 0:t<4&&(n=qe.LEFT),{...e,fixed:n,width:e.width}})}function he(l,e){const t=u(e,"YYYY-MM"),n=[];if(l==="year")for(let r=0;r<12;r++)n.push({year:t.year(),month:r+1,day:0});else if(l==="month"){const r=t.daysInMonth();for(let o=1;o<=r;o++)n.push({year:t.year(),month:t.month()+1,day:o})}return n}function A(){const l=[];_.value=x.value,v.value=v.value.filter(e=>{const{key:t}=e;return q.value.some(n=>n.key===t)}),v.value.length===0&&!$.value?_.value=x.value:(_.value.forEach(e=>{h.value.length>0&&!h.value.includes(e.code)||v.value.every(t=>{const{key:n,filterValue:r}=t,o=e[n];return!r.includes(o)})&&l.push(e)}),_.value=l)}function be(l,e,t){const n=[void 0,0],r=e.key;l?v.value.push({key:r,filterValue:n}):v.value=v.value.filter(o=>o.key!==r),A()}function P(l){const e=s(!1);return e.value=v.value.some(t=>t.key===l.column.key),a("div",{class:`header-cell-with-filter ${e.value?"header-cell-fileted":""}`},[a("div",{class:"header-cell-filter"},[a("p",{class:"header-cell-with-filter-title"},[l.column.title]),a(c("el-popover"),{placement:"bottom",trigger:"click",align:"center"},{default:()=>[a("div",{class:"date-header-cell-filter-popover"},[a(c("el-checkbox"),{modelValue:e.value,"onUpdate:modelValue":t=>e.value=t,label:"过滤空白和0",name:"filter",onChange:t=>be(t,l.column)},null)])],reference:()=>a(c("el-icon"),{class:"header-cell-with-filter-icon"},{default:()=>[a(oe,null,null)]})})])])}function pe(){O.value?h.value=x.value.map(l=>l.code):h.value=[]}ue(()=>{W.value=x.value.map(l=>{var e,t;return{value:(e=l.code)==null?void 0:e.trim(),label:(t=l.code)==null?void 0:t.trim()}}),O.value&&(h.value=x.value.map(l=>l.sku)),$.value=h.value.length>0});function ye(){S.value=!1,A()}function Ye(){S.value=!1}function _e(){S.value=!0}function ke(l){return a("div",{class:`header-cell-with-filter ${$.value?"header-cell-fileted":""}`},[a("div",{class:"header-cell-filter"},[a("p",{class:"header-cell-with-filter-title"},[l.column.title]),a(c("el-popover"),{align:"center",placement:"bottom",width:"230",visible:S.value},{default:()=>[a("div",{class:"date-header-cell-filter-popover sku-filter"},[a(c("el-select"),{placeholder:"请筛选物料",modelValue:h.value,"onUpdate:modelValue":e=>h.value=e,teleported:!1,multiple:!0,clearable:!0,filterable:!0,style:"width: 200px;","collapse-tags":!0},{default:()=>[a(c("el-checkbox"),{style:"display: flex; justify-content: flex-end; margin-right: 10px;",modelValue:O.value,"onUpdate:modelValue":e=>O.value=e,onChange:pe},{default:()=>[O.value?"反选":"全选"]}),W.value.map(e=>a(c("el-option"),{key:e.value,label:e.label,value:e.value},null))]})]),a("div",{class:"sku-filter-actions",style:"margin-top: 10px; display: flex; justify-content: flex-end"},[a(c("el-button"),{onClick:Ye},{default:()=>[y("取消")]}),a(c("el-button"),{type:"primary",onClick:ye},{default:()=>[y("确定")]})])],reference:()=>a(c("el-icon"),{class:"header-cell-with-filter-icon",onClick:_e},{default:()=>[a(oe,null,null)]})})])])}function Z(){U.value=he(d.value,Y.value);const l=[];return U.value.forEach(e=>{const t=e.year,n=e.month<10?`0${e.month}`:e.month,r=e.day<10?`0${e.day}`:e.day,o=d.value==="year"?`date-${t}${n}`:`date-${t}${n}${r}`,i=d.value==="year"?`${e.month}月`:`${e.month}月${e.day}日`;if(d.value==="month"&&u(`${t}-${n}`).isSame(u(),"month")){if(e.day>u().date())return}else if(d.value==="year"&&t===u().year()&&e.month>u().month()+1)return;const b={title:i,key:o,dataKey:o,width:100,align:"center",headerClass:"date-header-cell",headerCellRenderer:f=>P(f),cellRenderer:f=>{var X,ee,te,le,ae,ne;const w=((X=f.rowData)==null?void 0:X[`${o}-inbound`])||0,g=((ee=f.rowData)==null?void 0:ee[`${o}-outbound`])||0,D=o.replace("date-",""),m=z.value.find(Ee=>Ee[D]),C=(te=m==null?void 0:m[D])==null?void 0:te.inbound,F=(le=m==null?void 0:m[D])==null?void 0:le.outbound,V=(ae=k.value)==null?void 0:ae.includes("inbound"),p=(ne=k.value)==null?void 0:ne.includes("outbound");return w===0&&g===0?a("div",{class:f.class},null):a("div",{class:`${f.class} data-inbound-outbound`},[a("p",{class:"date-inbound",style:{display:V&&!C?"block":"none"}},[y("入："),w]),a("p",{class:"date-outbound",style:{display:p&&!F?"block":"none"}},[y("出："),g])])}};l.push(b)}),N.value=l,l}function De(){const l=G.value;_.value=[];let e=0;l.forEach(t=>{var o;const n=fe(L.value,Number.parseInt(t.color)).split("/")[0];n?t.color=n:t.color="-",(o=t.children)==null||o.forEach(i=>{const b=fe(L.value,Number.parseInt(i.color)).split("/")[0];b?i.color=b:i.color="-"});const r={...t,index:e};e++,_.value.push(r)}),x.value=_.value,A()}function ge({column:l,columnIndex:e}){const t=`hovering-col-${e}`;return{"data-key":t,onMouseenter:()=>{T.value=t},onMouseleave:()=>{T.value=""},onContextmenu:()=>{l.key.startsWith("date-")}}}function Me(){return{}}function xe(){E()}function we(){xe()}function Ce(){v.value=[],h.value=[],$.value=!1,A()}function Fe(l){if(E(),l==="month"&&u(Y.value,"YYYY-MM").month()===0){const e=u(Y.value,"YYYY-MM").year();Y.value=`${e}-01`}}function Ve(){E()}function $e(l){return l.getTime()>Date.now()}const J=s();function Oe(){var l;(l=J.value)==null||l.open({title:"物料出入库记录导出",width:"500px",dialog:{controls:["close"]},props:{labelWidth:"120px"},items:[{label:"请选择时间周期",prop:"range",required:!0,component:{name:"el-date-picker",props:{type:"daterange",placeholder:"请选择时间周期",shortcuts:[{text:"上月",value:()=>{const e=u().subtract(1,"month").startOf("month").format("YYYY-MM-DD"),t=u().subtract(1,"month").endOf("month").format("YYYY-MM-DD");return[e,t]}},{text:"本月",value:()=>{const e=u().startOf("month").format("YYYY-MM-DD"),t=u().endOf("month").format("YYYY-MM-DD");return[e,t]}},{text:"上季度",value:()=>{const e=u().subtract(1,"quarter").startOf("quarter").format("YYYY-MM-DD"),t=u().subtract(1,"quarter").endOf("quarter").format("YYYY-MM-DD");return[e,t]}},{text:"本季度",value:()=>{const e=u().startOf("quarter").format("YYYY-MM-DD"),t=u().endOf("quarter").format("YYYY-MM-DD");return[e,t]}},{text:"去年",value:()=>{const e=u().subtract(1,"year").startOf("year").format("YYYY-MM-DD"),t=u().subtract(1,"year").endOf("year").format("YYYY-MM-DD");return[e,t]}},{text:"本年",value:()=>{const e=u().startOf("year").format("YYYY-MM-DD"),t=u().endOf("year").format("YYYY-MM-DD");return[e,t]}}],clearable:!0,format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":e=>e.getTime()>Date.now()}}}],on:{submit:async(e,{done:t,close:n})=>{const[r,o]=e.range;if(!r||!o){I.error("请选择时间周期");return}Se(r,o).then(i=>{const f=`出入库记录-${u().format("YY-MM-DD")+Math.floor(Math.random()*1e3)}.xlsx`;He(i,f)&&I.success("导出成功"),n()}).catch(i=>{I.error(i.message||"导出失败")}).finally(()=>{t()})}}})}async function Se(l,e){const t={url:"/summaryExport",method:"GET",responseType:"blob",params:{start:l,end:e}};return await H.pms.material.request(t)}const Q=s();return ue(()=>{const l=Y.value,e=d.value==="year"?"YYYY年":"YYYY年MM月";Q.value=u(l,"YYYY-MM").format(e)}),(l,e)=>{const t=c("el-button"),n=c("el-radio-button"),r=c("el-radio-group"),o=c("el-date-picker"),i=c("el-checkbox-button"),b=c("el-checkbox-group"),f=c("el-row"),w=c("el-table-v2"),g=c("cl-form"),D=c("el-auto-resizer"),m=ie("permission"),C=ie("loading");return de(),re(D,null,{default:M(({height:F,width:V})=>[se("div",Ge,[a(f,{class:"production-summary-header"},{default:M(()=>[a(t,{class:"refresh-button",onClick:we},{default:M(()=>[y(" 刷新 ")]),_:1}),a(r,{modelValue:d.value,"onUpdate:modelValue":e[0]||(e[0]=p=>d.value=p),label:"size control",style:{"margin-left":"10px"},onChange:Fe},{default:M(()=>[a(n,{value:"year",label:"年"}),a(n,{value:"month",label:"月"})]),_:1},8,["modelValue"]),a(o,{modelValue:Y.value,"onUpdate:modelValue":e[1]||(e[1]=p=>Y.value=p),type:d.value,placeholder:"选择日期",style:{width:"150px","margin-left":"10px"},format:d.value==="year"?"YYYY":"YYYY-MM","value-format":d.value==="year"?"YYYY":"YYYY-MM","disabled-date":$e,clearable:!1,onChange:Ve},null,8,["modelValue","type","format","value-format"]),a(b,{modelValue:k.value,"onUpdate:modelValue":e[2]||(e[2]=p=>k.value=p),style:{"margin-left":"10px"}},{default:M(()=>[a(i,{key:"inbound",value:"inbound",label:"显示入库"}),a(i,{key:"outbound",value:"outbound",label:"显示出库"})]),_:1},8,["modelValue"]),a(t,{style:{"margin-left":"10px"},class:"clear-filter-button",type:"success",disabled:v.value.length===0&&!$.value,onClick:Ce},{default:M(()=>[y(" 清除过滤 ")]),_:1},8,["disabled"]),ce((de(),re(t,{style:{"margin-left":"10px"},class:"export-button",type:"warning",onClick:Oe},{default:M(()=>[y(" 导出 ")]),_:1})),[[m,Ie(H).pms.material.summaryExport]]),se("div",Ke,Be(Q.value)+"出入库记录 ",1)]),_:1}),ce(a(w,{columns:K.value,"row-key":"code",data:_.value,width:V-20,height:F-60,class:Te(`production-summary-table ${T.value}`),fixed:"","cell-props":ge,"header-cell-props":Me},null,8,["columns","data","width","height","class"]),[[C,R.value]])]),a(g,{ref_key:"InboundExportForm",ref:J},null,512)]),_:1})}}}),Je=Le(Ue,[["__scopeId","data-v-e74affe0"]]);export{Je as default};
