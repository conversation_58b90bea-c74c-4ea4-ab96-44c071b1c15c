function o(){const t=[{prop:"code",label:"物料编码",hidden:({scope:n})=>!!n.id,component:{name:"el-input",disabled:!0},required:!0},{prop:"name",label:"名称",required:!0,component:{name:"el-input"}},{prop:"model",label:"型号",required:!0,component:{name:"el-input"}},{prop:"size",label:"尺寸",component:{name:"el-input"}},{prop:"material",label:"材质",component:{name:"el-input"}},{prop:"process",label:"工艺",component:{name:"el-input"}},{prop:"coverColor",label:"颜色",component:{name:"el-input"}},{prop:"unit",label:"单位",required:!0,component:{name:"el-input"}},{prop:"isBindUser",label:"绑定当前用户",value:!0,component:{name:"slot-bind-user"}}],r={dialog:{title:"新增物料",controls:["close"],closeOnPressEscape:!1,"z-index":99999},items:t};function p(n){return t.filter(e=>e.prop!=="code"&&e.prop!=="isBindUser").map(e=>({...e,value:n[e.prop]}))}return{materialUpsertOptions:r,materialUpsertItems:t,getMaterialUpsertItemsWithMaterial:p}}export{o as u};
