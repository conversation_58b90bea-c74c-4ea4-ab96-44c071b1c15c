import{i as b,h as v}from"./index-BuqCFB-b.js";import{a as y}from"./index-BAHxID_w.js";import{c,b as r,e as V,U as x,z,S as g,i as C,f as N,o as S,h as k}from"./.pnpm-Kv7TmmH8.js";import{_ as B}from"./_plugin-vue_export-helper-DlAUqK2U.js";const E={class:"cl-menu-select"},F=c({name:"cl-menu-select"}),U=c({...F,props:{modelValue:[Number,String],type:{type:Number,default:1},placeholder:String,size:String},emits:["update:modelValue"],setup(a,{emit:d}){const o=a,u=d,{service:i}=y(),m=b.useForm(),s=r(),n=r([]),p=V(()=>v(x(n.value).filter(e=>o.type===0?e.type==0:o.type>e.type)));function f(){i.base.sys.menu.list().then(e=>{n.value=e.filter(t=>{var l;return t.id!=((l=m.value)==null?void 0:l.form.id)})})}function _(e){u("update:modelValue",e)}return z(()=>o.modelValue,e=>{s.value=e&&Number(e)},{immediate:!0}),g(()=>{f()}),(e,t)=>{const l=C("el-tree-select");return S(),N("div",E,[k(l,{modelValue:s.value,"onUpdate:modelValue":t[0]||(t[0]=h=>s.value=h),data:p.value,props:{label:"name",value:"id",disabled:"disabled",children:"children"},clearable:"","default-expand-all":"",filterable:"","check-strictly":"",size:a.size,placeholder:a.placeholder,onChange:_},null,8,["modelValue","data","size","placeholder"])])}}}),T=B(U,[["__scopeId","data-v-5cb7a739"]]);export{T as default};
