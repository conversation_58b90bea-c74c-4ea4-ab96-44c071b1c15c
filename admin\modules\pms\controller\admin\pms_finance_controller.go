package admin

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/modules/pms/service"
	"github.com/imhuso/lookah-erp/admin/yc"
)

type PmsFinanceController struct {
	*yc.Controller
}

// 查询物料入库请求
type financeMaterialInboundReq struct {
	g.Meta `path:"/materialInboundPage" method:"POST" summary:"财务查询物料入库" tags:"物料入库"`
	*model.QueryVo
}

func (c *PmsFinanceController) MaterialInboundPage(ctx context.Context, req *financeMaterialInboundReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsFinanceService().QueryMaterialInboundPage(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// 查询物料入库导出Excel
type financeMaterialInboundExportExcelReq struct {
	g.Meta `path:"/materialInboundExportExcel" method:"GET" summary:"财务查询物料入库导出Excel" tags:"物料入库"`
	*model.QueryVo
}

func (c *PmsFinanceController) MaterialInboundExportExcel(ctx context.Context, req *financeMaterialInboundExportExcelReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsFinanceService().MaterialInboundExportExcel(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil
}

// 查询物料出库请求
type financeMaterialOutboundReq struct {
	g.Meta `path:"/materialOutboundPage" method:"POST" summary:"财务查询物料出库" tags:"物料出库"`
	*model.QueryVo
}

func (c *PmsFinanceController) MaterialOutboundPage(ctx context.Context, req *financeMaterialOutboundReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsFinanceService().QueryMaterialOutboundPage(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// 查询物料出库导出Excel
type financeMaterialOutboundExportExcelReq struct {
	g.Meta `path:"/materialOutboundExportExcel" method:"POST" summary:"财务查询物料出库导出Excel" tags:"物料出库"`
	*model.QueryVo
}

func (c *PmsFinanceController) MaterialOutboundExportExcel(ctx context.Context, req *financeMaterialOutboundExportExcelReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsFinanceService().MaterialOutboundExportExcel(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil

}

// 查询成品入库请求
type financeProductInboundReq struct {
	g.Meta `path:"/productInboundPage" method:"POST" summary:"财务查询成品入库" tags:"成品入库"`
	*model.QueryVo
}

func (c *PmsFinanceController) ProductInboundPage(ctx context.Context, req *financeProductInboundReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsFinanceService().QueryProductInboundPage(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil

}

// 查询成品入库请求导出Excel
type financeProductInboundExportExcelReq struct {
	g.Meta `path:"/productInboundExportExcel" method:"GET" summary:"财务查询成品入库导出Excel" tags:"成品入库"`
	*model.QueryVo
}

func (c *PmsFinanceController) ProductInboundExportExcel(ctx context.Context, req *financeProductInboundExportExcelReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsFinanceService().ProductInboundExportExcel(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil
}

// 查询成品出库请求
type financeProductOutboundReq struct {
	g.Meta `path:"/productOutboundPage" method:"POST" summary:"财务查询成品出库" tags:"成品出库"`
	*model.QueryVo
}

func (c *PmsFinanceController) ProductOutboundPage(ctx context.Context, req *financeProductOutboundReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsFinanceService().QueryProductOutboundPage(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// 查询成品出库请求导出Excel
type financeProductOutboundExportExcelReq struct {
	g.Meta `path:"/productOutboundExportExcel" method:"GET" summary:"财务查询成品出库导出Excel" tags:"成品出库"`
	*model.QueryVo
}

func (c *PmsFinanceController) ProductOutboundExportExcel(ctx context.Context, req *financeProductOutboundExportExcelReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsFinanceService().ProductOutboundExportExcel(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil
}

// 查询采购订单请求
type financePurchaseOrderReq struct {
	g.Meta `path:"/purchaseOrderPage" method:"POST" summary:"财务查询采购订单" tags:"采购订单"`
	*model.QueryVo
}

func (c *PmsFinanceController) PurchaseOrderPage(ctx context.Context, req *financePurchaseOrderReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsFinanceService().QueryPurchaseOrderPage(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// 查询采购订单请求导出Excel
type financePurchaseOrderExportExcelReq struct {
	g.Meta `path:"/purchaseOrderExportExcel" method:"GET" summary:"财务查询采购订单导出Excel" tags:"采购订单"`
	*model.QueryVo
}

func (c *PmsFinanceController) PurchaseOrderExportExcel(ctx context.Context, req *financePurchaseOrderExportExcelReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsFinanceService().PurchaseOrderExportExcel(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil

}

// 查询生产单请求
type financeProductionOrderReq struct {
	g.Meta `path:"/productionOrderPage" method:"POST" summary:"财务查询生产单" tags:"生产单"`
	*model.QueryVo
}

func (c *PmsFinanceController) ProductionOrderPage(ctx context.Context, req *financeProductionOrderReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsFinanceService().QueryProductionOrderPage(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// 查询生产单请求导出Excel
type financeProductionOrderExportExcelReq struct {
	g.Meta `path:"/productionOrderExportExcel" method:"GET" summary:"财务查询生产单导出Excel" tags:"生产单"`
	*model.QueryVo
}

func (c *PmsFinanceController) ProductionOrderExportExcel(ctx context.Context, req *financeProductionOrderExportExcelReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsFinanceService().ProductionOrderExportExcel(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil
}

// 查询销售订单请求
type financeSalesOrderReq struct {
	g.Meta `path:"/salesOrderPage" method:"POST" summary:"财务查询销售订单" tags:"销售订单"`
	*model.QueryVo
}

func (c *PmsFinanceController) SalesOrderPage(ctx context.Context, req *financeSalesOrderReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsFinanceService().QuerySalesOrderPage(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// 查询销售订单请求导出Excel
type financeSalesOrderExportExcelReq struct {
	g.Meta `path:"/salesOrderExportExcel" method:"GET" summary:"财务查询销售订单导出Excel" tags:"销售订单"`
	*model.QueryVo
}

func (c *PmsFinanceController) SalesOrderExportExcel(ctx context.Context, req *financeSalesOrderExportExcelReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsFinanceService().SalesOrderExportExcel(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil
}

func init() {
	var pmsFinanceController = &PmsFinanceController{
		&yc.Controller{
			Prefix:  "/admin/pms/finance",
			Api:     []string{},
			Service: service.NewPmsFinanceService(),
		},
	}
	// 注册路由
	yc.RegisterController(pmsFinanceController)
}
