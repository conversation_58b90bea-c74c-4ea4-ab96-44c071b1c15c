<script lang="ts" name="pms-supplier" setup>
import { useCrud, useTable, useUpsert } from '@cool-vue-p/crud'
import { useCool } from '/@/cool'
import {ElMessage, ElMessageBox} from "element-plus";
import {downloadBlob} from "/@/cool/utils";

const { service } = useCool()

// cl-upsert
const Upsert = useUpsert({
  items: [
    {
      prop: 'name',
      label: '名称',
      required: true,
      component: { name: 'el-input' },
    },
    {
      prop: 'shortName',
      label: '简称',
      required: false,
      component: { name: 'el-input' },
    },
    {
      prop: 'mainProduct',
      label: '主要产品',
      required: false,
      component: { name: 'el-input' },
    },
    {
      prop: 'contact',
      label: '联系人',
      required: true,
      component: { name: 'el-input' },
    },
    {
      prop: 'phone',
      label: '联系电话',
      required: true,
      component: { name: 'el-input' },
    },
    {
      prop: 'fax',
      label: '传真',
      component: { name: 'el-input' },
    },
    {
      prop: 'address',
      label: '地址',
      component: { name: 'el-input', props: { type: 'textarea', rows: 3 } },
    },
  ],
})

// cl-table
const Table = useTable({
  columns: [
    { type: 'selection' },
    { prop: 'id', label: 'ID' },
    { prop: 'name', label: '名称' },
    { prop: 'mainProduct', label: '主要产品' },
    { prop: 'contact', label: '联系人' },
    { prop: 'phone', label: '联系电话' },
    { prop: 'fax', label: '传真' },
    { prop: 'address', label: '地址', showOverflowTooltip: true },
    { type: 'op', buttons: ['edit', 'delete'] },
  ],
})

// cl-crud
const Crud = useCrud(
  {
    service: service.pms.supplier,
  },
  (app) => {
    app.refresh()
  },
)
const exportLoading = ref(false)

async function exportSupplier() {
  try {
    exportLoading.value = true
    console.log(222)
    // const res = await service.pms.product.request({
    //   url: '/getAllProduct',
    //   method: 'GET',
    // })
  }
  catch (e: any) {
    console.error(e)
  }
  finally {
    exportLoading.value = false
  }
}

</script>

<template>
  <cl-crud ref="Crud">
    <cl-row>
      <!-- 刷新按钮 -->
      <cl-refresh-btn />
      <!-- 新增按钮 -->
      <cl-add-btn />
      <!-- 删除按钮 -->
      <cl-multi-delete-btn />
      <el-button
        type="success"
        mx="10px"
        :loading="exportLoading"
        @click="exportSupplier"
      >
        导出
      </el-button>
      <cl-flex1 />
      <!-- 关键字搜索 -->
      <cl-search-key />
    </cl-row>

    <cl-row>
      <!-- 数据表格 -->
      <cl-table ref="Table" />
    </cl-row>

    <cl-row>
      <cl-flex1 />
      <!-- 分页控件 -->
      <cl-pagination />
    </cl-row>

    <!-- 新增、编辑 -->
    <cl-upsert ref="Upsert" />
  </cl-crud>
</template>
