import{_ as b}from"./index.vue_vue_type_script_setup_true_name_cl-avatar_lang-DyLe_N_c.js";import{b as C,c as S}from"./index-BuqCFB-b.js";import{c as v,k as f,l as M,n as F,e as I,q as _,w as g,f as L,F as N,s as $,v as r,m as q,x as z,i as V,o as u,y as h,h as A,t as D}from"./.pnpm-Kv7TmmH8.js";const E={flex:"~ row items-center"},P={ml2:""},R=v({name:"select-user"}),J=v({...R,props:f({width:{default:"150px"},currentUser:{type:Boolean,default:!0}},{modelValue:{},modelModifiers:{}}),emits:f(["update:modelValue","change"],["update:modelValue"]),setup(c,{emit:w}){var p;const d=c,s=w,x=C(),y=M(),a=F(c,"modelValue"),{user:U}=S(),i=((p=U.info)==null?void 0:p.id)||null,l=I({get:()=>d.currentUser&&!a.value?(s("update:modelValue",i),i):a.value,set:t=>{a.value=t,s("update:modelValue",t)}}),m=x.getUserList();function k(t){if(t===void 0){s("change",t);return}const o=m.value.find(n=>n.id===t);s("change",o)}return(t,o)=>{const n=V("el-option"),B=V("el-select");return u(),_(B,q({modelValue:r(l),"onUpdate:modelValue":o[0]||(o[0]=e=>z(l)?l.value=e:null)},r(y),{clearable:"",style:`width:${d.width}`,onChange:k}),{default:g(()=>[(u(!0),L(N,null,$(r(m),e=>(u(),_(n,{key:e.id,label:e.name,value:e.id},{default:g(()=>[h("div",E,[A(b,{src:e.headImg,size:30,title:e.name},null,8,["src","title"]),h("span",P,D(e.name),1)])]),_:2},1032,["label","value"]))),128))]),_:1},16,["modelValue","style"])}}});export{J as _};
