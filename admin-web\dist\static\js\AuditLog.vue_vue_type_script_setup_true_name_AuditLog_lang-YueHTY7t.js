import{_ as d}from"./AuditLogTable.vue_vue_type_script_setup_true_name_AuditLogTable_lang-CvM6UG7-.js";import{c as s,n as m,b as r,A as i,q as f,i as c,v as p,x as _,w as v,h as V,o as w}from"./.pnpm-Kv7TmmH8.js";const x=s({name:"AuditLog"}),k=s({...x,props:{modelValue:{},modelModifiers:{}},emits:["update:modelValue"],setup(t){const e=m(t,"modelValue"),o=r(!1);function u(){o.value=!1,e.value=0}return i(()=>{e.value!==void 0&&e.value>0&&(o.value=!0)}),(C,l)=>{const n=c("el-dialog");return w(),f(n,{modelValue:p(o),"onUpdate:modelValue":l[1]||(l[1]=a=>_(o)?o.value=a:null),title:"审核记录","destroy-on-close":"",onClose:u},{default:v(()=>[V(d,{modelValue:e.value,"onUpdate:modelValue":l[0]||(l[0]=a=>e.value=a)},null,8,["modelValue"])]),_:1},8,["modelValue"])}}});export{k as _};
