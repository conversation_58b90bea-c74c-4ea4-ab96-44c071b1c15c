import{c as o,b as r,aq as i,e as t,f as c,o as l,y as m,V as f,W as u}from"./.pnpm-Kv7TmmH8.js";import{_ as d}from"./_plugin-vue_export-helper-DlAUqK2U.js";const _=o({name:"ClSvg",props:{name:{type:String},className:{type:String},size:{type:[String,Number]}},setup(e){const s=r({fontSize:i(e.size)?`${e.size}px`:e.size}),a=t(()=>`#icon-${e.name}`),n=t(()=>["cl-svg",`cl-svg__${e.name}`,String(e.className||"")]);return{style:s,iconName:a,svgClass:n}}}),g=["xlink:href"];function p(e,s,a,n,v,y){return l(),c("svg",{class:u(e.svgClass),style:f(e.style),"aria-hidden":"true"},[m("use",{"xlink:href":e.iconName},null,8,g)],6)}const S=d(_,[["render",p],["__scopeId","data-v-2fef5527"]]);export{S as default};
