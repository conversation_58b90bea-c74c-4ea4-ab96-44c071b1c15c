package admin

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/modules/pms/service"
	"github.com/imhuso/lookah-erp/admin/yc"
)

type PmsPurchaseContractController struct {
	*yc.Controller
}

// UpdateContractStatusReq 批量更新合同付款状态
type UpdateContractStatusReq struct {
	g.Meta        `path:"/updateContractStatus" method:"POST"  summary:"批量更新合同付款状态" tags:"采购合同"`
	Ids           []int64 `json:"ids"`            // 合同id列表
	PaymentStatus string  `json:"payment_status"` // 付款状态描述
}

// UpdateContractStatus 通过PO号查询合同列表
func (c *PmsPurchaseContractController) UpdateContractStatus(ctx context.Context, req *UpdateContractStatusReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsPurchaseContractService().UpdateContractStatus(ctx, req.Ids, req.PaymentStatus)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// GetContractListByPoAndSupplierIdReq 通过PO号以及供应商ID查询合同列表
type GetContractListByPoAndSupplierIdReq struct {
	g.Meta     `path:"/getContractListByPoAndSupplierId" method:"GET"  summary:"通过PO号以及供应商ID查询合同列表" tags:"采购合同"`
	Po         string `json:"po"`          // PO号
	SupplierId int64  `json:"supplier_id"` // 供应商ID
}

// GetContractListByPoAndSupplierId 通过PO号查询合同列表
func (c *PmsPurchaseContractController) GetContractListByPoAndSupplierId(ctx context.Context, req *GetContractListByPoAndSupplierIdReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsPurchaseContractService().GetContractListByPoAndSupplierId(ctx, req.Po, req.SupplierId)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// GetUnfinishedPoReq 查询未完成的PO列表
type GetUnfinishedPoReq struct {
	g.Meta     `path:"/getUnfinishedPo" method:"GET"  summary:"查询未完成的PO列表" tags:"采购合同"`
	SupplierId int64 `json:"supplier_id"` // 供应商ID
}

// GetUnfinishedPo 查询未完成的PO列表
func (c *PmsPurchaseContractController) GetUnfinishedPo(ctx context.Context, req *GetUnfinishedPoReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsPurchaseContractService().GetUnfinishedPo(ctx, req.SupplierId)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

type ImportContractExcelDataReq struct {
	g.Meta `path:"/importContractExcelData" method:"POST"  summary:"导入合同Excel数据" tags:"采购合同"`
}

func (c *PmsPurchaseContractController) ImportContractExcelData(ctx context.Context, _ *ImportContractExcelDataReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsPurchaseContractService().ImportContractExcelData(ctx, nil)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil
}

type PurchaseContractQueryPageReq struct {
	g.Meta `path:"/queryPage" method:"POST"  summary:"分页查询" tags:"采购合同"`
	*model.QueryVo
}

func (c *PmsPurchaseContractController) QueryPage(ctx context.Context, req *PurchaseContractQueryPageReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsPurchaseContractService().QueryPage(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// 导入Excel请求
type PurchaseContractImportReq struct {
	g.Meta  `path:"/purchaseContractImport" method:"POST"  summary:"导入Excel" tags:"采购合同"`
	QueryVo *model.QueryVo `json:"queryVo"`
}

func (c *PmsPurchaseContractController) ImportExcel(ctx context.Context, req *PurchaseContractImportReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsPurchaseContractService().ImportExcel(ctx, req.QueryVo)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil
}

// 导入出库Excel请求
type PurchaseContractOutboundImportReq struct {
	g.Meta `path:"/purchaseContractOutboundImport" method:"POST"  summary:"导入出库Excel" tags:"采购合同"`
}

func (c *PmsPurchaseContractController) ImportOutboundExcel(ctx context.Context, _ *PurchaseContractOutboundImportReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsPurchaseContractService().ImportOutboundExcel(ctx)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil
}

func init() {
	var pmsPurchaseContractController = &PmsPurchaseContractController{
		&yc.Controller{
			Prefix:  "/admin/pms/purchase/contract",
			Api:     []string{"Add", "Delete", "Update", "Info", "List", "Page"},
			Service: service.NewPmsPurchaseContractService(),
		},
	}
	// 注册路由
	yc.RegisterController(pmsPurchaseContractController)
}
