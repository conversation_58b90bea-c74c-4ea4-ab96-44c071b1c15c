import{i as A}from"./index-BuqCFB-b.js";import{_ as ce}from"./material-selector.vue_vue_type_script_name_product-selector_setup_true_lang-B31T1lMk.js";import{c as W,b as m,q as h,i as r,w as t,h as l,G as fe,H as _e,v as q,j as u,B as F,t as g,y as b,W as ve,E as p,T as be,o as w}from"./.pnpm-Kv7TmmH8.js";import{a as ye}from"./index-BAHxID_w.js";import{n as he}from"./index-CBanFtSc.js";import{u as ge}from"./table-ops-B_rNWTRT.js";import{_ as we}from"./material-table-columns.vue_vue_type_script_setup_true_lang-hBwyRg0n.js";const Ve=b("div",{style:{"border-bottom":"1px solid #dedede"},class:"py5px"}," 物料信息 ",-1),ke={class:"my10px flex items-center justify-between"},Ce={class:"flex items-center"},xe={class:"mr10px"},qe={class:"dialog-footer"},Be=W({name:"pms-material-bundle"}),ze=W({...Be,props:{selector:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(R,{emit:G}){const B=R,Q=G,{service:c}=ye(),f=m(!1),y=m(!1),S=m(null),V=m([]),k=m(!1),T={id:0,name:"",code:"",materials:[]},a=m(T),M=m({"slot-btn-update":{width:70,permission:c.pms.material.bundle.permission.saveBundle,show:!0},"slot-btn-delete":{width:70,permission:c.pms.material.bundle.permission.delete,show:!0}}),{getOpWidth:J,checkOpButtonIsAvaliable:U,getOpIsHidden:K}=ge(M),D=m(),E=m(!1);D.value=J(),E.value=K();const $=A.useTable({autoHeight:!B.selector,columns:[{type:"selection",width:50,hidden:!B.selector},{type:"expand",prop:"materials",label:"详情",width:100},{prop:"id",label:"ID"},{prop:"name",label:"名称"},{prop:"code",label:"代码"},{prop:"materialCount",label:"物料数量"},{prop:"materialTotal",label:"总用量"},{prop:"createTime",label:"创建时间",width:180},{type:"op",label:"操作",width:D,hidden:E,buttons:Object.keys(M.value)}]}),C=A.useCrud({service:c.pms.material.bundle},n=>{n.refresh()});function L(){f.value=!0}const z=m();async function P(){try{if(!await z.value.validate())return}catch{return}if(k.value=!0,a.value.materials.some(e=>!e.quantity))return p.error("请填写物料数量");if(k.value=!1,a.value.materials.length===0)return p.error("请添加物料");c.pms.material.bundle.saveBundle(a.value).then(()=>{var e;p.success("保存成功"),f.value=!1,(e=C.value)==null||e.refresh()}).catch(e=>{p.error(e.message||"保存失败")})}function X(){y.value=!0}function Y(){var e;const n=V.value;if(n.length===0)return p.warning("请选择物料");n.forEach(d=>{a.value.materials.find(_=>_.id===d.id)||a.value.materials.push({...d,quantity:null})}),y.value=!1,(e=S.value)==null||e.resetData(),a.value.id===0&&(a.value=T)}function Z(n,e){var d;(e==null?void 0:e.type)==="expand"||(e==null?void 0:e.type)==="op"||(d=$.value)==null||d.toggleRowExpansion(n)}function ee(n){a.value={...n,materials:n.materials.map(e=>({...e,quantity:e.quantity||null}))},f.value=!0}function le(n){be.confirm("确认删除吗？").then(()=>{c.pms.material.bundle.delete({ids:[n.id]}).then(()=>{var e;p.success("删除成功"),(e=C.value)==null||e.refresh()}).catch(e=>{p.error(e.message||"删除失败")})}).catch(()=>{})}function O(n){var d;const e=((d=n.materials)==null?void 0:d.reduce((i,_)=>i+(_.quantity||0),0))||0;return he(e)}function te(n){Q("update:modelValue",n)}function ae(){return a.value.materials.map(n=>n.id)}return(n,e)=>{const d=r("cl-refresh-btn"),i=r("el-button"),_=r("cl-flex1"),oe=r("cl-search-key"),x=r("cl-row"),v=r("el-table-column"),H=r("el-table"),ne=r("cl-table"),se=r("cl-pagination"),I=r("el-input"),N=r("el-form-item"),re=r("el-input-number"),ie=r("el-form"),ue=r("cl-dialog"),de=r("el-dialog"),me=r("cl-crud"),pe=_e("permission");return w(),h(me,{ref_key:"Crud",ref:C},{default:t(()=>[l(x,null,{default:t(()=>[l(d),fe((w(),h(i,{type:"primary",onClick:L},{default:t(()=>[u(" 新增 ")]),_:1})),[[pe,q(c).pms.material.bundle.permission.saveBundle]]),l(_),l(oe)]),_:1}),l(x,null,{default:t(()=>[l(ne,{ref_key:"Table",ref:$,"row-key":"id",onSelectionChange:te,onRowClick:Z},{"column-materials":t(({scope:o})=>[l(H,{border:"",data:o.row.materials,style:{width:"100%"}},{default:t(()=>[l(we,{"auto-width":""}),l(v,{prop:"quantity",label:"用量",align:"left","show-overflow-tooltip":""})]),_:2},1032,["data"])]),"column-materialCount":t(({scope:o})=>{var s;return[u(g(((s=o.row.materials)==null?void 0:s.length)||0),1)]}),"column-materialTotal":t(({scope:o})=>[u(g(O(o.row)),1)]),"slot-btn-update":t(({scope:o})=>[q(U)("slot-btn-update")?(w(),h(i,{key:0,size:"small",type:"primary",onClick:s=>ee(o.row)},{default:t(()=>[u(" 编辑 ")]),_:2},1032,["onClick"])):F("",!0)]),"slot-btn-delete":t(({scope:o})=>[q(U)("slot-btn-delete")?(w(),h(i,{key:0,type:"danger",size:"small",onClick:s=>le(o.row)},{default:t(()=>[u(" 删除 ")]),_:2},1032,["onClick"])):F("",!0)]),_:1},512)]),_:1}),l(x,null,{default:t(()=>[l(_),l(se)]),_:1}),l(ue,{modelValue:f.value,"onUpdate:modelValue":e[5]||(e[5]=o=>f.value=o),"close-on-press-escape":!1,controls:["close"],"min-width":800,title:"维护物料集合","append-to-body":""},{footer:t(()=>[l(i,{onClick:e[4]||(e[4]=o=>f.value=!1)},{default:t(()=>[u(" 取 消 ")]),_:1}),l(i,{type:"success",onClick:P},{default:t(()=>[u(" 保 存 ")]),_:1})]),default:t(()=>[l(ie,{ref_key:"bundleForm",ref:z,model:a.value,"label-width":"80px"},{default:t(()=>{var o;return[l(N,{label:"名称",prop:"name",required:"",rules:[{required:!0,message:"请输入名称",trigger:"blur"}]},{default:t(()=>[l(I,{modelValue:a.value.name,"onUpdate:modelValue":e[0]||(e[0]=s=>a.value.name=s)},null,8,["modelValue"])]),_:1}),l(N,{label:"代码",prop:"code",required:"",rules:[{required:!0,message:"请输入代码",trigger:"blur"}]},{default:t(()=>[l(I,{modelValue:a.value.code,"onUpdate:modelValue":e[1]||(e[1]=s=>a.value.code=s),disabled:a.value.id>0},null,8,["modelValue","disabled"])]),_:1}),Ve,b("div",ke,[l(i,{type:"primary",onClick:X},{default:t(()=>[u(" 选择物料 ")]),_:1}),b("div",Ce,[b("div",xe," 物料数量："+g(((o=a.value.materials)==null?void 0:o.length)||0),1),b("div",null," 总用量："+g(O(a.value)),1)]),l(i,{type:"danger",onClick:e[2]||(e[2]=s=>a.value.materials=[])},{default:t(()=>[u(" 清空 ")]),_:1})]),l(H,{modelValue:a.value.materials,"onUpdate:modelValue":e[3]||(e[3]=s=>a.value.materials=s),border:"",data:a.value.materials,style:{width:"100%"},"max-height":600},{default:t(()=>[l(v,{prop:"code",label:"物料编码",align:"left","show-overflow-tooltip":""}),l(v,{prop:"name",label:"名称",align:"left","show-overflow-tooltip":""}),l(v,{prop:"model",label:"型号",align:"left","show-overflow-tooltip":""}),l(v,{label:"用量",width:"200",align:"center"},{default:t(({row:s})=>[l(re,{modelValue:s.quantity,"onUpdate:modelValue":j=>s.quantity=j,style:{width:"100%"},class:ve(k.value&&!(s.quantity>0)?"material-quantity-input-error":""),min:0,precision:4,size:"small"},null,8,["modelValue","onUpdate:modelValue","class"])]),_:1}),l(v,{label:"操作",width:"100",align:"center"},{default:t(({row:s})=>[l(i,{type:"danger",size:"small",onClick:j=>a.value.materials.splice(a.value.materials.indexOf(s),1)},{default:t(()=>[u(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["modelValue","data"])]}),_:1},8,["model"])]),_:1},8,["modelValue"]),l(de,{modelValue:y.value,"onUpdate:modelValue":e[8]||(e[8]=o=>y.value=o),title:"选择物料",width:"80%","append-to-body":"","z-index":99999,modal:!1},{footer:t(()=>[b("span",qe,[l(i,{onClick:e[7]||(e[7]=o=>y.value=!1)},{default:t(()=>[u("取消")]),_:1}),l(i,{type:"primary",onClick:Y},{default:t(()=>[u(" 确认选择 ")]),_:1})])]),default:t(()=>[l(ce,{ref_key:"materialSelector",ref:S,modelValue:V.value,"onUpdate:modelValue":e[6]||(e[6]=o=>V.value=o),"has-stock":!1,"disabled-materials":ae()},null,8,["modelValue","disabled-materials"])]),_:1},8,["modelValue"])]),_:1},512)}}});export{ze as _};
