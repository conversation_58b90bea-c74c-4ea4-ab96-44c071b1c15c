import{c as Q,b as u,A as j,S as ue,E as y,q as z,w as i,h as o,i as p,y as d,t as v,B as me,v as _e,Y as be,j as he,f as I,s as R,F as $,o as w}from"./.pnpm-Kv7TmmH8.js";import{i as T,e as ve}from"./index-BuqCFB-b.js";import{u as fe}from"./table-ops-B_rNWTRT.js";import{a as ye}from"./index-BAHxID_w.js";const ge={class:"test-items-form"},we={class:"category-title"},ke={class:"test-items-grid"},qe={class:"test-item-header"},xe={class:"tool-name"},Ie={class:"test-item-header"},Te={class:"tool-name"},Me=Q({name:"pms-material-testing"}),Ve=Q({...Me,setup(Fe){const M=u([]),O=u([]),S=u([]),F=u([]),{service:m}=ye(),B=T.useCrud({service:m.pms.material_test},e=>{e.refresh()});async function U(){try{O.value=await m.pms.supplier.request({url:"/list",method:"POST"}),M.value=O.value.map(e=>({label:e.supplierName,value:e.id}))}catch(e){console.error(e)}}async function L(e){try{const t=await m.pms.material.request({url:"/getMaterialByName",method:"GET",params:{keyword:e}});F.value=t.map(a=>({...a,value:a.id,label:`${a.name} / ${a.code}`,name:a.name,code:a.code,materialSize:a.size}))}catch(t){console.error(t)}}async function P(e){try{const t=await m.pms.material.request({url:"/getMaterialById",method:"GET",params:{id:e}});F.value=[t].map(a=>({id:a.id,value:a.id,label:`${a.name} / ${a.code}`,name:a.name,code:a.code,materialSize:a.size}))}catch(t){console.error(t)}}async function G(){try{const e=await m.pms.material.request({url:"/list",method:"POST"});S.value=e.map(t=>({id:t.id,value:t.id,label:`${t.name} / ${t.code}`,name:t.name,code:t.code,materialSize:t.size}))}catch(e){console.error(e)}}const A=u([{label:"允收",value:"允收"},{label:"特采",value:"特采"},{label:"退货",value:"退货"},{label:"供应商加工/挑选",value:"供应商加工/挑选"},{label:"生产加工/挑选，耗时按30RMB/人/小时计算",value:"生产加工/挑选，耗时按30RMB/人/小时计算"}]);j(()=>{U(),G()});const H=T.useSearch({items:[{label:"关键字",prop:"keyWord",props:{labelWidth:"160px"},component:{name:"el-input",props:{style:"width: 360px",placeholder:"请输入供应商名称/物料名称/检测项目/检测仪器",clearable:!0,onChange(e){var t;(t=B.value)==null||t.refresh({keyWord:e.trim(),page:1})}}}}]}),V=u({edit:{width:80,permission:m.pms.material_test.permission.update,show:!0},delete:{width:80,permission:m.pms.material_test.permission.delete,show:!0},"slot-btn-export":{width:110,permission:m.pms.material_test.permission.export,show:!0}}),{getOpWidth:K,checkOpButtonIsAvaliable:J,getOpIsHidden:X}=fe(V),Z=u(K()),ee=u(X()),C=u([{label:"核对",value:"核对",children:[{label:"送货单",value:"送货单",code:"delivery_note",standard:"核对实物与送货单物料名称、规格、数量等内容是否相符"},{label:"出货检验报告",value:"出货检验报告",code:"test_report",standard:"核对供应商是否提供出货检验报告，内容是否真实有效"},{label:"BOM",value:"BOM",code:"bom",standard:"核对实物与BOM表规格要求是否相符"},{label:"样品/承认书",value:"样品/承认书",code:"acknowledgment",standard:"核对实物外观/颜色/表面工艺与工程签样是否相符"}]},{label:"包装防护",value:"包装防护",children:[{label:"目视",value:"目视",code:"visual_package",standard:"1、检查包装方式是否符合产品要求，是否会造成产品损伤,2、检查外包装是否有标识，包括品名/规格/数量/物料编码等"}]},{label:"适配",value:"适配",children:[{label:"相关配件",value:"相关配件",code:"relevant_part",standard:"与相关匹配件进行适配，检验吻合度是否符合要求（如缝隙/断差/紧密度等）"}]},{label:"外观",value:"外观",children:[{label:"目视",value:"目视",code:"visual_facade",standard:"表面无脏污、无变形、压伤、掉漆，破损等现象，字迹无毛边，缺画等"}]},{label:"尺寸",value:"尺寸",children:[{label:"卡尺",value:"卡尺",code:"callipers",standard:"（见相关图纸）检查各关键尺寸及公差是否符合图纸要求"}]},{label:"可靠性",value:"可靠性",children:[{label:"盐雾试验机",value:"盐雾试验机",code:"salt_spray_tester",standard:"5%浓度的工业盐水，喷雾24小时，检验金属表面是否生锈"},{label:"酒精摩擦附着力测试",value:"酒精摩擦附着力测试",code:"adhesion_tester",standard:"1、95%浓度的酒精擦拭，检查丝印是否脱落,2、3M胶带贴于丝印表面，45度角，用力拉扯，检查丝印是否脱落"},{label:"水煮测试",value:"水煮测试",code:"boiling_test",standard:"在80℃的恒温水浴锅中煮30min，无起泡、无桔皮、无明显变色等不良现象"},{label:"高温存储试验",value:"高温存储试验",code:"hot_test",standard:"在80℃环境下存储24h，无漆裂、无变形、无变色等不良现象"},{label:"低温存储试验",value:"低温存储试验",code:"low_temperature_test",standard:"在-20℃环境下存储25h，无漆裂、无变形、无变色等不良现象"},{label:"耐化学品测试",value:"耐化学品测试",code:"chemical_resistance_test",standard:"耐凡士林护手霜/耐人工汗/耐测试烟油测试，详见内部可靠性检测标准，无起泡、无麻点、无变色等不良现象"}]}]),f=u({items:[]});function W(){f.value.items=C.value.reduce((e,t)=>{const a=t.children.map(l=>({testItem:t.value,testTool:l.value,code:l.code,description:"",remark:""}));return[...e,...a]},[])}ue(()=>{W()});const c=T.useUpsert({props:{labelWidth:"120px",class:"material_test-form",width:"90%"},items:[{label:"检测日期",prop:"test_date",required:!0,component:{name:"el-date-picker",props:{type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",clearable:!0,disabledDate:e=>e.getTime()>Date.now()}}},{label:"供应商",prop:"supplier_id",required:!0,component:{options:M,name:"el-select",props:{clearable:!0,filterable:!0}}},{label:"选择物料",prop:"material_id",required:!0,component:{options:F,name:"el-select",props:{clearable:!0,filterable:!0,"filter-method":e=>{e!==""&&L(e)}}}},{label:"总量",prop:"batch_quantity",required:!0,component:{name:"el-input-number",props:{min:1}}},{label:"抽检数",prop:"check_quantity",required:!0,component:{name:"el-input-number",props:{min:1,onChange:e=>{var a,l,n;const t=(a=c.value)==null?void 0:a.getForm("batch_quantity");console.log("batchQty",t),t===void 0&&(y.warning("请先输入批量数"),(l=c.value)==null||l.setForm("check_quantity",void 0)),t&&e>t&&(y.warning("抽检数不能大于总量"),(n=c.value)==null||n.setForm("check_quantity",t))}}}},{label:"不良数",prop:"bad_quantity",required:!0,component:{name:"el-input-number",props:{min:0,onChange:e=>{var a,l,n;const t=(a=c.value)==null?void 0:a.getForm("check_quantity");t===void 0&&(y.warning("请先输入检测数"),(l=c.value)==null||l.setForm("bad_quantity",void 0)),t&&e>t&&(y.warning("不良数不能大于抽检数"),(n=c.value)==null||n.setForm("bad_quantity",t)),Y()}}}},{label:"不良率",prop:"reject_ratio",required:!0,component:{name:"el-input-number",props:{disabled:!0,placeholder:"0.0",precision:1,formatter:e=>`${e}%`,parser:e=>Number.parseFloat(e.replace("%","")),controls:!1}}},{label:"抽样方案",prop:"sampling_plan",required:!0,component:{name:"el-input",props:{disabled:!0}}},{label:"处理结果",prop:"dispose",required:!0,component:{options:A,name:"el-select",props:{clearable:!0,filterable:!0}}},{label:"检验员",prop:"inspector",required:!0,component:{name:"el-input"}},{label:"备注",prop:"remark",required:!0,component:{name:"el-input"}},{label:"附图",prop:"voucher",component:{name:"cl-upload",props:{limit:1,accept:"image/jpg,image/jpeg,image/png",text:"上传附图",type:"image",disabled:!1,isPrivate:!1}}},{label:"不良项目",prop:"testItems",required:!1,component:{name:"slot-test-items"}}],async onOpen(){var e;(e=c.value)==null||e.setForm("sampling_plan","GB/T 2828.1-2012"),setTimeout(()=>{const t=document.querySelector(".el-dialog");t&&t.scrollIntoView({behavior:"smooth",block:"start"})},100)},async onInfo(e,{done:t}){P(e.material_id),f.value.items=C.value.reduce((a,l)=>{const n=l.children.map(r=>{var h,q;return{testItem:l.value,testTool:r.value,code:r.code,description:(h=e.testItems.find(_=>_.code==r.code))==null?void 0:h.description,remark:(q=e.testItems.find(_=>_.code==r.code))==null?void 0:q.remark}});return[...a,...n]},[]),t(e),setTimeout(()=>{const a=document.querySelector(".el-dialog");a&&a.scrollIntoView({behavior:"smooth",block:"start"})},100)},onSubmit(e,{done:t,close:a,next:l}){let n=!1;if(e.testItems=[],f.value.items.forEach(r=>{(r.description||r.remark)&&(e.testItems.push({testItem:r.testItem,testTool:r.testTool,code:r.code,description:r.description,remark:r.remark}),n=!0)}),!n){y.error("没有填写不良项目数据，请检查表单！");return}l({id:e.id,requestData:e,done:t}),a()},async onClose(e,t){W(),t()}}),E=T.useTable({columns:[{label:"展开",prop:"testItems",type:"expand",width:60},{label:"异常日期",prop:"test_date",width:120,component:{name:"cl-date-text",props:{format:"YYYY-MM-DD"}}},{label:"批量",prop:"batch_quantity",minWidth:50},{label:"检测数",prop:"check_quantity",minWidth:40},{label:"不良数",prop:"bad_quantity",minWidth:40},{label:"不良率",prop:"reject_ratio",minWidth:40},{label:"物料名称",prop:"material_id",width:220,dict:S,showOverflowTooltip:!0},{label:"供应商",prop:"supplier_id",width:220,dict:M},{label:"处理结果",prop:"dispose",minWidth:60},{label:"检验员",prop:"inspector",minWidth:50},{label:"抽样方案",prop:"sampling_plan",minWidth:60},{label:"附图",prop:"voucher",width:120,component:{name:"cl-image",props:{fit:"cover",lazy:!0,size:[50,50]}}},{label:"备注",prop:"remark",minWidth:120},{type:"op",label:"操作",width:Z,hidden:ee,buttons:Object.keys(V.value)}]});function Y(){var a,l,n,r;const e=(a=c.value)==null?void 0:a.getForm("check_quantity"),t=(l=c.value)==null?void 0:l.getForm("bad_quantity");if(e&&t!==void 0&&e>0){const h=Number.parseFloat((t/e*100).toFixed(1));(n=c.value)==null||n.setForm("reject_ratio",h)}else(r=c.value)==null||r.setForm("reject_ratio",0)}j(()=>{var a,l;const e=(a=c.value)==null?void 0:a.getForm("check_quantity"),t=(l=c.value)==null?void 0:l.getForm("bad_quantity");e!==void 0&&t!==void 0&&Y()});function te(){return"primary-row"}const ae=u([]);function le(e,t){var a;(t==null?void 0:t.type)==="expand"||(t==null?void 0:t.type)==="op"||(a=E.value)==null||a.toggleRowExpansion(e)}function oe(e){const t={url:"/export",method:"POST",data:{materialTesting:e},responseType:"blob"};m.pms.material_test.request(t).then(a=>{var l;ve(a)&&y.success("导出成功"),(l=B.value)==null||l.refresh()}).catch(a=>{y.error(a.message||"导出失败")})}return(e,t)=>{const a=p("cl-add-btn"),l=p("cl-refresh-btn"),n=p("cl-flex1"),r=p("cl-search"),h=p("el-row"),q=p("el-button"),_=p("el-table-column"),re=p("el-table"),se=p("cl-table"),ne=p("cl-pagination"),D=p("el-input"),N=p("el-form-item"),ie=p("cl-upsert"),ce=p("cl-crud");return w(),z(ce,{ref_key:"Crud",ref:B},{default:i(()=>[o(h,null,{default:i(()=>[o(a),o(l),o(n),o(r,{ref_key:"Search",ref:H},null,512)]),_:1}),o(h,null,{default:i(()=>[o(se,{ref_key:"Table",ref:E,"auto-height":!1,"row-key":"id","expand-row-keys":ae.value,class:"table-row-pointer",onRowClick:le},{"slot-btn-export":i(({scope:x})=>[_e(J)("slot-btn-export")?(w(),z(q,{key:0,text:"",bg:"",type:"success",onClick:be(s=>oe(x.row),["stop"])},{default:i(()=>[he(" 导出 ")]),_:2},1032,["onClick"])):me("",!0)]),"column-testItems":i(({scope:x})=>[o(re,{data:x.row.testItems,style:{width:"100%"},border:"","row-class-name":te},{default:i(()=>[o(_,{label:"检验项目",align:"center"},{default:i(s=>[d("span",null,v(s.row.test_item),1)]),_:2},1024),o(_,{label:"检测仪器工具",align:"center"},{default:i(s=>[d("span",null,v(s.row.test_tool),1)]),_:2},1024),o(_,{label:"code",align:"center"},{default:i(s=>[d("span",null,v(s.row.code),1)]),_:2},1024),o(_,{label:"不良描述",align:"center"},{default:i(s=>[d("span",null,v(s.row.description),1)]),_:2},1024),o(_,{label:"不良备注",align:"center"},{default:i(s=>[d("span",null,v(s.row.remark),1)]),_:2},1024)]),_:2},1032,["data"])]),_:1},8,["expand-row-keys"])]),_:1}),o(h,null,{default:i(()=>[o(n),o(ne)]),_:1}),o(ie,{ref_key:"Upsert",ref:c},{"slot-test-items":i(({scope:x})=>[d("div",ge,[(w(!0),I($,null,R(C.value,(s,pe)=>(w(),I("div",{key:pe,class:"test-category"},[d("h3",we,v(s.label),1),d("div",ke,[(w(!0),I($,null,R(s.children,(g,de)=>(w(),I("div",{key:de,class:"test-item-row"},[d("div",qe,[d("span",xe,v(g.label),1)]),d("div",Ie,[d("span",Te,"检查标准："+v(g.standard),1)]),o(N,{label:"问题描述"},{default:i(()=>[o(D,{modelValue:f.value.items.find(b=>b.testItem===s.value&&b.testTool===g.value).description,"onUpdate:modelValue":b=>f.value.items.find(k=>k.testItem===s.value&&k.testTool===g.value).description=b,type:"textarea",rows:2,placeholder:"请输入问题描述"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),o(N,{label:"备注"},{default:i(()=>[o(D,{modelValue:f.value.items.find(b=>b.testItem===s.value&&b.testTool===g.value).remark,"onUpdate:modelValue":b=>f.value.items.find(k=>k.testItem===s.value&&k.testTool===g.value).remark=b,type:"textarea",rows:2,placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]))),128))])]))),128))])]),_:1},512)]),_:1},512)}}});export{Ve as default};
