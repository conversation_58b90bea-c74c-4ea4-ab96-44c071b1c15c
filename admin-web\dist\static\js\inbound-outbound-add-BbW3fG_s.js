import{_ as de}from"./material-excel-import.vue_vue_type_script_setup_true_lang-xGZIJ5UI.js";import{_ as aa}from"./material-selector.vue_vue_type_script_name_product-selector_setup_true_lang-B31T1lMk.js";import{_ as la}from"./material-table-columns.vue_vue_type_script_setup_true_lang-hBwyRg0n.js";import{_ as ta}from"./purchase-pending-order.vue_vue_type_script_setup_true_lang-CxJm1w1Z.js";import{c as ee,l as ua,q as m,m as na,v as p,o as v,ae as ra,b as g,A as oe,f as I,y as b,h as i,w as c,B as h,i as $,t as M,j as q,F as N,s as E,W as ie,aZ as da,E as x,T as S,U as P,af as oa,ag as ia}from"./.pnpm-Kv7TmmH8.js";import{n as ke}from"./index-CBanFtSc.js";import{_ as qe}from"./select-dict.vue_vue_type_script_setup_true_name_select-dict_lang-rWvH4Sy8.js";/* empty css              */import{_ as sa}from"./OutboundTable.vue_vue_type_script_setup_true_name_OutboundTable_lang-BGd-CvjM.js";import va from"./index-BQv5-z8i.js";import"./index-BuqCFB-b.js";import{a as ca}from"./index-BAHxID_w.js";import{_ as pa}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ma=ee({name:"undefined"}),ya=ee({...ma,setup(O){const A=ua();return(Q,se)=>(v(),m(ta,na({"is-selector":"","page-size":10},p(A)),null,16))}}),K=O=>(oa("data-v-f99731ef"),O=O(),ia(),O),fa={class:"cl-crud inbound-create"},ha={class:"order-create-body"},ba=K(()=>b("div",{class:"inbound-create-header"}," 基础信息 ",-1)),_a={class:"is-order-label"},wa={key:0},ga={key:1},Ia={key:0,"text-green-6":""},xa={key:1,"text-red":""},ka={key:0,flex:"~"},qa={"text-green-6":""},Va={pl:"50px"},$a=K(()=>b("span",{pr:"12px"},"工单号",-1)),Qa={"text-green-6":""},Ma={pl:"50px"},Ca=K(()=>b("span",{pr:"12px"},"产品名称",-1)),Sa={"text-green-6":""},Ua={pl:"50px"},Ta=K(()=>b("span",{pr:"12px"},"计划生产数量",-1)),Oa={"text-green-6":""},za={key:1,"text-red":""},Ba={class:"inbound-create-header"},Da={flex:"~ justify-between items-center"},Na={key:0,flex:"~ "},Ea={key:1,flex:"~ items-center"},Pa={key:2,flex:"~ items-center"},Ka={key:3,flex:"~"},Aa={key:4,flex:"~"},La={key:0,style:{display:"flex","align-items":"center"}},Fa={key:1,style:{display:"flex","align-items":"center"}},Ra={key:2,style:{display:"flex","align-items":"center"}},Ya={key:3,style:{display:"flex","align-items":"center"}},Wa=K(()=>b("span",{style:{color:"var(--el-color-danger)"}},"*",-1)),ja=K(()=>b("span",{style:{color:"var(--el-color-danger)"}},"*",-1)),Ga={flex:"~ items-center"},Ha={class:"dialog-footer"},Za=ee({name:"undefined"}),Ja=ee({...Za,props:{type:{}},setup(O){const A=O,Q=88,{height:se}=ra(),{service:L,router:F}=ca(),V=g(!1),s=A.type,ve=g([]),ae=A.type==="inbound"?"入库":"出库",Ve=A.type==="inbound"?0:-1,$e=0,R=g({}),u=g({id:null,type:s==="outbound"?0:1,orderId:0,remark:"",materials:[],productId:0,workOrder:"",workOrderId:0}),H=g(!1),U=g(""),Qe=g(!1);oe(()=>{s==="outbound"&&(H.value=u.value.type===1&&u.value.orderId>0)});const Me=g({buttons:["slot-btn-outbound"],width:160}),ce=s==="inbound"?"/pms/material/inbound":"/pms/material/outbound",le=s==="inbound"?L.pms.material.inbound:L.pms.material.outbound,z=g(!1),Y=g(!1),Z=g(!1),te=g(),B=g(!1),J=g(!1),W=g(!1),pe=g(void 0),j=g(!1),r=g([]),G=g([]),y=g([]),D=g(!1);async function Ce(){te.value&&await te.value.validate(a=>{if(a){if(Y.value=!0,Z.value=!0,r.value.length===0){x.error({message:"请添加物料"});return}if(r.value=r.value.filter(t=>t.id||t.materialId),r.value.find(t=>u.value.type===2?!1:t.quantity===null||t.quantity===void 0||t.quantity===0||t.quantity<0)){x.error({message:"请填写正确的物料数量"});return}if(s==="outbound"&&u.value.type===3&&r.value.find(e=>e.total_quantity===null||e.total_quantity===void 0||e.total_quantity===0||e.total_quantity<0||e.total_quantity<e.quantity)){x.error({message:"请填写正确的物料总量"});return}s==="inbound"&&r.value.forEach(t=>{if(!t.warehouseId)throw x.error({message:"请选择仓库"}),new Error("请选择仓库")}),Y.value=!1,Z.value=!1,u.value.materials=r.value.map(t=>(Array.isArray(t.address)&&(t.address=t.address.join(",")),{materiaLdetail:t,contractId:u.value.type===1||u.value.type===9?t.contractId:null,materialId:u.value.type===1||u.value.type===9?t.materialId:t.id,quantity:t.quantity,warehouseId:t.warehouseId,address:t.address,scrap_date:t.scrap_date,product_group_id:t.product_group_id,responsible:t.responsible,handling_method:t.handling_method,inbound_outbound_key:t.inbound_outbound_key,total_quantity:t.total_quantity})),z.value=!0,V.value&&u.value.id&&u.value.id>0?le.update(u.value).then(()=>{F.push(`${ce}?tab=0`),x.success({message:"保存成功",onClose:()=>{z.value=!1}})}).catch(t=>{x.error({message:t.message}),z.value=!1}):(delete u.value.id,le.add(u.value).then(t=>{const e=t==null?void 0:t.id;F.push(`${ce}?tab=0&expand=${e}`),x.success({message:"保存成功",onClose:()=>{z.value=!1}})}).catch(t=>{x.error({message:t.message}),z.value=!1}))}})}function Se(a){if(a)return D.value=!0,L.pms.material.list({keyWord:a}).then(l=>{l=l||[];const t=new Set(r.value.map(k=>k.id)),e=y.value.filter(k=>t.has(k.value)),_=[...l.map(k=>({...k,value:k.id,uniqueKey:`${k.id}`,label:`${k.name}`,disabled:r.value.some(T=>T.id===k.id)})),...e],w=new Set,f=_.filter(k=>w.has(k.id)?!1:(w.add(k.id),!0));y.value=f}).finally(()=>{D.value=!1})}const me=g(null);function ye(){G.value=[],B.value=!0}function Ue(){G.value=[],J.value=!0}function Te(a){const l=y.value.find(e=>(e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):"",Number.parseInt(e.uniqueKey)===a));if(!l)return;const t=r.value.findIndex(e=>e.id===a);y.value=y.value.map(e=>(Number.parseInt(e.uniqueKey)!==a&&(e.disabled=!1),e)),t!==-1?r.value[t]={...l,index:r.value[t].index,contractId:l.value,id:l.value,warehouseId:l.warehouseId||Q}:r.value.push({...l,index:new Date().getTime()+Math.floor(Math.random()*1e3),id:l.value,contractId:l.value,quantity:null,warehouseId:l.warehouseId||Q})}function Oe(a){const l=y.value.find(e=>(e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):"",Number.parseInt(e.materialId)===a));if(!l)return;const t=r.value.findIndex(e=>e.materialId===a);y.value=y.value.map(e=>(Number.parseInt(e.materialId)!==a&&(e.disabled=!1),e)),t!==-1?r.value[t]={...l,index:r.value[t].index,contractId:l.value,id:l.value,warehouseId:l.warehouseId||Q}:r.value.push({...l,index:new Date().getTime()+Math.floor(Math.random()*1e3),id:l.value,contractId:l.value,quantity:null,warehouseId:l.warehouseId||Q})}function fe(a){const l=y.value.find(e=>(e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):"",e.value===a));if(!l)return;const t=r.value.findIndex(e=>e.id===a);y.value=y.value.map(e=>(e.value!==a&&(e.disabled=!1),e)),t!==-1?r.value[t]={...l,index:r.value[t].index,id:l.value,warehouseId:l.warehouseId||Q}:r.value.push({...l,index:new Date().getTime()+Math.floor(Math.random()*1e3),id:l.value,quantity:null,warehouseId:l.warehouseId||Q})}function ze(){j.value=!0}function Be(){B.value=!1,G.value.forEach(a=>{if(!r.value.find(t=>t.id===a.id)){y.value.push({...a,value:a.id,disabled:!1,label:`${a.name}`}),a.address=a.address_name!==""&&a.address_name!==void 0?a.address_name.split(","):"";const t={...a,index:new Date().getTime()+Math.floor(Math.random()*1e3),orderQuantity:a.quantity,quantity:null,warehouseId:0,address_arr:a.address_name!==""&&a.address_name!==void 0?a.address_name.split(","):[]};s==="inbound"&&(t.warehouseId=Q),r.value.push(t)}})}function De(a){r.value=r.value.filter(l=>l.index!==a)}function he(){!(u.value.type===1&&u.value.orderId)&&!(s==="outbound"&&u.value.type===0)&&!(s==="outbound"&&u.value.type===2)&&!(s==="outbound"&&u.value.type===5)&&!(s==="inbound"&&u.value.type===3)&&!(s==="inbound"&&u.value.type===4)&&(y.value=[]);const a={index:new Date().getTime()+Math.floor(Math.random()*1e3),id:null,name:"",model:"",inventory:0,unit:"",code:"",expectedInbound:0,quantity:null,coverColor:"",size:"",material:"",process:"",receivedQuantity:0,orderQuantity:0,returnOrderQuantity:0,restockingQty:0,transfer:0,uniqueKey:"",warehouseId:0};s==="inbound"&&(a.warehouseId=Q),r.value.push(a)}function Ne(){S.confirm("确定清空已选择的物料列表吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{r.value=[],me.value.resetData()}).catch(()=>{})}function Ee(a){le.info({id:a}).then(async l=>{u.value.type=(l==null?void 0:l.type)||0,u.value.remark=(l==null?void 0:l.remark)||"";const t=(l==null?void 0:l.orderId)||0;u.value.orderId=t;const e=(l==null?void 0:l.products)||[];e.forEach(o=>{o.Po&&!o.po&&(o.po=o.Po)}),t>0&&u.value.type!==4?(s==="outbound"&&(u.value.type===0||u.value.type===5)?(await be(l),r.value=e==null?void 0:e.map(o=>{const _=y.value.find(w=>w.id===o.materialId||w.materialId===o.materialId);return{...o,..._,id:o.materialId,index:new Date().getTime()+Math.floor(Math.random()*1e3),uniqueKey:o.contractId}})):s==="inbound"&&u.value.type===3?(await be(l),r.value=e==null?void 0:e.map(o=>{const _=y.value.find(w=>w.id===o.materialId||w.materialId===o.materialId);return{...o,..._,id:o.materialId,index:new Date().getTime()+Math.floor(Math.random()*1e3),uniqueKey:o.contractId}})):await _e(t.toString(),!1),r.value=e==null?void 0:e.map(o=>({...y.value.find(w=>w.materialId===o.materialId),...o,index:new Date().getTime()+Math.floor(Math.random()*1e3),uniqueKey:o.contractId}))):(y.value=e==null?void 0:e.map(o=>{const _=P(o);return{...o,returnOrderQuantity:_.returnOrderQuantity,value:o.materialId?o.materialId:o.id,uniqueKey:o.contractId??o.id,orderQuantity:o.quantity,label:`${o.name}`}}),r.value=e==null?void 0:e.map(o=>{const _=P(o);return{...o,returnOrderQuantity:_.returnOrderQuantity,id:o.materialId,index:new Date().getTime()+Math.floor(Math.random()*1e3),orderQuantity:o.quantity,uniqueKey:o.id}})),r.value=r.value.map(o=>(o.address&&(o.address=o.address.split(",")),o))}).catch(l=>{x.error({message:l.message||"获取入库单信息失败"})})}async function be(a){try{Pe(a.extras,a.workOrder,a.products)}catch(l){x.error({message:l.message||"获取生产订单信息失败"})}}function Pe(a,l,t){!a||!l||!t||(R.value=P(l),U.value=l.productionScheduleSn,u.value.workOrderId=l.id,r.value.length>0&&S.confirm("当前物料列表中存在已选数据，如果确定，让先清除列表中的数据，是否继续").then(e=>{e&&(r.value=[])}).catch(()=>{}),r.value=[],y.value=a.map(e=>({...e,value:e.id,materialId:e.id,uniqueKey:`${e.id}`,label:`${e.name}`,index:new Date().getTime()+Math.floor(Math.random()*1e3),disabled:r.value.some(o=>o.id===e.id)})))}async function _e(a,l=!0){try{const t=await L.pms.purchase.order.pending.info({id:a});await ge(t,l)}catch(t){x.error({message:t.message||"获取采购单信息失败"})}}function we(a,l){const t=e=>{x.error({message:e}),r.value=[]};if(r.value.length>0){x.error({message:"当前物料列表中存在已选数据，如需导入，请先清除列表中的数据"});return}l.some(e=>{const o=y.value.find(_=>_.code.toUpperCase().trim()===e.code.toUpperCase().trim());if(!o||!o.materialId)return t(`物料编码：${e.code} 不在采购单中`),!0;if(r.value.some(_=>_.id===o.value))return t(`物料编码：${e.code} 重复添加`),!0;if(e.quantity===null||e.quantity===void 0||e.quantity===0)return t(`物料编码：${e.code} 数量不能为空`),!0;if(s==="outbound"){if(H.value&&e.quantity>o.receivedQuantity)return t(`物料编码：${e.code} 数量不能大于已收数量，退货数量：${e.quantity}，已收数量：${o.receivedQuantity}`),!0}else{const _=ke(Math.max(o.orderQuantity-o.transfer-o.receivedQuantity,0));if(e.quantity>_+$e)return t(`物料编码：${e.code} 数量不能大于可入库数量，入库数量：${e.quantity}，可入库数量：${_}`),!0}return r.value.push({...o,index:new Date().getTime()+Math.floor(Math.random()*1e3),id:o.value,quantity:e.quantity}),!1})}const ue=g(!1);function Ke(a,l){const t=a.map(e=>{var w;if(r.value.some(f=>f.id===e.id))return!1;const o=(w=l.find(f=>f.code===e.code))==null?void 0:w.quantity,_={...e,index:new Date().getTime()+Math.floor(Math.random()*1e3),uniqueKey:e.id,orderQuantity:e.quantity,quantity:o};return s==="inbound"&&(_.warehouseId=Q),_});ue.value=!0,r.value.push(...t)}function Ae(){W.value=!0,s==="outbound"&&u.value.type===1&&(pe.value=0)}const C=g(!1);async function ge(a,l=!0){if(!a)return;if(u.value.orderId=a.id,U.value=a.orderNo,Qe.value=a.parentOrderId>0,r.value.length>0)try{if(!await S.confirm("当前物料列表中存在已选数据，如果确定，让先清除列表中的数据，是否继续"))return}catch{return}if(r.value=[],l)try{const e=await S.confirm("是否需要自动填充该订单的全部物料","提示",{confirmButtonText:"自动填充",cancelButtonText:"手动输入",type:"warning",showClose:!1});C.value=e==="confirm"}catch{C.value=!1,r.value=[],ne()}const t=a==null?void 0:a.contracts.map(e=>(e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):"",{...e,contractId:e.id,quantity:null,uniqueKey:e.id,orderQuantity:e.quantity,index:new Date().getTime()+Math.floor(Math.random()*1e3)}));y.value=t.map(e=>{const o=e.id;return{...e,value:o,uniqueKey:o,label:`${e.name}`,disabled:C.value}}),C.value&&(s==="outbound"?r.value=t.filter(e=>e.receivedQuantity>0):r.value=t,s==="inbound"&&r.value.length>0&&r.value.forEach(e=>{e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):"",e.warehouseId||(e.warehouseId=86)}))}function ne(a=10){for(let l=0;l<a;l++)he()}async function Le(a){await ge(a),W.value=!1}async function Fe(a){R.value=P(a),await Re(a),j.value=!1}async function Re(a,l=!0){var t;if(u.value.orderId=a.productionScheduleId,u.value.workOrderId=a.id,u.value.workOrder=a.workOrderNo,U.value=a.sn,!a.extras){x.error({message:"该生产订单没有物料"});return}if(r.value.length>0)try{if(!await S.confirm("当前物料列表中存在已选数据，如果确定，让先清除列表中的数据，是否继续"))return}catch{return}if(y.value=[],r.value=[],y.value=(t=a.extras)==null?void 0:t.map(e=>(e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):"",{...e,value:e.id,materialId:e.id,uniqueKey:`${e.id}`,label:`${e.name}`,index:new Date().getTime()+Math.floor(Math.random()*1e3),disabled:r.value.some(o=>o.id===e.id)})),l){try{await S.confirm("是否需要自动填充该订单的全部物料","提示",{confirmButtonText:"自动填充",cancelButtonText:"手动输入",type:"warning",showClose:!1}),C.value=!0}catch{ne(),C.value=!1}C.value&&(s==="outbound"?(r.value=P(y.value),r.value=r.value.filter(e=>e.inventory>0),u.value.type===0&&(r.value.forEach(e=>{e.quantity=e.calcBomQuantity-e.outboundQuantity>e.inventory?e.inventory:e.calcBomQuantity-e.outboundQuantity}),r.value=r.value.filter(e=>e.outboundQuantity!==e.calcBomQuantity))):s==="inbound"&&u.value.type===3&&(r.value=P(y.value),r.value=r.value.filter(e=>e.outboundQuantity>0),r.value.length>0&&r.value.forEach(e=>{e.warehouseId||(e.warehouseId=Q)})))}}async function Ye(a){if(a!==u.value.type){if(r.value.length===0){u.value.type=a;return}try{await S.confirm("切换入库类型后，已选择的物料将被清空，是否继续")&&(u.value.orderId=null,U.value="",u.value.type=a,u.value.orderId=null,r.value=[])}catch{}}}function We(a){console.log("row.total_quantity",a.total_quantity);let l=!1;(a.quantity>a.total_quantity||a.total_quantity==0||!a.total_quantity)&&(x.error({message:"请重新输入物料总量"}),l=!0),l&&(a.total_quantity=null,Z.value=!0)}function Ie(a){let l=!1;if(s==="outbound")[0,2,3,4,5].includes(u.value.type)?a.quantity>a.inventory&&(x.error({message:"出库数量不能大于库存数量"}),l=!0):H.value&&u.value.orderId&&a.quantity>a.receivedQuantity&&(x.error({message:"退货数量不能大于已收数量"}),l=!0);else if(s==="inbound"){if(u.value.type===1||u.value.type===5){const t=xe(a);a.quantity>t&&(x.error({message:"入库数量不能大于可入库数量"}),l=!0)}if(u.value.type===3&&a.quantity>a.outQty&&(x.error({message:"退料数量不能大于领料数量"}),l=!0),u.value.type===4){const t=a.returnOrderQuantity-a.restockingQty;a.quantity>t&&(x.error({message:"入库数量不能大于可入库数量"}),l=!0)}}console.log("hasError",l),l&&(a.quantity=null,Y.value=!0)}function je({row:a}){return!a.id||u.value.type!==1&&u.value.type!==5?"":a.receivedQuantity>=a.orderQuantity-a.transfer?"success-row":""}oe(()=>{var l;const a=(l=r.value)==null?void 0:l.map(t=>t.uniqueKey);y.value=y.value.map(t=>(a.includes(t.uniqueKey)?t.disabled=!0:t.disabled=!1,t)),r.value=r.value.map(t=>(t.address_arr=t.address_name!==""&&t.address_name!==void 0?t.address_name.split(","):[],t.address=t.address?t.address:"",t))}),oe(()=>{He();const a=F.currentRoute.value.query.id,l=F.currentRoute.value.query.orderId;F.currentRoute.value.query.return==="1"&&(u.value.type=1,H.value=!0),a?(V.value=!0,u.value.id=Number.parseInt(a),Ee(a)):l&&_e(l)});function xe(a){let l=0;return a.transfer>0?l=a.orderQuantity-a.transfer-a.receivedQuantity:l=a.orderQuantity-a.receivedQuantity,l>0?ke(l):0}async function Ge(a){var t;const{row:l}=a;if(l.products.length===0)return x.error({message:"该退货单没有物料"});if(u.value.orderId=l.id,u.value.orderId=l.id,r.value.length>0)try{if(!await S.confirm("当前物料列表中存在已选数据，如果确定，让先清除列表中的数据，是否继续"))return}catch{return}y.value=[],r.value=[],y.value=(t=l.products)==null?void 0:t.map(e=>({...e,id:e.materialId,value:e.materialId,materialId:e.materialId,uniqueKey:`${e.materialId}`,label:`${e.name}`,returnOrderQuantity:e.quantity,quantity:0,index:new Date().getTime()+Math.floor(Math.random()*1e3),disabled:r.value.some(o=>o.id===e.id)}));try{await S.confirm("是否需要自动填充该订单的全部物料","提示",{confirmButtonText:"自动填充",cancelButtonText:"手动输入",type:"warning",showClose:!1}),C.value=!0}catch{ne(),C.value=!1}C.value&&(r.value=y.value.filter(e=>e.orderQuantity>0),r.value.length>0&&r.value.forEach(e=>{e.warehouseId||(e.warehouseId=Q)})),r.value.map(e=>{e.address=e.address_name!==""&&e.address_name!==void 0?e.address_name.split(","):""}),J.value=!1}async function He(){try{ve.value=await L.pms.product.group.request({url:"/list",method:"POST"})}catch(a){console.error(a)}}return(a,l)=>{const t=$("el-radio"),e=$("el-radio-group"),o=$("el-form-item"),_=$("el-input"),w=$("el-button"),f=$("el-table-column"),k=$("el-option"),T=$("el-select"),re=$("el-input-number"),Ze=$("el-date-picker"),Je=$("el-table"),Xe=$("el-row"),X=$("el-dialog");return v(),I("div",null,[b("div",fa,[i(p(da),{ref_key:"inboundForm",ref:te,model:u.value,"label-width":"90px",size:"large","status-icon":""},{default:c(()=>[b("div",ha,[ba,i(o,{label:`${p(ae)}类型`,prop:"type"},{label:c(()=>[b("div",_a,[b("span",null,M(`${p(ae)}类型`),1)])]),default:c(()=>[i(e,{"model-value":u.value.type,mr:"50px","onUpdate:modelValue":Ye},{default:c(()=>[p(s)==="inbound"?(v(),I("div",wa,[i(t,{value:2,label:"盘盈调帐",disabled:V.value},null,8,["disabled"]),i(t,{value:3,label:"生产退料",disabled:V.value},null,8,["disabled"]),i(t,{value:0,label:"生产退良品",disabled:V.value},null,8,["disabled"]),i(t,{value:1,label:"采购单入库",disabled:V.value},null,8,["disabled"]),i(t,{value:4,label:"库存退货入库",disabled:V.value},null,8,["disabled"]),i(t,{value:9,label:"补退货",disabled:V.value},null,8,["disabled"])])):h("",!0),p(s)==="outbound"?(v(),I("div",ga,[i(t,{value:0,label:"领料出库",disabled:V.value},null,8,["disabled"]),i(t,{value:1,label:"退货出库",disabled:V.value},null,8,["disabled"]),i(t,{value:4,label:"库存退货出库",disabled:V.value},null,8,["disabled"]),i(t,{value:2,label:"其他领用",disabled:V.value},null,8,["disabled"]),i(t,{value:3,label:"报废",disabled:V.value},null,8,["disabled"]),i(t,{value:5,label:"委外出库",disabled:V.value},null,8,["disabled"])])):h("",!0)]),_:1},8,["model-value"]),u.value.type===1||u.value.type===9?(v(),m(o,{key:0,label:"采购单号",prop:"orderId",pb:"7px"},{default:c(()=>[U.value?(v(),I("div",Ia,M(U.value),1)):(v(),I("div",xa," 请先选择采购单 "))]),_:1})):h("",!0),p(s)==="outbound"&&(u.value.type===0||u.value.type===5)||p(s)==="inbound"&&u.value.type===3?(v(),m(o,{key:1,pb:"7px",label:"生产单号",prop:"orderId"},{default:c(()=>[U.value?(v(),I("div",ka,[b("span",qa,M(U.value),1),b("div",Va,[$a,b("span",Qa,M(R.value.workOrderNo),1)]),b("div",Ma,[Ca,b("span",Sa,M(R.value.productName),1)]),b("div",Ua,[Ta,b("span",Oa,M(R.value.quantity),1)])])):(v(),I("div",za," 请先选择生产订单 "))]),_:1})):h("",!0)]),_:1},8,["label"]),i(o,{label:"备注",prop:"remark",style:{width:"550px"},pb:"7px"},{default:c(()=>[i(_,{modelValue:u.value.remark,"onUpdate:modelValue":l[0]||(l[0]=d=>u.value.remark=d),type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),b("div",Ba,M(`物料${p(ae)}信息`),1),b("div",Da,[p(s)==="inbound"&&u.value.type===0||u.value.type===2||u.value.type===3&&p(s)==="outbound"?(v(),I("div",Na,[i(w,{type:"primary",class:"mb-10px mr-10px",size:"default",onClick:ye},{default:c(()=>[q(" 选择物料 ")]),_:1}),i(de,{onSuccess:Ke})])):h("",!0),p(s)==="outbound"&&(u.value.type===0||u.value.type===5)||p(s)==="inbound"&&u.value.type===3?(v(),I("div",Ea,[i(w,{disabled:u.value.orderId>0&&r.value.length>0,type:"primary",class:"mb-10px mr-10px",size:"default",onClick:ze},{default:c(()=>[q(" 选择工单 ")]),_:1},8,["disabled"]),i(de,{disabled:!u.value.orderId,onSuccess:we},null,8,["disabled"])])):h("",!0),u.value.type===1||u.value.type===9?(v(),I("div",Pa,[i(w,{type:"primary",disabled:u.value.orderId>0&&r.value.length>0,class:"mb-10px mr-10px",size:"default",onClick:Ae},{default:c(()=>[q(" 选择采购单 ")]),_:1},8,["disabled"]),i(de,{disabled:!u.value.orderId,onSuccess:we},null,8,["disabled"])])):h("",!0),p(s)==="outbound"&&u.value.type===4?(v(),I("div",Ka,[i(w,{type:"warning",class:"mb-10px mr-10px",disabled:r.value.length>0,size:"default",onClick:ye},{default:c(()=>[q(" 选择物料 ")]),_:1},8,["disabled"])])):h("",!0),p(s)==="inbound"&&u.value.type===4?(v(),I("div",Aa,[i(w,{type:"warning",class:"mb-10px mr-10px",disabled:r.value.length>0,size:"default",onClick:Ue},{default:c(()=>[q(" 选择退货单 ")]),_:1},8,["disabled"])])):h("",!0),i(w,{type:"danger",disabled:r.value.length===0,class:"mb-10px mr-10px",size:"default",onClick:Ne},{default:c(()=>[q(" 清空列表 ")]),_:1},8,["disabled"])]),i(Je,{data:r.value,style:{width:"100%"},border:"",size:"small","row-class-name":je,"max-height":Math.max(p(se)-680,300)},{default:c(()=>[ue.value?(v(),m(f,{key:0,prop:"=code",label:"物料编码",width:"150",align:"center"},{default:c(d=>{var n;return[q(M(((n=d.row)==null?void 0:n.code)||""),1)]}),_:1})):(v(),m(f,{key:1,prop:"code",label:"选择物料",width:"450",align:"center"},{default:c(d=>[u.value.type===1&&u.value.orderId&&p(s)==="outbound"?(v(),I("div",La,[i(T,{modelValue:d.row.materialId,"onUpdate:modelValue":n=>d.row.materialId=n,filterable:"","reserve-keyword":"",loading:D.value,placeholder:"请选择物料",style:{width:"400px"},size:"small",onChange:Oe},{default:c(()=>[(v(!0),I(N,null,E(y.value,n=>(v(),m(k,{key:n.value,size:"small",disabled:n.disabled,label:`${n.code} / ${n.name} / ${n.model} ${n!=null&&n.po?` / ${n.po}`:""} ${n!=null&&n.supplierName?` / ${n.supplierName}`:""}`,value:n.materialId},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])])):(u.value.type===1||u.value.type===9)&&u.value.orderId&&p(s)==="inbound"?(v(),I("div",Fa,[i(T,{modelValue:d.row.id,"onUpdate:modelValue":n=>d.row.id=n,filterable:"","reserve-keyword":"",loading:D.value,placeholder:"请选择物料",style:{width:"400px"},size:"small",onChange:Te},{default:c(()=>[(v(!0),I(N,null,E(y.value,n=>(v(),m(k,{key:n.value,size:"small",disabled:n.disabled,label:`${n.code} / ${n.name} / ${n.model} ${n!=null&&n.po?` / ${n.po}`:""} ${n!=null&&n.supplierName?` / ${n.supplierName}`:""}`,value:n.value},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])])):p(s)==="outbound"&&(u.value.type===0||u.value.type===5)||p(s)==="inbound"&&u.value.type===3?(v(),I("div",Ra,[i(T,{modelValue:d.row.id,"onUpdate:modelValue":n=>d.row.id=n,filterable:"","reserve-keyword":"",loading:D.value,placeholder:"请选择物料",style:{width:"400px"},size:"small",onChange:fe},{default:c(()=>[(v(!0),I(N,null,E(y.value,n=>(v(),m(k,{key:n.value,size:"small",disabled:n.disabled,label:`${n.code} / ${n.name} / ${n.model} ${n!=null&&n.po?` / ${n.po}`:""} ${n!=null&&n.supplierName?` / ${n.supplierName}`:""}`,value:n.value},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])])):(v(),I("div",Ya,[i(T,{modelValue:d.row.id,"onUpdate:modelValue":n=>d.row.id=n,size:"small",filterable:"","reserve-keyword":"",loading:D.value,placeholder:"请选择物料",style:{width:"400px"},remote:"","remote-method":Se,onChange:fe},{default:c(()=>[(v(!0),I(N,null,E(y.value,n=>(v(),m(k,{key:n.value,disabled:n.disabled,label:`${n.code} / ${n.name} / ${n.model}`,value:n.value},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])]))]),_:1})),i(f,{prop:"quantity",label:"*数量",align:"center",width:"150"},{header:c(()=>[Wa,q(" 数量 ")]),default:c(d=>[p(s)==="outbound"&&(u.value.type===0||u.value.type===5)?(v(),I("div",{key:0,class:ie(Y.value&&!(d.row.quantity>0)?"quantity-input-error":"")},[i(re,{modelValue:d.row.quantity,"onUpdate:modelValue":n=>d.row.quantity=n,min:0,placeholder:"请输入物料数量",size:"small",precision:4,onChange:n=>Ie(d.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])],2)):(v(),I("div",{key:1,class:ie(Y.value&&!(d.row.quantity>0)?"quantity-input-error":"")},[i(re,{modelValue:d.row.quantity,"onUpdate:modelValue":n=>d.row.quantity=n,min:p(s)==="inbound"&&u.value.type===2?void 0:0,placeholder:"请输入物料数量",size:"small",precision:4,onChange:n=>Ie(d.row)},null,8,["modelValue","onUpdate:modelValue","min","onChange"])],2))]),_:1}),p(s)==="outbound"&&u.value.type===3?(v(),m(f,{key:2,prop:"total_quantity",align:"center",width:"160"},{header:c(()=>[ja,q(" 物料总量 ")]),default:c(d=>[b("div",{class:ie(Z.value?"quantity-input-error":"")},[i(re,{modelValue:d.row.total_quantity,"onUpdate:modelValue":n=>d.row.total_quantity=n,min:0,placeholder:"请输入物料总量",size:"small",precision:4,onChange:n=>We(d.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])],2)]),_:1})):h("",!0),p(s)==="outbound"?(v(),m(f,{key:3,prop:"remark",label:"备注",align:"center",width:"150"},{header:c(()=>[q(" 备注 ")]),default:c(d=>[b("div",null,[i(_,{modelValue:d.row.remark,"onUpdate:modelValue":n=>d.row.remark=n,size:"small",placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])])]),_:1})):h("",!0),p(s)==="outbound"&&u.value.type===3?(v(),m(f,{key:4,prop:"scrap_date",label:"报废日期",align:"center",width:"150"},{default:c(d=>[b("div",null,[i(Ze,{modelValue:d.row.scrap_date,"onUpdate:modelValue":n=>d.row.scrap_date=n,type:"date",placeholder:"选择报废日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",size:"small",style:{width:"140px"}},null,8,["modelValue","onUpdate:modelValue"])])]),_:1})):h("",!0),p(s)==="outbound"&&u.value.type===3?(v(),m(f,{key:5,prop:"responsible",label:"责任人",align:"center",width:"150"},{header:c(()=>[q(" 责任人 ")]),default:c(d=>[b("div",null,[i(_,{modelValue:d.row.responsible,"onUpdate:modelValue":n=>d.row.responsible=n,size:"small",placeholder:"请输入责任人",type:"textarea"},null,8,["modelValue","onUpdate:modelValue"])])]),_:1})):h("",!0),p(s)==="outbound"&&u.value.type===3?(v(),m(f,{key:6,prop:"handling_method",label:"处理方式",align:"center",width:"160"},{default:c(d=>[b("div",null,[i(_,{modelValue:d.row.handling_method,"onUpdate:modelValue":n=>d.row.handling_method=n,placeholder:"请输入处理方式",size:"small",type:"textarea"},null,8,["modelValue","onUpdate:modelValue"])])]),_:1})):h("",!0),p(s)==="outbound"&&u.value.type===3?(v(),m(f,{key:7,prop:"product_group_id",label:"机型",align:"center",width:"220"},{default:c(d=>[b("div",null,[i(T,{modelValue:d.row.product_group_id,"onUpdate:modelValue":n=>d.row.product_group_id=n,placeholder:"选择机型",size:"small",filterable:""},{default:c(()=>[i(k,{key:"0",value:0,label:"请选择机型"}),(v(!0),I(N,null,E(ve.value,n=>(v(),m(k,{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])]),_:1})):h("",!0),p(s)==="inbound"&&u.value.type===4?(v(),m(f,{key:8,prop:"returnOrderQuantity",label:"退货数量",align:"center",width:"100"})):h("",!0),p(s)==="inbound"&&u.value.type===4?(v(),m(f,{key:9,prop:"restockingQty",label:"已补货数量",align:"center",width:"100"})):h("",!0),p(s)==="inbound"&&u.value.type===4?(v(),m(f,{key:10,prop:"",label:"剩余数量",align:"center",width:"120"},{default:c(({row:d})=>[q(M(d.returnOrderQuantity-d.restockingQty),1)]),_:1})):h("",!0),p(s)==="inbound"?(v(),m(f,{key:11,prop:"quantity",label:"仓位",align:"center",width:"100"},{default:c(d=>[i(qe,{modelValue:d.row.warehouseId,"onUpdate:modelValue":n=>d.row.warehouseId=n,code:"warehouse_name",size:"small",width:"100px"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})):h("",!0),i(f,{prop:"inbound_outbound_key",label:"关键字",align:"center",width:"120"},{default:c(d=>[i(qe,{modelValue:d.row.inbound_outbound_key,"onUpdate:modelValue":n=>d.row.inbound_outbound_key=n,code:"inbound_outbound_key",size:"small",width:"100px"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(f,{label:"位置",align:"left",width:"220","show-overflow-tooltip":""},{default:c(d=>[b("div",Ga,[i(T,{modelValue:d.row.address,"onUpdate:modelValue":n=>d.row.address=n,filterable:"",multiple:"",size:"small",style:{width:"220px"},placeholder:"请选择位置"},{default:c(()=>[(v(!0),I(N,null,E(d.row.address_arr,(n,ea)=>(v(),m(k,{key:ea,label:n,value:n},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])]),_:1}),p(s)==="inbound"&&u.value.type===1?(v(),m(f,{key:12,prop:"availableQuantity",label:"可入库",width:"80",align:"center","show-overflow-tooltip":""},{default:c(d=>[q(M(xe(d.row)),1)]),_:1})):h("",!0),u.value.type===1?(v(),m(f,{key:13,prop:"receivedQuantity",label:"已收数量",width:"80",align:"center"},{default:c(d=>{var n;return[q(M(((n=d.row)==null?void 0:n.receivedQuantity)||0),1)]}),_:1})):h("",!0),u.value.type===3&&p(s)==="inbound"?(v(),m(f,{key:14,prop:"outQty",label:"可退数量",align:"center",width:"80"})):h("",!0),u.value.type===1?(v(),m(f,{key:15,prop:"po",label:"PO",align:"left",width:"180","show-overflow-tooltip":""})):h("",!0),p(s)==="outbound"?(v(),m(f,{key:16,prop:"inventory",label:"库存",width:"80",align:"center","show-overflow-tooltip":""})):h("",!0),p(s)==="outbound"&&(u.value.type===0||u.value.type===5)?(v(),m(f,{key:17,prop:"calcBomQuantity",label:"Bom用量",width:"80",align:"center","show-overflow-tooltip":""})):h("",!0),p(s)==="outbound"&&(u.value.type===0||u.value.type===5)?(v(),m(f,{key:18,prop:"outboundQuantity",label:"已出库",width:"80",align:"center","show-overflow-tooltip":""})):h("",!0),p(s)==="outbound"&&(u.value.type===0||u.value.type===5)?(v(),m(f,{key:19,prop:"",label:"待发货数量",align:"center",width:"120"},{default:c(({row:d})=>[q(M(d.calcBomQuantity&&(d.outboundQuantity||d.outboundQuantity===0)?(d.calcBomQuantity-d.outboundQuantity).toFixed(4):""),1)]),_:1})):h("",!0),u.value.type===1?(v(),m(f,{key:20,prop:"supplierName",label:"供应商",align:"left",width:"150","show-overflow-tooltip":""})):h("",!0),i(la,{"auto-width":"","show-code":!1}),i(f,{prop:"unit",label:"单位",align:"center",width:"80","show-overflow-tooltip":""}),i(f,{label:"操作",width:"120",align:"center"},{default:c(d=>[i(w,{type:"danger",size:"small",onClick:n=>De(d.row.index)},{default:c(()=>[q(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","max-height"]),ue.value?h("",!0):(v(),m(w,{key:0,disabled:!u.value.orderId&&(u.value.type===1||u.value.type===9)||p(s)==="outbound"&&(u.value.type===0||u.value.type===5)&&!u.value.orderId,style:{width:"100%"},size:"small",class:"btn-material-add",onClick:he},{default:c(()=>[q(" + 添加物料 ")]),_:1},8,["disabled"])),i(Xe,{flex:"~ 1 items-center justify-end"},{default:c(()=>[i(w,{type:"success",style:{"margin-top":"20px"},loading:z.value,onClick:Ce},{default:c(()=>[q(" 保存为草稿 ")]),_:1},8,["loading"])]),_:1})])]),_:1},8,["model"])]),i(X,{modelValue:B.value,"onUpdate:modelValue":l[3]||(l[3]=d=>B.value=d),title:"选择物料",width:"80%"},{footer:c(()=>[b("span",Ha,[i(w,{onClick:l[2]||(l[2]=d=>B.value=!1)},{default:c(()=>[q("取消")]),_:1}),i(w,{type:"primary",onClick:Be},{default:c(()=>[q(" 确认选择 ")]),_:1})])]),default:c(()=>[B.value?(v(),m(aa,{key:0,ref_key:"materialSelector",ref:me,modelValue:G.value,"onUpdate:modelValue":l[1]||(l[1]=d=>G.value=d),"has-stock":p(s)==="outbound"},null,8,["modelValue","has-stock"])):h("",!0)]),_:1},8,["modelValue"]),i(X,{modelValue:W.value,"onUpdate:modelValue":l[4]||(l[4]=d=>W.value=d),title:"选择采购单",width:"80%",height:"600"},{default:c(()=>[W.value?(v(),m(ya,{key:0,status:pe.value,onSelected:Le},null,8,["status"])):h("",!0)]),_:1},8,["modelValue"]),i(X,{modelValue:j.value,"onUpdate:modelValue":l[5]||(l[5]=d=>j.value=d),title:"选择工单",width:"80%",height:"600"},{default:c(()=>[j.value?(v(),m(va,{key:0,"table-op":Me.value,status:p(Ve),onSelected:Fe},null,8,["table-op","status"])):h("",!0)]),_:1},8,["modelValue"]),i(X,{modelValue:J.value,"onUpdate:modelValue":l[6]||(l[6]=d=>J.value=d),title:"选择退货单",width:"80%",height:"600"},{default:c(()=>[i(sa,{onSelected:Ge})]),_:1},8,["modelValue"])])}}}),cl=pa(Ja,[["__scopeId","data-v-f99731ef"]]);export{cl as I};
