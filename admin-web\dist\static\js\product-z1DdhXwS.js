import{g as be,i as D,e as Z}from"./index-BuqCFB-b.js";import{u as ge}from"./table-ops-B_rNWTRT.js";import{a as ve}from"./index-BAHxID_w.js";import{c as ee,b as f,a0 as ke,q as L,w as r,h as n,y as v,j as s,i,f as M,s as R,F as j,E as _,M as we,N as ye,o as C,t as Ve}from"./.pnpm-Kv7TmmH8.js";const Ue={class:"title"},xe=v("div",{class:"title"}," 2. 在表格中按照模板格式填写数据 ",-1),Se={class:"title"},Ee={class:"title mb20px!"},Ie=v("br",null,null,-1),$e=v("br",null,null,-1),Ce=v("br",null,null,-1),Pe=v("br",null,null,-1),qe=v("br",null,null,-1),Fe=ee({name:"pms-products"}),De=ee({...Fe,setup(Ne){var X,Y;const{dict:te}=be(),{service:g}=ve(),P=f([]),K=f([{label:"无分组",value:0}]),G=f([]),I=f(void 0),W=[{label:"单品",value:0,name:"-",nameEn:"-",type:"info"},{label:"内盒/展示盒",value:1,name:"展示盒",nameEn:"Inner Box",type:"warning"},{label:"箱装",value:2,name:"箱装",nameEn:"Outer Box",type:"success"}],T=f(!1);async function le(){G.value=await g.pms.product.group.list({}).then(e=>e.map(t=>({value:t.id,label:`${t.name}/${t.nameEn}`}))),I.value=void 0,T.value=!0}const U=te.get("color");(X=U.value)!=null&&X.find(e=>e.value===0)||(Y=U.value)==null||Y.unshift({label:"无",value:0});const h=D.useUpsert({props:{class:"product-form"},items:[{prop:"unit",value:0,label:"单位类型",class:"full-line",required:!0,component:{name:"slot-product-unit"}},{label:"包含产品",prop:"unitProductId",required:!0,hidden:({scope:e})=>e.unit===0,component:{name:"slot-product-select"}},{label:"单位数量",prop:"unitQuantity",required:!0,hidden:({scope:e})=>e.unit===0,component:{name:"el-input-number",props:{min:1}}},{label:"产品名称",prop:"name",class:"full-line",required:!0,component:{name:"el-input"}},{label:"英文名称",prop:"nameEn",class:"full-line",required:!0,component:{name:"el-input"}},{label:"产品分组",prop:"groupId",required:!0,component:{name:"el-select",props:{filterable:!0},options:K}},{label:"SKU",prop:"sku",hook:{submit:e=>e.trim()},required:!0,component:{name:"el-input"}},{label:"UPC",prop:"upc",required:!0,component:{name:"el-input"}},{label:"颜色",prop:"color",required:!0,component:{name:"el-select",props:{filterable:!0},options:U}},{label:"长度",prop:"length",required:!0,component:{name:"slot-input-length"}},{label:"宽度",prop:"width",required:!0,component:{name:"slot-input-width"}},{label:"高度",prop:"height",required:!0,component:{name:"slot-input-height"}},{label:"净量",prop:"weight",required:!0,component:{name:"slot-input-weight"}},{label:"毛重",prop:"grossWeight",required:!0,component:{name:"slot-input-gross-weight"}}],onInfo:async(e,{done:t})=>{e.unitProductId>0&&await g.pms.product.info({id:e.unitProductId}).then(l=>{l&&(P.value=[{value:l.id,label:`${l.sku} ${l.name}`,name:`${l.name}`,nameEn:`${l.nameEn}`,color:l.color}])}),ke(()=>{t(e)})},onOpen:()=>{g.pms.product.group.list({}).then(e=>{K.value.splice(1);const t=e.map(l=>({value:l.id,label:`${l.name}/${l.nameEn}`}));K.value.push(...t)})}}),H=f({edit:{width:80,permission:g.pms.product.permission.update,show:!0},delete:{width:80,permission:g.pms.product.permission.delete,show:!0}}),{getOpWidth:oe,getOpIsHidden:ne}=ge(H),ae=f(oe()),re=f(ne()),ue=D.useTable({columns:[{label:"ID",prop:"id",width:80},{label:"SKU",prop:"sku",width:150},{label:"UPC",prop:"upc",width:150},{label:"名称",children:[{label:"中文名称",prop:"name",minWidth:200,showOverflowTooltip:!0},{label:"英文名称",prop:"nameEn",minWidth:200,showOverflowTooltip:!0}]},{label:"颜色",showOverflowTooltip:!0,prop:"color",width:110,formatter:e=>{var t,l;return((l=(t=U.value)==null?void 0:t.find(m=>m.value===Number.parseInt(e.color)))==null?void 0:l.label)||"-"}},{label:"库存信息",children:[{label:"可用",prop:"stock",width:100,formatter:e=>{var t,l;return((t=e.stock)==null?void 0:t.inStock)-((l=e.stock)==null?void 0:l.freezeStock)||0}},{label:"冻结",prop:"freezeStock",width:100,formatter:e=>{var t;return((t=e.stock)==null?void 0:t.freezeStock)||0}},{label:"生产中",prop:"productionStock",width:100,formatter:e=>{var t;return((t=e.stock)==null?void 0:t.productionStock)||0}},{label:"待入库",prop:"waitInboundStock",width:100,formatter:e=>{var t;return((t=e.stock)==null?void 0:t.waitInboundStock)||0}}]},{label:"包装产品信息",children:[{label:"包装单位",prop:"unit",width:100,dict:W},{label:"包装产品",prop:"unitProductSku",width:100,formatter:e=>e.unitProductSku||"-"},{label:"单位数量",prop:"quantity",formatter:e=>e.unit===0?"-":e.unitQuantity,width:100}]},{label:"长cm",prop:"length",width:70},{label:"宽cm",prop:"width",width:70},{label:"高cm",prop:"height",width:70},{label:"净重kg",prop:"weight",width:70},{label:"毛量kg",prop:"grossWeight",width:70},{label:"创建时间",prop:"createTime",width:160},{type:"op",label:"操作",width:ae,hidden:re,buttons:Object.keys(H.value)}]}),Q=f(0),q=D.useCrud({service:g.pms.product},e=>{e.refresh({unit:Q.value})}),se=D.useSearch({items:[{label:"包装类型",prop:"unit",props:{labelWidth:"80px"},value:Q.value,component:{name:"el-select",props:{style:"width: 120px",clearable:!1,onChange(e){var t;(t=q.value)==null||t.refresh({unit:e,page:1})}},options:W}},{label:"SKU/UPC/名称",prop:"keyWord",props:{labelWidth:"120px"},component:{name:"el-input",props:{clearable:!1,onChange(e){var t;(t=q.value)==null||t.refresh({keyWord:e.trim(),page:1})}}}}]});function ie(e){if(e)return g.pms.product.list({keyWord:e}).then(t=>{var p,b;const l=((p=h.value)==null?void 0:p.getForm("unit"))||3,m=((b=h.value)==null?void 0:b.getForm("sku"))||"";P.value=t==null?void 0:t.filter(u=>u.unit<l&&u.sku!==m).map(u=>({value:u.id,label:`${u.sku} ${u.name}`,name:u.name,nameEn:u.nameEn,color:u.color}))})}function ce(){var t,l;P.value=[];const e=(t=h.value)==null?void 0:t.getForm("unitProductId");(e===0||!e)&&((l=h.value)==null||l.setForm("unitProductId",""))}function pe(e){var m,p,b,u,x,$;const t=P.value.find(S=>S.value===e);if(!t)return;const l=W.find(S=>{var y;return S.value===((y=h.value)==null?void 0:y.getForm("unit"))});(m=h.value)!=null&&m.getForm("name")||(p=h.value)==null||p.setForm("name",`${t.name} ${l==null?void 0:l.name}`),(b=h.value)!=null&&b.getForm("nameEn")||(u=h.value)==null||u.setForm("nameEn",`${t.nameEn} ${l==null?void 0:l.nameEn}`),(x=h.value)!=null&&x.getForm("color")||($=h.value)==null||$.setForm("color",Number.parseInt(t.color))}function F(e){return/^(\d+)((?:\.\d{0,4})?)$/.test(e)?e:e.slice(0,-1)}const J=f(null),k=f(!1);async function de(e){if(I.value==0||I.value==null){_.error("请选择分组");return}const t={};U&&U.value.length>0&&U.value.forEach(u=>{t[u.label]=u.value});const l={},m=await g.pms.product.getAllProduct();m&&m.length>0&&m.forEach(u=>{l[u.sku]={sku:u.sku,upc:u.upc,src:"db"}});const p=e.target,b=p.files;if(b&&b.length>0){k.value=!0;const u=b[0],x=new FileReader;x.onload=$=>{var O;const S=new Uint8Array((O=$.target)==null?void 0:O.result),y=we(S,{type:"array"}),z=y.Sheets[y.SheetNames[0]],V=ye.sheet_to_json(z,{header:1}),N=[void 0,null,"","undefined","null","NaN"],A=["sku","upc","name","nameEn","color","length","width","height","weight","grossWeight"],w=[];if(V&&V.length>0){for(let d=1;d<V.length;d++){const E=V[d],c={};for(let a=0;a<E.length;a++){const _e=A[a];c.group_id=I.value,c[_e]=(E[a].toString()||"").trim(),c.color=t[c.color]?t[c.color]:0,c.unit=0}if(N.includes(c.sku)){_.error(`第${d}行SKU不能为空`),B(p),k.value=!1;break}if(N.includes(c.name)){_.error(`第${d}行名称不能为空`),B(p),k.value=!1;break}const o=l[c.sku];if(o&&o.sku&&!N.includes(o)&&Number.isNaN(o)){o.src&&o.src==="db"?_.error(`第${d}行物料编码${o.sku}已经存在`):_.error(`第${d}行物料编码${o.sku}与第${o.rowNo}物料编号${o.sku}重复`),B(p),k.value=!1;break}else l[c.sku]={sku:c.sku,rowNo:d,data:c};w.push(c)}w.length>0?g.pms.product.import_data({product_list:w}).then(d=>{var E;(E=q.value)==null||E.refresh(),_.success(`导出成功：导入${d}条数据！`)}).catch(d=>{_.error(d.message||"导入失败")}).finally(()=>{k.value=!1}).finally(()=>{k.value=!1}):(k.value=!1,_.error("导入数据为空")),B(p)}},x.readAsArrayBuffer(u),T.value=!1}else k.value=!1,_.error("请选择文件")}f(!1);function me(){const e=J.value;e&&e.click()}function B(e){e&&(e.value="")}function he(){const e="导入产品模板.xlsx";fetch("/product.xlsx").then(l=>l.blob()).then(l=>{Z(l,e)}).catch(()=>{_.error({message:"下载模板文件失败"})})}function fe(){const e={url:"/exportProduct",method:"GET",responseType:"blob"};g.pms.product.request(e).then(t=>{var l;Z(t)&&_.success("导出成功"),(l=q.value)==null||l.refresh()}).catch(t=>{_.error(t.message||"导出失败")})}return(e,t)=>{const l=i("el-button"),m=i("el-option"),p=i("el-select"),b=i("el-text"),u=i("cl-dialog"),x=i("cl-refresh-btn"),$=i("cl-add-btn"),S=i("cl-multi-delete-btn"),y=i("cl-flex1"),z=i("cl-search"),V=i("el-row"),N=i("cl-table"),A=i("cl-pagination"),w=i("el-input"),O=i("el-radio"),d=i("el-radio-group"),E=i("cl-upsert"),c=i("cl-crud");return C(),L(c,{ref_key:"Crud",ref:q},{default:r(()=>[n(u,{modelValue:T.value,"onUpdate:modelValue":t[1]||(t[1]=o=>T.value=o),title:"导入产品","close-on-click-modal":!1,controls:["close"],width:"800",class:"import-step-dialog"},{default:r(()=>[v("div",Ue,[s(" 1. "),n(l,{type:"primary",link:"",onClick:he},{default:r(()=>[s(" 下载Excel模板 ")]),_:1})]),xe,v("div",Se,[s(" 3. 选择产品分组 "),n(p,{modelValue:I.value,"onUpdate:modelValue":t[0]||(t[0]=o=>I.value=o),placeholder:"请选择分组",size:"small",filterable:"",style:{width:"240px","margin-left":"20px"}},{default:r(()=>[(C(!0),M(j,null,R(G.value,o=>(C(),L(m,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),v("div",Ee,[s(" 4. "),n(l,{loading:k.value,link:"",type:"success",onClick:me},{default:r(()=>[s(" 点击选择文件导入数据 ")]),_:1},8,["loading"])]),n(b,{type:"danger"},{default:r(()=>[s(" 注意事项： "),Ie,s(" 1. 如果导入的产品已经存在SKU或UPC，将会报错 "),$e,s(" 2. 如果系统中不存在表格中的颜色，请创建颜色 "),Ce,s(" 3. 如果不选择分组，将会默认为无分组 "),Pe,s(" 4. 目前只支持单品上传 "),qe]),_:1})]),_:1},8,["modelValue"]),n(V,null,{default:r(()=>[n(x),n($),n(S),v("input",{ref_key:"fileInputRef",ref:J,type:"file",style:{display:"none"},accept:".xlsx, .xls",onChange:de},null,544),n(l,{type:"success",onClick:le,style:{"margin-left":"20px"}},{default:r(()=>[s(" 导入 ")]),_:1}),n(l,{type:"warning",onClick:fe,style:{"margin-left":"20px"}},{default:r(()=>[s(" 导出 ")]),_:1}),n(y),n(z,{ref_key:"Search",ref:se},null,512)]),_:1}),n(V,null,{default:r(()=>[n(N,{ref_key:"Table",ref:ue},null,512)]),_:1}),n(V,null,{default:r(()=>[n(y),n(A)]),_:1}),n(E,{ref_key:"Upsert",ref:h},{"slot-input-length":r(({scope:o})=>[n(w,{modelValue:o.length,"onUpdate:modelValue":a=>o.length=a,type:"number",placeholder:"请输入长度",formatter:F},{append:r(()=>[s(" cm ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-input-width":r(({scope:o})=>[n(w,{modelValue:o.width,"onUpdate:modelValue":a=>o.width=a,type:"number",placeholder:"请输入宽度",formatter:F},{append:r(()=>[s(" cm ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-input-height":r(({scope:o})=>[n(w,{modelValue:o.height,"onUpdate:modelValue":a=>o.height=a,type:"number",placeholder:"请输入高度",formatter:F},{append:r(()=>[s(" cm ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-input-weight":r(({scope:o})=>[n(w,{modelValue:o.weight,"onUpdate:modelValue":a=>o.weight=a,type:"number",placeholder:"请输入净量",formatter:F},{append:r(()=>[s(" kg ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-input-gross-weight":r(({scope:o})=>[n(w,{modelValue:o.grossWeight,"onUpdate:modelValue":a=>o.grossWeight=a,type:"number",placeholder:"请输入毛重",formatter:F},{append:r(()=>[s(" kg ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-product-select":r(({scope:o})=>[n(p,{modelValue:o.unitProductId,"onUpdate:modelValue":a=>o.unitProductId=a,filterable:"",remote:"",placeholder:"输入产品SKU/UPC查询","remote-method":ie,onChange:pe},{default:r(()=>[(C(!0),M(j,null,R(P.value,a=>(C(),L(m,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),"slot-product-unit":r(({scope:o})=>[n(d,{modelValue:o.unit,"onUpdate:modelValue":a=>o.unit=a,onChange:ce},{default:r(()=>[(C(),M(j,null,R(W,a=>n(O,{key:a.value,label:a.value},{default:r(()=>[s(Ve(a.label),1)]),_:2},1032,["label"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1},512)]),_:1},512)}}});export{De as default};
