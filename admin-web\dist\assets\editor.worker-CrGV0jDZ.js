(function(){"use strict";class rr{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(t){setTimeout(()=>{throw t.stack?de.isErrorNoTelemetry(t)?new de(t.message+`

`+t.stack):new Error(t.message+`

`+t.stack):t},0)}}emit(t){this.listeners.forEach(n=>{n(t)})}onUnexpectedError(t){this.unexpectedErrorHandler(t),this.emit(t)}onUnexpectedExternalError(t){this.unexpectedErrorHandler(t)}}const sr=new rr;function Lt(e){ir(e)||sr.onUnexpectedError(e)}function vt(e){if(e instanceof Error){const{name:t,message:n}=e,r=e.stacktrace||e.stack;return{$isError:!0,name:t,message:n,stack:r,noTelemetry:de.isErrorNoTelemetry(e)}}return e}const $e="Canceled";function ir(e){return e instanceof ar?!0:e instanceof Error&&e.name===$e&&e.message===$e}class ar extends Error{constructor(){super($e),this.name=this.message}}class de extends Error{constructor(t){super(t),this.name="CodeExpectedError"}static fromError(t){if(t instanceof de)return t;const n=new de;return n.message=t.message,n.stack=t.stack,n}static isErrorNoTelemetry(t){return t.name==="CodeExpectedError"}}class ze extends Error{constructor(t){super(t||"An unexpected bug occurred."),Object.setPrototypeOf(this,ze.prototype);debugger}}function lr(e){const t=this;let n=!1,r;return function(){return n||(n=!0,r=e.apply(t,arguments)),r}}var Re;(function(e){function t(g){return g&&typeof g=="object"&&typeof g[Symbol.iterator]=="function"}e.is=t;const n=Object.freeze([]);function r(){return n}e.empty=r;function*s(g){yield g}e.single=s;function a(g){return t(g)?g:s(g)}e.wrap=a;function l(g){return g||n}e.from=l;function o(g){return!g||g[Symbol.iterator]().next().done===!0}e.isEmpty=o;function c(g){return g[Symbol.iterator]().next().value}e.first=c;function u(g,S){for(const L of g)if(S(L))return!0;return!1}e.some=u;function f(g,S){for(const L of g)if(S(L))return L}e.find=f;function*h(g,S){for(const L of g)S(L)&&(yield L)}e.filter=h;function*d(g,S){let L=0;for(const P of g)yield S(P,L++)}e.map=d;function*m(...g){for(const S of g)for(const L of S)yield L}e.concat=m;function b(g,S,L){let P=L;for(const k of g)P=S(P,k);return P}e.reduce=b;function*C(g,S,L=g.length){for(S<0&&(S+=g.length),L<0?L+=g.length:L>g.length&&(L=g.length);S<L;S++)yield g[S]}e.slice=C;function N(g,S=Number.POSITIVE_INFINITY){const L=[];if(S===0)return[L,g];const P=g[Symbol.iterator]();for(let k=0;k<S;k++){const G=P.next();if(G.done)return[L,e.empty()];L.push(G.value)}return[L,{[Symbol.iterator](){return P}}]}e.consume=N})(Re||(Re={}));function ai(e){return e}function li(e,t){}function Nt(e){if(Re.is(e)){const t=[];for(const n of e)if(n)try{n.dispose()}catch(r){t.push(r)}if(t.length===1)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function or(...e){return ke(()=>Nt(e))}function ke(e){return{dispose:lr(()=>{e()})}}class oe{constructor(){this._toDispose=new Set,this._isDisposed=!1}dispose(){this._isDisposed||(this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(this._toDispose.size!==0)try{Nt(this._toDispose)}finally{this._toDispose.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return this._isDisposed?oe.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(t),t}}oe.DISABLE_DISPOSED_WARNING=!1;class Me{constructor(){this._store=new oe,this._store}dispose(){this._store.dispose()}_register(t){if(t===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(t)}}Me.None=Object.freeze({dispose(){}});class ur{constructor(){this.dispose=()=>{},this.unset=()=>{},this.isset=()=>!1}set(t){let n=t;return this.unset=()=>n=void 0,this.isset=()=>n!==void 0,this.dispose=()=>{n&&(n(),n=void 0)},this}}class D{constructor(t){this.element=t,this.next=D.Undefined,this.prev=D.Undefined}}D.Undefined=new D(void 0);class Pe{constructor(){this._first=D.Undefined,this._last=D.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===D.Undefined}clear(){let t=this._first;for(;t!==D.Undefined;){const n=t.next;t.prev=D.Undefined,t.next=D.Undefined,t=n}this._first=D.Undefined,this._last=D.Undefined,this._size=0}unshift(t){return this._insert(t,!1)}push(t){return this._insert(t,!0)}_insert(t,n){const r=new D(t);if(this._first===D.Undefined)this._first=r,this._last=r;else if(n){const a=this._last;this._last=r,r.prev=a,a.next=r}else{const a=this._first;this._first=r,r.next=a,a.prev=r}this._size+=1;let s=!1;return()=>{s||(s=!0,this._remove(r))}}shift(){if(this._first!==D.Undefined){const t=this._first.element;return this._remove(this._first),t}}pop(){if(this._last!==D.Undefined){const t=this._last.element;return this._remove(this._last),t}}_remove(t){if(t.prev!==D.Undefined&&t.next!==D.Undefined){const n=t.prev;n.next=t.next,t.next.prev=n}else t.prev===D.Undefined&&t.next===D.Undefined?(this._first=D.Undefined,this._last=D.Undefined):t.next===D.Undefined?(this._last=this._last.prev,this._last.next=D.Undefined):t.prev===D.Undefined&&(this._first=this._first.next,this._first.prev=D.Undefined);this._size-=1}*[Symbol.iterator](){let t=this._first;for(;t!==D.Undefined;)yield t.element,t=t.next}}let cr=typeof document<"u"&&document.location&&document.location.hash.indexOf("pseudo=true")>=0;function hr(e,t){let n;return t.length===0?n=e:n=e.replace(/\{(\d+)\}/g,(r,s)=>{const a=s[0],l=t[a];let o=r;return typeof l=="string"?o=l:(typeof l=="number"||typeof l=="boolean"||l===void 0||l===null)&&(o=String(l)),o}),cr&&(n="［"+n.replace(/[aouei]/g,"$&$&")+"］"),n}function fr(e,t,...n){return hr(t,n)}function oi(e){}var Oe;const Ne="en";let Ge=!1,je=!1,Xe=!1,St=!1,De,Qe=Ne,dr,Q;const U=typeof self=="object"?self:typeof global=="object"?global:{};let I;typeof U.vscode<"u"&&typeof U.vscode.process<"u"?I=U.vscode.process:typeof process<"u"&&(I=process);const mr=typeof((Oe=I==null?void 0:I.versions)===null||Oe===void 0?void 0:Oe.electron)=="string"&&(I==null?void 0:I.type)==="renderer";if(typeof navigator=="object"&&!mr)Q=navigator.userAgent,Ge=Q.indexOf("Windows")>=0,je=Q.indexOf("Macintosh")>=0,(Q.indexOf("Macintosh")>=0||Q.indexOf("iPad")>=0||Q.indexOf("iPhone")>=0)&&navigator.maxTouchPoints&&navigator.maxTouchPoints>0,Xe=Q.indexOf("Linux")>=0,(Q==null?void 0:Q.indexOf("Mobi"))>=0,St=!0,fr({},"_"),De=Ne,Qe=De;else if(typeof I=="object"){Ge=I.platform==="win32",je=I.platform==="darwin",Xe=I.platform==="linux",Xe&&I.env.SNAP&&I.env.SNAP_REVISION,I.env.CI||I.env.BUILD_ARTIFACTSTAGINGDIRECTORY,De=Ne,Qe=Ne;const e=I.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e),n=t.availableLanguages["*"];De=t.locale,Qe=n||Ne,dr=t._translationsConfigFile}catch{}}else console.error("Unable to resolve platform.");const Se=Ge,gr=je;St&&U.importScripts;const Z=Q,br=typeof U.postMessage=="function"&&!U.importScripts;(()=>{if(br){const e=[];U.addEventListener("message",n=>{if(n.data&&n.data.vscodeScheduleAsyncWork)for(let r=0,s=e.length;r<s;r++){const a=e[r];if(a.id===n.data.vscodeScheduleAsyncWork){e.splice(r,1),a.callback();return}}});let t=0;return n=>{const r=++t;e.push({id:r,callback:n}),U.postMessage({vscodeScheduleAsyncWork:r},"*")}}return e=>setTimeout(e)})();const _r=!!(Z&&Z.indexOf("Chrome")>=0);Z&&Z.indexOf("Firefox")>=0,!_r&&Z&&Z.indexOf("Safari")>=0,Z&&Z.indexOf("Edg/")>=0,Z&&Z.indexOf("Android")>=0;const pr=U.performance&&typeof U.performance.now=="function";class Ee{static create(t=!0){return new Ee(t)}constructor(t){this._highResolution=pr&&t,this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}elapsed(){return this._stopTime!==-1?this._stopTime-this._startTime:this._now()-this._startTime}_now(){return this._highResolution?U.performance.now():Date.now()}}var Ye;(function(e){e.None=()=>Me.None;function t(w,_){return f(w,()=>{},0,void 0,!0,void 0,_)}e.defer=t;function n(w){return(_,v=null,x)=>{let p=!1,R;return R=w(F=>{if(!p)return R?R.dispose():p=!0,_.call(v,F)},null,x),p&&R.dispose(),R}}e.once=n;function r(w,_,v){return u((x,p=null,R)=>w(F=>x.call(p,_(F)),null,R),v)}e.map=r;function s(w,_,v){return u((x,p=null,R)=>w(F=>{_(F),x.call(p,F)},null,R),v)}e.forEach=s;function a(w,_,v){return u((x,p=null,R)=>w(F=>_(F)&&x.call(p,F),null,R),v)}e.filter=a;function l(w){return w}e.signal=l;function o(...w){return(_,v=null,x)=>or(...w.map(p=>p(R=>_.call(v,R),null,x)))}e.any=o;function c(w,_,v,x){let p=v;return r(w,R=>(p=_(p,R),p),x)}e.reduce=c;function u(w,_){let v;const x={onWillAddFirstListener(){v=w(p.fire,p)},onDidRemoveLastListener(){v==null||v.dispose()}},p=new J(x);return _==null||_.add(p),p.event}function f(w,_,v=100,x=!1,p=!1,R,F){let j,Le,ve,He=0,fe;const ri={leakWarningThreshold:R,onWillAddFirstListener(){j=w(si=>{He++,Le=_(Le,si),x&&!ve&&(We.fire(Le),Le=void 0),fe=()=>{const ii=Le;Le=void 0,ve=void 0,(!x||He>1)&&We.fire(ii),He=0},typeof v=="number"?(clearTimeout(ve),ve=setTimeout(fe,v)):ve===void 0&&(ve=0,queueMicrotask(fe))})},onWillRemoveListener(){p&&He>0&&(fe==null||fe())},onDidRemoveLastListener(){fe=void 0,j.dispose()}},We=new J(ri);return F==null||F.add(We),We.event}e.debounce=f;function h(w,_=0,v){return e.debounce(w,(x,p)=>x?(x.push(p),x):[p],_,void 0,!0,void 0,v)}e.accumulate=h;function d(w,_=(x,p)=>x===p,v){let x=!0,p;return a(w,R=>{const F=x||!_(R,p);return x=!1,p=R,F},v)}e.latch=d;function m(w,_,v){return[e.filter(w,_,v),e.filter(w,x=>!_(x),v)]}e.split=m;function b(w,_=!1,v=[]){let x=v.slice(),p=w(j=>{x?x.push(j):F.fire(j)});const R=()=>{x==null||x.forEach(j=>F.fire(j)),x=null},F=new J({onWillAddFirstListener(){p||(p=w(j=>F.fire(j)))},onDidAddFirstListener(){x&&(_?setTimeout(R):R())},onDidRemoveLastListener(){p&&p.dispose(),p=null}});return F.event}e.buffer=b;class C{constructor(_){this.event=_,this.disposables=new oe}map(_){return new C(r(this.event,_,this.disposables))}forEach(_){return new C(s(this.event,_,this.disposables))}filter(_){return new C(a(this.event,_,this.disposables))}reduce(_,v){return new C(c(this.event,_,v,this.disposables))}latch(){return new C(d(this.event,void 0,this.disposables))}debounce(_,v=100,x=!1,p=!1,R){return new C(f(this.event,_,v,x,p,R,this.disposables))}on(_,v,x){return this.event(_,v,x)}once(_,v,x){return n(this.event)(_,v,x)}dispose(){this.disposables.dispose()}}function N(w){return new C(w)}e.chain=N;function g(w,_,v=x=>x){const x=(...j)=>F.fire(v(...j)),p=()=>w.on(_,x),R=()=>w.removeListener(_,x),F=new J({onWillAddFirstListener:p,onDidRemoveLastListener:R});return F.event}e.fromNodeEventEmitter=g;function S(w,_,v=x=>x){const x=(...j)=>F.fire(v(...j)),p=()=>w.addEventListener(_,x),R=()=>w.removeEventListener(_,x),F=new J({onWillAddFirstListener:p,onDidRemoveLastListener:R});return F.event}e.fromDOMEventEmitter=S;function L(w){return new Promise(_=>n(w)(_))}e.toPromise=L;function P(w,_){return _(void 0),w(v=>_(v))}e.runAndSubscribe=P;function k(w,_){let v=null;function x(R){v==null||v.dispose(),v=new oe,_(R,v)}x(void 0);const p=w(R=>x(R));return ke(()=>{p.dispose(),v==null||v.dispose()})}e.runAndSubscribeWithStore=k;class G{constructor(_,v){this.obs=_,this._counter=0,this._hasChanged=!1;const x={onWillAddFirstListener:()=>{_.addObserver(this)},onDidRemoveLastListener:()=>{_.removeObserver(this)}};this.emitter=new J(x),v&&v.add(this.emitter)}beginUpdate(_){this._counter++}handleChange(_,v){this._hasChanged=!0}endUpdate(_){--this._counter===0&&this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this.obs.get()))}}function B(w,_){return new G(w,_).emitter.event}e.fromObservable=B})(Ye||(Ye={}));class me{constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${me._idPool++}`,me.all.add(this)}start(t){this._stopWatch=new Ee(!0),this.listenerCount=t}stop(){if(this._stopWatch){const t=this._stopWatch.elapsed();this.durations.push(t),this.elapsedOverall+=t,this.invocationCount+=1,this._stopWatch=void 0}}}me.all=new Set,me._idPool=0;let xr=-1;class wr{constructor(t,n=Math.random().toString(18).slice(2,5)){this.threshold=t,this.name=n,this._warnCountdown=0}dispose(){var t;(t=this._stacks)===null||t===void 0||t.clear()}check(t,n){const r=this.threshold;if(r<=0||n<r)return;this._stacks||(this._stacks=new Map);const s=this._stacks.get(t.value)||0;if(this._stacks.set(t.value,s+1),this._warnCountdown-=1,this._warnCountdown<=0){this._warnCountdown=r*.5;let a,l=0;for(const[o,c]of this._stacks)(!a||l<c)&&(a=o,l=c);console.warn(`[${this.name}] potential listener LEAK detected, having ${n} listeners already. MOST frequent listener (${l}):`),console.warn(a)}return()=>{const a=this._stacks.get(t.value)||0;this._stacks.set(t.value,a-1)}}}class Ze{static create(){var t;return new Ze((t=new Error().stack)!==null&&t!==void 0?t:"")}constructor(t){this.value=t}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}}class Lr{constructor(t,n,r){this.callback=t,this.callbackThis=n,this.stack=r,this.subscription=new ur}invoke(t){this.callback.call(this.callbackThis,t)}}class J{constructor(t){var n,r,s,a,l;this._disposed=!1,this._options=t,this._leakageMon=!((n=this._options)===null||n===void 0)&&n.leakWarningThreshold?new wr((s=(r=this._options)===null||r===void 0?void 0:r.leakWarningThreshold)!==null&&s!==void 0?s:xr):void 0,this._perfMon=!((a=this._options)===null||a===void 0)&&a._profName?new me(this._options._profName):void 0,this._deliveryQueue=(l=this._options)===null||l===void 0?void 0:l.deliveryQueue}dispose(){var t,n,r,s;this._disposed||(this._disposed=!0,this._listeners&&this._listeners.clear(),(t=this._deliveryQueue)===null||t===void 0||t.clear(this),(r=(n=this._options)===null||n===void 0?void 0:n.onDidRemoveLastListener)===null||r===void 0||r.call(n),(s=this._leakageMon)===null||s===void 0||s.dispose())}get event(){return this._event||(this._event=(t,n,r)=>{var s,a,l;if(this._listeners||(this._listeners=new Pe),this._leakageMon&&this._listeners.size>this._leakageMon.threshold*3)return console.warn(`[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far`),Me.None;const o=this._listeners.isEmpty();o&&(!((s=this._options)===null||s===void 0)&&s.onWillAddFirstListener)&&this._options.onWillAddFirstListener(this);let c,u;this._leakageMon&&this._listeners.size>=Math.ceil(this._leakageMon.threshold*.2)&&(u=Ze.create(),c=this._leakageMon.check(u,this._listeners.size+1));const f=new Lr(t,n,u),h=this._listeners.push(f);o&&(!((a=this._options)===null||a===void 0)&&a.onDidAddFirstListener)&&this._options.onDidAddFirstListener(this),!((l=this._options)===null||l===void 0)&&l.onDidAddListener&&this._options.onDidAddListener(this,t,n);const d=f.subscription.set(()=>{var m,b;c==null||c(),this._disposed||((b=(m=this._options)===null||m===void 0?void 0:m.onWillRemoveListener)===null||b===void 0||b.call(m,this),h(),this._options&&this._options.onDidRemoveLastListener&&(this._listeners&&!this._listeners.isEmpty()||this._options.onDidRemoveLastListener(this)))});return r instanceof oe?r.add(d):Array.isArray(r)&&r.push(d),d}),this._event}fire(t){var n,r;if(this._listeners){this._deliveryQueue||(this._deliveryQueue=new Nr);for(const s of this._listeners)this._deliveryQueue.push(this,s,t);(n=this._perfMon)===null||n===void 0||n.start(this._deliveryQueue.size),this._deliveryQueue.deliver(),(r=this._perfMon)===null||r===void 0||r.stop()}}hasListeners(){return this._listeners?!this._listeners.isEmpty():!1}}class vr{constructor(){this._queue=new Pe}get size(){return this._queue.size}push(t,n,r){this._queue.push(new Sr(t,n,r))}clear(t){const n=new Pe;for(const r of this._queue)r.emitter!==t&&n.push(r);this._queue=n}deliver(){for(;this._queue.size>0;){const t=this._queue.shift();try{t.listener.invoke(t.event)}catch(n){Lt(n)}}}}class Nr extends vr{clear(t){this._queue.clear()}}class Sr{constructor(t,n,r){this.emitter=t,this.listener=n,this.event=r}}function Cr(e){return typeof e=="string"}function Ar(e){let t=[],n=Object.getPrototypeOf(e);for(;Object.prototype!==n;)t=t.concat(Object.getOwnPropertyNames(n)),n=Object.getPrototypeOf(n);return t}function Je(e){const t=[];for(const n of Ar(e))typeof e[n]=="function"&&t.push(n);return t}function yr(e,t){const n=s=>function(){const a=Array.prototype.slice.call(arguments,0);return t(s,a)},r={};for(const s of e)r[s]=n(s);return r}class Rr{constructor(t){this.fn=t,this.lastCache=void 0,this.lastArgKey=void 0}get(t){const n=JSON.stringify(t);return this.lastArgKey!==n&&(this.lastArgKey=n,this.lastCache=this.fn(t)),this.lastCache}}class Ct{constructor(t){this.executor=t,this._didRun=!1}get value(){if(!this._didRun)try{this._value=this.executor()}catch(t){this._error=t}finally{this._didRun=!0}if(this._error)throw this._error;return this._value}get rawValue(){return this._value}}var At;function kr(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function Mr(e){return e.split(/\r\n|\r|\n/)}function Pr(e){for(let t=0,n=e.length;t<n;t++){const r=e.charCodeAt(t);if(r!==32&&r!==9)return t}return-1}function Dr(e,t=e.length-1){for(let n=t;n>=0;n--){const r=e.charCodeAt(n);if(r!==32&&r!==9)return n}return-1}function yt(e){return e>=65&&e<=90}function Ke(e){return 55296<=e&&e<=56319}function Er(e){return 56320<=e&&e<=57343}function Fr(e,t){return(e-55296<<10)+(t-56320)+65536}function Vr(e,t,n){const r=e.charCodeAt(n);if(Ke(r)&&n+1<t){const s=e.charCodeAt(n+1);if(Er(s))return Fr(r,s)}return r}const Br=/^[\t\n\r\x20-\x7E]*$/;function Tr(e){return Br.test(e)}class X{static getInstance(t){return X.cache.get(Array.from(t))}static getLocales(){return X._locales.value}constructor(t){this.confusableDictionary=t}isAmbiguous(t){return this.confusableDictionary.has(t)}getPrimaryConfusable(t){return this.confusableDictionary.get(t)}getConfusableCodePoints(){return new Set(this.confusableDictionary.keys())}}At=X,X.ambiguousCharacterData=new Ct(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,8218,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,8242,96,1370,96,1523,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71922,67,71913,67,65315,67,8557,67,8450,67,8493,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71919,87,71910,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,66293,90,71909,90,65338,90,8484,90,8488,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65297,49,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125],"_default":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"cs":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"es":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"fr":[65374,126,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"it":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ja":[8211,45,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65292,44,65307,59],"ko":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pt-BR":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ru":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"zh-hans":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41],"zh-hant":[8211,45,65374,126,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65307,59]}')),X.cache=new Rr(e=>{function t(u){const f=new Map;for(let h=0;h<u.length;h+=2)f.set(u[h],u[h+1]);return f}function n(u,f){const h=new Map(u);for(const[d,m]of f)h.set(d,m);return h}function r(u,f){if(!u)return f;const h=new Map;for(const[d,m]of u)f.has(d)&&h.set(d,m);return h}const s=At.ambiguousCharacterData.value;let a=e.filter(u=>!u.startsWith("_")&&u in s);a.length===0&&(a=["_default"]);let l;for(const u of a){const f=t(s[u]);l=r(l,f)}const o=t(s._common),c=n(o,l);return new X(c)}),X._locales=new Ct(()=>Object.keys(X.ambiguousCharacterData.value).filter(e=>!e.startsWith("_")));class te{static getRawData(){return JSON.parse("[9,10,11,12,13,32,127,160,173,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8203,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12288,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999]")}static getData(){return this._data||(this._data=new Set(te.getRawData())),this._data}static isInvisibleCharacter(t){return te.getData().has(t)}static get codePoints(){return te.getData()}}te._data=void 0;const Ur="$initialize";class Ir{constructor(t,n,r,s){this.vsWorker=t,this.req=n,this.method=r,this.args=s,this.type=0}}class Rt{constructor(t,n,r,s){this.vsWorker=t,this.seq=n,this.res=r,this.err=s,this.type=1}}class qr{constructor(t,n,r,s){this.vsWorker=t,this.req=n,this.eventName=r,this.arg=s,this.type=2}}class Hr{constructor(t,n,r){this.vsWorker=t,this.req=n,this.event=r,this.type=3}}class Wr{constructor(t,n){this.vsWorker=t,this.req=n,this.type=4}}class $r{constructor(t){this._workerId=-1,this._handler=t,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(t){this._workerId=t}sendMessage(t,n){const r=String(++this._lastSentReq);return new Promise((s,a)=>{this._pendingReplies[r]={resolve:s,reject:a},this._send(new Ir(this._workerId,r,t,n))})}listen(t,n){let r=null;const s=new J({onWillAddFirstListener:()=>{r=String(++this._lastSentReq),this._pendingEmitters.set(r,s),this._send(new qr(this._workerId,r,t,n))},onDidRemoveLastListener:()=>{this._pendingEmitters.delete(r),this._send(new Wr(this._workerId,r)),r=null}});return s.event}handleMessage(t){!t||!t.vsWorker||this._workerId!==-1&&t.vsWorker!==this._workerId||this._handleMessage(t)}_handleMessage(t){switch(t.type){case 1:return this._handleReplyMessage(t);case 0:return this._handleRequestMessage(t);case 2:return this._handleSubscribeEventMessage(t);case 3:return this._handleEventMessage(t);case 4:return this._handleUnsubscribeEventMessage(t)}}_handleReplyMessage(t){if(!this._pendingReplies[t.seq]){console.warn("Got reply to unknown seq");return}const n=this._pendingReplies[t.seq];if(delete this._pendingReplies[t.seq],t.err){let r=t.err;t.err.$isError&&(r=new Error,r.name=t.err.name,r.message=t.err.message,r.stack=t.err.stack),n.reject(r);return}n.resolve(t.res)}_handleRequestMessage(t){const n=t.req;this._handler.handleMessage(t.method,t.args).then(s=>{this._send(new Rt(this._workerId,n,s,void 0))},s=>{s.detail instanceof Error&&(s.detail=vt(s.detail)),this._send(new Rt(this._workerId,n,void 0,vt(s)))})}_handleSubscribeEventMessage(t){const n=t.req,r=this._handler.handleEvent(t.eventName,t.arg)(s=>{this._send(new Hr(this._workerId,n,s))});this._pendingEvents.set(n,r)}_handleEventMessage(t){if(!this._pendingEmitters.has(t.req)){console.warn("Got event for unknown req");return}this._pendingEmitters.get(t.req).fire(t.event)}_handleUnsubscribeEventMessage(t){if(!this._pendingEvents.has(t.req)){console.warn("Got unsubscribe for unknown req");return}this._pendingEvents.get(t.req).dispose(),this._pendingEvents.delete(t.req)}_send(t){const n=[];if(t.type===0)for(let r=0;r<t.args.length;r++)t.args[r]instanceof ArrayBuffer&&n.push(t.args[r]);else t.type===1&&t.res instanceof ArrayBuffer&&n.push(t.res);this._handler.sendMessage(t,n)}}function kt(e){return e[0]==="o"&&e[1]==="n"&&yt(e.charCodeAt(2))}function Mt(e){return/^onDynamic/.test(e)&&yt(e.charCodeAt(9))}function zr(e,t,n){const r=l=>function(){const o=Array.prototype.slice.call(arguments,0);return t(l,o)},s=l=>function(o){return n(l,o)},a={};for(const l of e){if(Mt(l)){a[l]=s(l);continue}if(kt(l)){a[l]=n(l,void 0);continue}a[l]=r(l)}return a}class Or{constructor(t,n){this._requestHandlerFactory=n,this._requestHandler=null,this._protocol=new $r({sendMessage:(r,s)=>{t(r,s)},handleMessage:(r,s)=>this._handleMessage(r,s),handleEvent:(r,s)=>this._handleEvent(r,s)})}onmessage(t){this._protocol.handleMessage(t)}_handleMessage(t,n){if(t===Ur)return this.initialize(n[0],n[1],n[2],n[3]);if(!this._requestHandler||typeof this._requestHandler[t]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+t));try{return Promise.resolve(this._requestHandler[t].apply(this._requestHandler,n))}catch(r){return Promise.reject(r)}}_handleEvent(t,n){if(!this._requestHandler)throw new Error("Missing requestHandler");if(Mt(t)){const r=this._requestHandler[t].call(this._requestHandler,n);if(typeof r!="function")throw new Error(`Missing dynamic event ${t} on request handler.`);return r}if(kt(t)){const r=this._requestHandler[t];if(typeof r!="function")throw new Error(`Missing event ${t} on request handler.`);return r}throw new Error(`Malformed event name ${t}`)}initialize(t,n,r,s){this._protocol.setWorkerId(t);const o=zr(s,(c,u)=>this._protocol.sendMessage(c,u),(c,u)=>this._protocol.listen(c,u));return this._requestHandlerFactory?(this._requestHandler=this._requestHandlerFactory(o),Promise.resolve(Je(this._requestHandler))):(n&&(typeof n.baseUrl<"u"&&delete n.baseUrl,typeof n.paths<"u"&&typeof n.paths.vs<"u"&&delete n.paths.vs,typeof n.trustedTypesPolicy!==void 0&&delete n.trustedTypesPolicy,n.catchError=!0,U.require.config(n)),new Promise((c,u)=>{const f=U.require;f([r],h=>{if(this._requestHandler=h.create(o),!this._requestHandler){u(new Error("No RequestHandler!"));return}c(Je(this._requestHandler))},u)}))}}class ne{constructor(t,n,r,s){this.originalStart=t,this.originalLength=n,this.modifiedStart=r,this.modifiedLength=s}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}}function Pt(e,t){return(t<<5)-t+e|0}function Gr(e,t){t=Pt(149417,t);for(let n=0,r=e.length;n<r;n++)t=Pt(e.charCodeAt(n),t);return t}class Dt{constructor(t){this.source=t}getElements(){const t=this.source,n=new Int32Array(t.length);for(let r=0,s=t.length;r<s;r++)n[r]=t.charCodeAt(r);return n}}function jr(e,t,n){return new re(new Dt(e),new Dt(t)).ComputeDiff(n).changes}class ge{static Assert(t,n){if(!t)throw new Error(n)}}class be{static Copy(t,n,r,s,a){for(let l=0;l<a;l++)r[s+l]=t[n+l]}static Copy2(t,n,r,s,a){for(let l=0;l<a;l++)r[s+l]=t[n+l]}}class Et{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new ne(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(t,n){this.m_originalStart=Math.min(this.m_originalStart,t),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_originalCount++}AddModifiedElement(t,n){this.m_originalStart=Math.min(this.m_originalStart,t),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}}class re{constructor(t,n,r=null){this.ContinueProcessingPredicate=r,this._originalSequence=t,this._modifiedSequence=n;const[s,a,l]=re._getElements(t),[o,c,u]=re._getElements(n);this._hasStrings=l&&u,this._originalStringElements=s,this._originalElementsOrHash=a,this._modifiedStringElements=o,this._modifiedElementsOrHash=c,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(t){return t.length>0&&typeof t[0]=="string"}static _getElements(t){const n=t.getElements();if(re._isStringArray(n)){const r=new Int32Array(n.length);for(let s=0,a=n.length;s<a;s++)r[s]=Gr(n[s],0);return[n,r,!0]}return n instanceof Int32Array?[[],n,!1]:[[],new Int32Array(n),!1]}ElementsAreEqual(t,n){return this._originalElementsOrHash[t]!==this._modifiedElementsOrHash[n]?!1:this._hasStrings?this._originalStringElements[t]===this._modifiedStringElements[n]:!0}ElementsAreStrictEqual(t,n){if(!this.ElementsAreEqual(t,n))return!1;const r=re._getStrictElement(this._originalSequence,t),s=re._getStrictElement(this._modifiedSequence,n);return r===s}static _getStrictElement(t,n){return typeof t.getStrictElement=="function"?t.getStrictElement(n):null}OriginalElementsAreEqual(t,n){return this._originalElementsOrHash[t]!==this._originalElementsOrHash[n]?!1:this._hasStrings?this._originalStringElements[t]===this._originalStringElements[n]:!0}ModifiedElementsAreEqual(t,n){return this._modifiedElementsOrHash[t]!==this._modifiedElementsOrHash[n]?!1:this._hasStrings?this._modifiedStringElements[t]===this._modifiedStringElements[n]:!0}ComputeDiff(t){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,t)}_ComputeDiff(t,n,r,s,a){const l=[!1];let o=this.ComputeDiffRecursive(t,n,r,s,l);return a&&(o=this.PrettifyChanges(o)),{quitEarly:l[0],changes:o}}ComputeDiffRecursive(t,n,r,s,a){for(a[0]=!1;t<=n&&r<=s&&this.ElementsAreEqual(t,r);)t++,r++;for(;n>=t&&s>=r&&this.ElementsAreEqual(n,s);)n--,s--;if(t>n||r>s){let h;return r<=s?(ge.Assert(t===n+1,"originalStart should only be one more than originalEnd"),h=[new ne(t,0,r,s-r+1)]):t<=n?(ge.Assert(r===s+1,"modifiedStart should only be one more than modifiedEnd"),h=[new ne(t,n-t+1,r,0)]):(ge.Assert(t===n+1,"originalStart should only be one more than originalEnd"),ge.Assert(r===s+1,"modifiedStart should only be one more than modifiedEnd"),h=[]),h}const l=[0],o=[0],c=this.ComputeRecursionPoint(t,n,r,s,l,o,a),u=l[0],f=o[0];if(c!==null)return c;if(!a[0]){const h=this.ComputeDiffRecursive(t,u,r,f,a);let d=[];return a[0]?d=[new ne(u+1,n-(u+1)+1,f+1,s-(f+1)+1)]:d=this.ComputeDiffRecursive(u+1,n,f+1,s,a),this.ConcatenateChanges(h,d)}return[new ne(t,n-t+1,r,s-r+1)]}WALKTRACE(t,n,r,s,a,l,o,c,u,f,h,d,m,b,C,N,g,S){let L=null,P=null,k=new Et,G=n,B=r,w=m[0]-N[0]-s,_=-1073741824,v=this.m_forwardHistory.length-1;do{const x=w+t;x===G||x<B&&u[x-1]<u[x+1]?(h=u[x+1],b=h-w-s,h<_&&k.MarkNextChange(),_=h,k.AddModifiedElement(h+1,b),w=x+1-t):(h=u[x-1]+1,b=h-w-s,h<_&&k.MarkNextChange(),_=h-1,k.AddOriginalElement(h,b+1),w=x-1-t),v>=0&&(u=this.m_forwardHistory[v],t=u[0],G=1,B=u.length-1)}while(--v>=-1);if(L=k.getReverseChanges(),S[0]){let x=m[0]+1,p=N[0]+1;if(L!==null&&L.length>0){const R=L[L.length-1];x=Math.max(x,R.getOriginalEnd()),p=Math.max(p,R.getModifiedEnd())}P=[new ne(x,d-x+1,p,C-p+1)]}else{k=new Et,G=l,B=o,w=m[0]-N[0]-c,_=1073741824,v=g?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{const x=w+a;x===G||x<B&&f[x-1]>=f[x+1]?(h=f[x+1]-1,b=h-w-c,h>_&&k.MarkNextChange(),_=h+1,k.AddOriginalElement(h+1,b+1),w=x+1-a):(h=f[x-1],b=h-w-c,h>_&&k.MarkNextChange(),_=h,k.AddModifiedElement(h+1,b+1),w=x-1-a),v>=0&&(f=this.m_reverseHistory[v],a=f[0],G=1,B=f.length-1)}while(--v>=-1);P=k.getChanges()}return this.ConcatenateChanges(L,P)}ComputeRecursionPoint(t,n,r,s,a,l,o){let c=0,u=0,f=0,h=0,d=0,m=0;t--,r--,a[0]=0,l[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];const b=n-t+(s-r),C=b+1,N=new Int32Array(C),g=new Int32Array(C),S=s-r,L=n-t,P=t-r,k=n-s,B=(L-S)%2===0;N[S]=t,g[L]=n,o[0]=!1;for(let w=1;w<=b/2+1;w++){let _=0,v=0;f=this.ClipDiagonalBound(S-w,w,S,C),h=this.ClipDiagonalBound(S+w,w,S,C);for(let p=f;p<=h;p+=2){p===f||p<h&&N[p-1]<N[p+1]?c=N[p+1]:c=N[p-1]+1,u=c-(p-S)-P;const R=c;for(;c<n&&u<s&&this.ElementsAreEqual(c+1,u+1);)c++,u++;if(N[p]=c,c+u>_+v&&(_=c,v=u),!B&&Math.abs(p-L)<=w-1&&c>=g[p])return a[0]=c,l[0]=u,R<=g[p]&&w<=1448?this.WALKTRACE(S,f,h,P,L,d,m,k,N,g,c,n,a,u,s,l,B,o):null}const x=(_-t+(v-r)-w)/2;if(this.ContinueProcessingPredicate!==null&&!this.ContinueProcessingPredicate(_,x))return o[0]=!0,a[0]=_,l[0]=v,x>0&&w<=1448?this.WALKTRACE(S,f,h,P,L,d,m,k,N,g,c,n,a,u,s,l,B,o):(t++,r++,[new ne(t,n-t+1,r,s-r+1)]);d=this.ClipDiagonalBound(L-w,w,L,C),m=this.ClipDiagonalBound(L+w,w,L,C);for(let p=d;p<=m;p+=2){p===d||p<m&&g[p-1]>=g[p+1]?c=g[p+1]-1:c=g[p-1],u=c-(p-L)-k;const R=c;for(;c>t&&u>r&&this.ElementsAreEqual(c,u);)c--,u--;if(g[p]=c,B&&Math.abs(p-S)<=w&&c<=N[p])return a[0]=c,l[0]=u,R>=N[p]&&w<=1448?this.WALKTRACE(S,f,h,P,L,d,m,k,N,g,c,n,a,u,s,l,B,o):null}if(w<=1447){let p=new Int32Array(h-f+2);p[0]=S-f+1,be.Copy2(N,f,p,1,h-f+1),this.m_forwardHistory.push(p),p=new Int32Array(m-d+2),p[0]=L-d+1,be.Copy2(g,d,p,1,m-d+1),this.m_reverseHistory.push(p)}}return this.WALKTRACE(S,f,h,P,L,d,m,k,N,g,c,n,a,u,s,l,B,o)}PrettifyChanges(t){for(let n=0;n<t.length;n++){const r=t[n],s=n<t.length-1?t[n+1].originalStart:this._originalElementsOrHash.length,a=n<t.length-1?t[n+1].modifiedStart:this._modifiedElementsOrHash.length,l=r.originalLength>0,o=r.modifiedLength>0;for(;r.originalStart+r.originalLength<s&&r.modifiedStart+r.modifiedLength<a&&(!l||this.OriginalElementsAreEqual(r.originalStart,r.originalStart+r.originalLength))&&(!o||this.ModifiedElementsAreEqual(r.modifiedStart,r.modifiedStart+r.modifiedLength));){const u=this.ElementsAreStrictEqual(r.originalStart,r.modifiedStart);if(this.ElementsAreStrictEqual(r.originalStart+r.originalLength,r.modifiedStart+r.modifiedLength)&&!u)break;r.originalStart++,r.modifiedStart++}const c=[null];if(n<t.length-1&&this.ChangesOverlap(t[n],t[n+1],c)){t[n]=c[0],t.splice(n+1,1),n--;continue}}for(let n=t.length-1;n>=0;n--){const r=t[n];let s=0,a=0;if(n>0){const h=t[n-1];s=h.originalStart+h.originalLength,a=h.modifiedStart+h.modifiedLength}const l=r.originalLength>0,o=r.modifiedLength>0;let c=0,u=this._boundaryScore(r.originalStart,r.originalLength,r.modifiedStart,r.modifiedLength);for(let h=1;;h++){const d=r.originalStart-h,m=r.modifiedStart-h;if(d<s||m<a||l&&!this.OriginalElementsAreEqual(d,d+r.originalLength)||o&&!this.ModifiedElementsAreEqual(m,m+r.modifiedLength))break;const C=(d===s&&m===a?5:0)+this._boundaryScore(d,r.originalLength,m,r.modifiedLength);C>u&&(u=C,c=h)}r.originalStart-=c,r.modifiedStart-=c;const f=[null];if(n>0&&this.ChangesOverlap(t[n-1],t[n],f)){t[n-1]=f[0],t.splice(n,1),n++;continue}}if(this._hasStrings)for(let n=1,r=t.length;n<r;n++){const s=t[n-1],a=t[n],l=a.originalStart-s.originalStart-s.originalLength,o=s.originalStart,c=a.originalStart+a.originalLength,u=c-o,f=s.modifiedStart,h=a.modifiedStart+a.modifiedLength,d=h-f;if(l<5&&u<20&&d<20){const m=this._findBetterContiguousSequence(o,u,f,d,l);if(m){const[b,C]=m;(b!==s.originalStart+s.originalLength||C!==s.modifiedStart+s.modifiedLength)&&(s.originalLength=b-s.originalStart,s.modifiedLength=C-s.modifiedStart,a.originalStart=b+l,a.modifiedStart=C+l,a.originalLength=c-a.originalStart,a.modifiedLength=h-a.modifiedStart)}}}return t}_findBetterContiguousSequence(t,n,r,s,a){if(n<a||s<a)return null;const l=t+n-a+1,o=r+s-a+1;let c=0,u=0,f=0;for(let h=t;h<l;h++)for(let d=r;d<o;d++){const m=this._contiguousSequenceScore(h,d,a);m>0&&m>c&&(c=m,u=h,f=d)}return c>0?[u,f]:null}_contiguousSequenceScore(t,n,r){let s=0;for(let a=0;a<r;a++){if(!this.ElementsAreEqual(t+a,n+a))return 0;s+=this._originalStringElements[t+a].length}return s}_OriginalIsBoundary(t){return t<=0||t>=this._originalElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._originalStringElements[t])}_OriginalRegionIsBoundary(t,n){if(this._OriginalIsBoundary(t)||this._OriginalIsBoundary(t-1))return!0;if(n>0){const r=t+n;if(this._OriginalIsBoundary(r-1)||this._OriginalIsBoundary(r))return!0}return!1}_ModifiedIsBoundary(t){return t<=0||t>=this._modifiedElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[t])}_ModifiedRegionIsBoundary(t,n){if(this._ModifiedIsBoundary(t)||this._ModifiedIsBoundary(t-1))return!0;if(n>0){const r=t+n;if(this._ModifiedIsBoundary(r-1)||this._ModifiedIsBoundary(r))return!0}return!1}_boundaryScore(t,n,r,s){const a=this._OriginalRegionIsBoundary(t,n)?1:0,l=this._ModifiedRegionIsBoundary(r,s)?1:0;return a+l}ConcatenateChanges(t,n){const r=[];if(t.length===0||n.length===0)return n.length>0?n:t;if(this.ChangesOverlap(t[t.length-1],n[0],r)){const s=new Array(t.length+n.length-1);return be.Copy(t,0,s,0,t.length-1),s[t.length-1]=r[0],be.Copy(n,1,s,t.length,n.length-1),s}else{const s=new Array(t.length+n.length);return be.Copy(t,0,s,0,t.length),be.Copy(n,0,s,t.length,n.length),s}}ChangesOverlap(t,n,r){if(ge.Assert(t.originalStart<=n.originalStart,"Left change is not less than or equal to right change"),ge.Assert(t.modifiedStart<=n.modifiedStart,"Left change is not less than or equal to right change"),t.originalStart+t.originalLength>=n.originalStart||t.modifiedStart+t.modifiedLength>=n.modifiedStart){const s=t.originalStart;let a=t.originalLength;const l=t.modifiedStart;let o=t.modifiedLength;return t.originalStart+t.originalLength>=n.originalStart&&(a=n.originalStart+n.originalLength-t.originalStart),t.modifiedStart+t.modifiedLength>=n.modifiedStart&&(o=n.modifiedStart+n.modifiedLength-t.modifiedStart),r[0]=new ne(s,a,l,o),!0}else return r[0]=null,!1}ClipDiagonalBound(t,n,r,s){if(t>=0&&t<s)return t;const a=r,l=s-r-1,o=n%2===0;if(t<0){const c=a%2===0;return o===c?0:1}else{const c=l%2===0;return o===c?s-1:s-2}}}var Ft={};let _e;if(typeof U.vscode<"u"&&typeof U.vscode.process<"u"){const e=U.vscode.process;_e={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process<"u"?_e={get platform(){return process.platform},get arch(){return process.arch},get env(){return Ft},cwd(){return Ft.VSCODE_CWD||process.cwd()}}:_e={get platform(){return Se?"win32":gr?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};const Fe=_e.cwd,Xr=_e.env,Qr=_e.platform,Yr=65,Zr=97,Jr=90,Kr=122,se=46,T=47,W=92,ie=58,es=63;class Vt extends Error{constructor(t,n,r){let s;typeof n=="string"&&n.indexOf("not ")===0?(s="must not be",n=n.replace(/^not /,"")):s="must be";const a=t.indexOf(".")!==-1?"property":"argument";let l=`The "${t}" ${a} ${s} of type ${n}`;l+=`. Received type ${typeof r}`,super(l),this.code="ERR_INVALID_ARG_TYPE"}}function ts(e,t){if(e===null||typeof e!="object")throw new Vt(t,"Object",e)}function V(e,t){if(typeof e!="string")throw new Vt(t,"string",e)}const ae=Qr==="win32";function y(e){return e===T||e===W}function et(e){return e===T}function le(e){return e>=Yr&&e<=Jr||e>=Zr&&e<=Kr}function Ve(e,t,n,r){let s="",a=0,l=-1,o=0,c=0;for(let u=0;u<=e.length;++u){if(u<e.length)c=e.charCodeAt(u);else{if(r(c))break;c=T}if(r(c)){if(!(l===u-1||o===1))if(o===2){if(s.length<2||a!==2||s.charCodeAt(s.length-1)!==se||s.charCodeAt(s.length-2)!==se){if(s.length>2){const f=s.lastIndexOf(n);f===-1?(s="",a=0):(s=s.slice(0,f),a=s.length-1-s.lastIndexOf(n)),l=u,o=0;continue}else if(s.length!==0){s="",a=0,l=u,o=0;continue}}t&&(s+=s.length>0?`${n}..`:"..",a=2)}else s.length>0?s+=`${n}${e.slice(l+1,u)}`:s=e.slice(l+1,u),a=u-l-1;l=u,o=0}else c===se&&o!==-1?++o:o=-1}return s}function Bt(e,t){ts(t,"pathObject");const n=t.dir||t.root,r=t.base||`${t.name||""}${t.ext||""}`;return n?n===t.root?`${n}${r}`:`${n}${e}${r}`:r}const q={resolve(...e){let t="",n="",r=!1;for(let s=e.length-1;s>=-1;s--){let a;if(s>=0){if(a=e[s],V(a,"path"),a.length===0)continue}else t.length===0?a=Fe():(a=Xr[`=${t}`]||Fe(),(a===void 0||a.slice(0,2).toLowerCase()!==t.toLowerCase()&&a.charCodeAt(2)===W)&&(a=`${t}\\`));const l=a.length;let o=0,c="",u=!1;const f=a.charCodeAt(0);if(l===1)y(f)&&(o=1,u=!0);else if(y(f))if(u=!0,y(a.charCodeAt(1))){let h=2,d=h;for(;h<l&&!y(a.charCodeAt(h));)h++;if(h<l&&h!==d){const m=a.slice(d,h);for(d=h;h<l&&y(a.charCodeAt(h));)h++;if(h<l&&h!==d){for(d=h;h<l&&!y(a.charCodeAt(h));)h++;(h===l||h!==d)&&(c=`\\\\${m}\\${a.slice(d,h)}`,o=h)}}}else o=1;else le(f)&&a.charCodeAt(1)===ie&&(c=a.slice(0,2),o=2,l>2&&y(a.charCodeAt(2))&&(u=!0,o=3));if(c.length>0)if(t.length>0){if(c.toLowerCase()!==t.toLowerCase())continue}else t=c;if(r){if(t.length>0)break}else if(n=`${a.slice(o)}\\${n}`,r=u,u&&t.length>0)break}return n=Ve(n,!r,"\\",y),r?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){V(e,"path");const t=e.length;if(t===0)return".";let n=0,r,s=!1;const a=e.charCodeAt(0);if(t===1)return et(a)?"\\":e;if(y(a))if(s=!0,y(e.charCodeAt(1))){let o=2,c=o;for(;o<t&&!y(e.charCodeAt(o));)o++;if(o<t&&o!==c){const u=e.slice(c,o);for(c=o;o<t&&y(e.charCodeAt(o));)o++;if(o<t&&o!==c){for(c=o;o<t&&!y(e.charCodeAt(o));)o++;if(o===t)return`\\\\${u}\\${e.slice(c)}\\`;o!==c&&(r=`\\\\${u}\\${e.slice(c,o)}`,n=o)}}}else n=1;else le(a)&&e.charCodeAt(1)===ie&&(r=e.slice(0,2),n=2,t>2&&y(e.charCodeAt(2))&&(s=!0,n=3));let l=n<t?Ve(e.slice(n),!s,"\\",y):"";return l.length===0&&!s&&(l="."),l.length>0&&y(e.charCodeAt(t-1))&&(l+="\\"),r===void 0?s?`\\${l}`:l:s?`${r}\\${l}`:`${r}${l}`},isAbsolute(e){V(e,"path");const t=e.length;if(t===0)return!1;const n=e.charCodeAt(0);return y(n)||t>2&&le(n)&&e.charCodeAt(1)===ie&&y(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,n;for(let a=0;a<e.length;++a){const l=e[a];V(l,"path"),l.length>0&&(t===void 0?t=n=l:t+=`\\${l}`)}if(t===void 0)return".";let r=!0,s=0;if(typeof n=="string"&&y(n.charCodeAt(0))){++s;const a=n.length;a>1&&y(n.charCodeAt(1))&&(++s,a>2&&(y(n.charCodeAt(2))?++s:r=!1))}if(r){for(;s<t.length&&y(t.charCodeAt(s));)s++;s>=2&&(t=`\\${t.slice(s)}`)}return q.normalize(t)},relative(e,t){if(V(e,"from"),V(t,"to"),e===t)return"";const n=q.resolve(e),r=q.resolve(t);if(n===r||(e=n.toLowerCase(),t=r.toLowerCase(),e===t))return"";let s=0;for(;s<e.length&&e.charCodeAt(s)===W;)s++;let a=e.length;for(;a-1>s&&e.charCodeAt(a-1)===W;)a--;const l=a-s;let o=0;for(;o<t.length&&t.charCodeAt(o)===W;)o++;let c=t.length;for(;c-1>o&&t.charCodeAt(c-1)===W;)c--;const u=c-o,f=l<u?l:u;let h=-1,d=0;for(;d<f;d++){const b=e.charCodeAt(s+d);if(b!==t.charCodeAt(o+d))break;b===W&&(h=d)}if(d!==f){if(h===-1)return r}else{if(u>f){if(t.charCodeAt(o+d)===W)return r.slice(o+d+1);if(d===2)return r.slice(o+d)}l>f&&(e.charCodeAt(s+d)===W?h=d:d===2&&(h=3)),h===-1&&(h=0)}let m="";for(d=s+h+1;d<=a;++d)(d===a||e.charCodeAt(d)===W)&&(m+=m.length===0?"..":"\\..");return o+=h,m.length>0?`${m}${r.slice(o,c)}`:(r.charCodeAt(o)===W&&++o,r.slice(o,c))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;const t=q.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===W){if(t.charCodeAt(1)===W){const n=t.charCodeAt(2);if(n!==es&&n!==se)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(le(t.charCodeAt(0))&&t.charCodeAt(1)===ie&&t.charCodeAt(2)===W)return`\\\\?\\${t}`;return e},dirname(e){V(e,"path");const t=e.length;if(t===0)return".";let n=-1,r=0;const s=e.charCodeAt(0);if(t===1)return y(s)?e:".";if(y(s)){if(n=r=1,y(e.charCodeAt(1))){let o=2,c=o;for(;o<t&&!y(e.charCodeAt(o));)o++;if(o<t&&o!==c){for(c=o;o<t&&y(e.charCodeAt(o));)o++;if(o<t&&o!==c){for(c=o;o<t&&!y(e.charCodeAt(o));)o++;if(o===t)return e;o!==c&&(n=r=o+1)}}}}else le(s)&&e.charCodeAt(1)===ie&&(n=t>2&&y(e.charCodeAt(2))?3:2,r=n);let a=-1,l=!0;for(let o=t-1;o>=r;--o)if(y(e.charCodeAt(o))){if(!l){a=o;break}}else l=!1;if(a===-1){if(n===-1)return".";a=n}return e.slice(0,a)},basename(e,t){t!==void 0&&V(t,"ext"),V(e,"path");let n=0,r=-1,s=!0,a;if(e.length>=2&&le(e.charCodeAt(0))&&e.charCodeAt(1)===ie&&(n=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let l=t.length-1,o=-1;for(a=e.length-1;a>=n;--a){const c=e.charCodeAt(a);if(y(c)){if(!s){n=a+1;break}}else o===-1&&(s=!1,o=a+1),l>=0&&(c===t.charCodeAt(l)?--l===-1&&(r=a):(l=-1,r=o))}return n===r?r=o:r===-1&&(r=e.length),e.slice(n,r)}for(a=e.length-1;a>=n;--a)if(y(e.charCodeAt(a))){if(!s){n=a+1;break}}else r===-1&&(s=!1,r=a+1);return r===-1?"":e.slice(n,r)},extname(e){V(e,"path");let t=0,n=-1,r=0,s=-1,a=!0,l=0;e.length>=2&&e.charCodeAt(1)===ie&&le(e.charCodeAt(0))&&(t=r=2);for(let o=e.length-1;o>=t;--o){const c=e.charCodeAt(o);if(y(c)){if(!a){r=o+1;break}continue}s===-1&&(a=!1,s=o+1),c===se?n===-1?n=o:l!==1&&(l=1):n!==-1&&(l=-1)}return n===-1||s===-1||l===0||l===1&&n===s-1&&n===r+1?"":e.slice(n,s)},format:Bt.bind(null,"\\"),parse(e){V(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.length;let r=0,s=e.charCodeAt(0);if(n===1)return y(s)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(y(s)){if(r=1,y(e.charCodeAt(1))){let h=2,d=h;for(;h<n&&!y(e.charCodeAt(h));)h++;if(h<n&&h!==d){for(d=h;h<n&&y(e.charCodeAt(h));)h++;if(h<n&&h!==d){for(d=h;h<n&&!y(e.charCodeAt(h));)h++;h===n?r=h:h!==d&&(r=h+1)}}}}else if(le(s)&&e.charCodeAt(1)===ie){if(n<=2)return t.root=t.dir=e,t;if(r=2,y(e.charCodeAt(2))){if(n===3)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let a=-1,l=r,o=-1,c=!0,u=e.length-1,f=0;for(;u>=r;--u){if(s=e.charCodeAt(u),y(s)){if(!c){l=u+1;break}continue}o===-1&&(c=!1,o=u+1),s===se?a===-1?a=u:f!==1&&(f=1):a!==-1&&(f=-1)}return o!==-1&&(a===-1||f===0||f===1&&a===o-1&&a===l+1?t.base=t.name=e.slice(l,o):(t.name=e.slice(l,a),t.base=e.slice(l,o),t.ext=e.slice(a,o))),l>0&&l!==r?t.dir=e.slice(0,l-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},ns=(()=>{if(ae){const e=/\\/g;return()=>{const t=Fe().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>Fe()})(),$={resolve(...e){let t="",n=!1;for(let r=e.length-1;r>=-1&&!n;r--){const s=r>=0?e[r]:ns();V(s,"path"),s.length!==0&&(t=`${s}/${t}`,n=s.charCodeAt(0)===T)}return t=Ve(t,!n,"/",et),n?`/${t}`:t.length>0?t:"."},normalize(e){if(V(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===T,n=e.charCodeAt(e.length-1)===T;return e=Ve(e,!t,"/",et),e.length===0?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return V(e,"path"),e.length>0&&e.charCodeAt(0)===T},join(...e){if(e.length===0)return".";let t;for(let n=0;n<e.length;++n){const r=e[n];V(r,"path"),r.length>0&&(t===void 0?t=r:t+=`/${r}`)}return t===void 0?".":$.normalize(t)},relative(e,t){if(V(e,"from"),V(t,"to"),e===t||(e=$.resolve(e),t=$.resolve(t),e===t))return"";const n=1,r=e.length,s=r-n,a=1,l=t.length-a,o=s<l?s:l;let c=-1,u=0;for(;u<o;u++){const h=e.charCodeAt(n+u);if(h!==t.charCodeAt(a+u))break;h===T&&(c=u)}if(u===o)if(l>o){if(t.charCodeAt(a+u)===T)return t.slice(a+u+1);if(u===0)return t.slice(a+u)}else s>o&&(e.charCodeAt(n+u)===T?c=u:u===0&&(c=0));let f="";for(u=n+c+1;u<=r;++u)(u===r||e.charCodeAt(u)===T)&&(f+=f.length===0?"..":"/..");return`${f}${t.slice(a+c)}`},toNamespacedPath(e){return e},dirname(e){if(V(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===T;let n=-1,r=!0;for(let s=e.length-1;s>=1;--s)if(e.charCodeAt(s)===T){if(!r){n=s;break}}else r=!1;return n===-1?t?"/":".":t&&n===1?"//":e.slice(0,n)},basename(e,t){t!==void 0&&V(t,"ext"),V(e,"path");let n=0,r=-1,s=!0,a;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let l=t.length-1,o=-1;for(a=e.length-1;a>=0;--a){const c=e.charCodeAt(a);if(c===T){if(!s){n=a+1;break}}else o===-1&&(s=!1,o=a+1),l>=0&&(c===t.charCodeAt(l)?--l===-1&&(r=a):(l=-1,r=o))}return n===r?r=o:r===-1&&(r=e.length),e.slice(n,r)}for(a=e.length-1;a>=0;--a)if(e.charCodeAt(a)===T){if(!s){n=a+1;break}}else r===-1&&(s=!1,r=a+1);return r===-1?"":e.slice(n,r)},extname(e){V(e,"path");let t=-1,n=0,r=-1,s=!0,a=0;for(let l=e.length-1;l>=0;--l){const o=e.charCodeAt(l);if(o===T){if(!s){n=l+1;break}continue}r===-1&&(s=!1,r=l+1),o===se?t===-1?t=l:a!==1&&(a=1):t!==-1&&(a=-1)}return t===-1||r===-1||a===0||a===1&&t===r-1&&t===n+1?"":e.slice(t,r)},format:Bt.bind(null,"/"),parse(e){V(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.charCodeAt(0)===T;let r;n?(t.root="/",r=1):r=0;let s=-1,a=0,l=-1,o=!0,c=e.length-1,u=0;for(;c>=r;--c){const f=e.charCodeAt(c);if(f===T){if(!o){a=c+1;break}continue}l===-1&&(o=!1,l=c+1),f===se?s===-1?s=c:u!==1&&(u=1):s!==-1&&(u=-1)}if(l!==-1){const f=a===0&&n?1:a;s===-1||u===0||u===1&&s===l-1&&s===a+1?t.base=t.name=e.slice(f,l):(t.name=e.slice(f,s),t.base=e.slice(f,l),t.ext=e.slice(s,l))}return a>0?t.dir=e.slice(0,a-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};$.win32=q.win32=q,$.posix=q.posix=$,ae?q.normalize:$.normalize,ae?q.resolve:$.resolve,ae?q.relative:$.relative,ae?q.dirname:$.dirname,ae?q.basename:$.basename,ae?q.extname:$.extname,ae?q.sep:$.sep;const rs=/^\w[\w\d+.-]*$/,ss=/^\//,is=/^\/\//;function Tt(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!rs.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!ss.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(is.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function as(e,t){return!e&&!t?"file":e}function ls(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==Y&&(t=Y+t):t=Y;break}return t}const E="",Y="/",os=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class ue{static isUri(t){return t instanceof ue?!0:t?typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function":!1}constructor(t,n,r,s,a,l=!1){typeof t=="object"?(this.scheme=t.scheme||E,this.authority=t.authority||E,this.path=t.path||E,this.query=t.query||E,this.fragment=t.fragment||E):(this.scheme=as(t,l),this.authority=n||E,this.path=ls(this.scheme,r||E),this.query=s||E,this.fragment=a||E,Tt(this,l))}get fsPath(){return tt(this,!1)}with(t){if(!t)return this;let{scheme:n,authority:r,path:s,query:a,fragment:l}=t;return n===void 0?n=this.scheme:n===null&&(n=E),r===void 0?r=this.authority:r===null&&(r=E),s===void 0?s=this.path:s===null&&(s=E),a===void 0?a=this.query:a===null&&(a=E),l===void 0?l=this.fragment:l===null&&(l=E),n===this.scheme&&r===this.authority&&s===this.path&&a===this.query&&l===this.fragment?this:new pe(n,r,s,a,l)}static parse(t,n=!1){const r=os.exec(t);return r?new pe(r[2]||E,Be(r[4]||E),Be(r[5]||E),Be(r[7]||E),Be(r[9]||E),n):new pe(E,E,E,E,E)}static file(t){let n=E;if(Se&&(t=t.replace(/\\/g,Y)),t[0]===Y&&t[1]===Y){const r=t.indexOf(Y,2);r===-1?(n=t.substring(2),t=Y):(n=t.substring(2,r),t=t.substring(r)||Y)}return new pe("file",n,t,E,E)}static from(t){const n=new pe(t.scheme,t.authority,t.path,t.query,t.fragment);return Tt(n,!0),n}static joinPath(t,...n){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let r;return Se&&t.scheme==="file"?r=ue.file(q.join(tt(t,!0),...n)).path:r=$.join(t.path,...n),t.with({path:r})}toString(t=!1){return nt(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof ue)return t;{const n=new pe(t);return n._formatted=t.external,n._fsPath=t._sep===Ut?t.fsPath:null,n}}else return t}}const Ut=Se?1:void 0;class pe extends ue{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=tt(this,!1)),this._fsPath}toString(t=!1){return t?nt(this,!0):(this._formatted||(this._formatted=nt(this,!1)),this._formatted)}toJSON(){const t={$mid:1};return this._fsPath&&(t.fsPath=this._fsPath,t._sep=Ut),this._formatted&&(t.external=this._formatted),this.path&&(t.path=this.path),this.scheme&&(t.scheme=this.scheme),this.authority&&(t.authority=this.authority),this.query&&(t.query=this.query),this.fragment&&(t.fragment=this.fragment),t}}const It={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function qt(e,t,n){let r,s=-1;for(let a=0;a<e.length;a++){const l=e.charCodeAt(a);if(l>=97&&l<=122||l>=65&&l<=90||l>=48&&l<=57||l===45||l===46||l===95||l===126||t&&l===47||n&&l===91||n&&l===93||n&&l===58)s!==-1&&(r+=encodeURIComponent(e.substring(s,a)),s=-1),r!==void 0&&(r+=e.charAt(a));else{r===void 0&&(r=e.substr(0,a));const o=It[l];o!==void 0?(s!==-1&&(r+=encodeURIComponent(e.substring(s,a)),s=-1),r+=o):s===-1&&(s=a)}}return s!==-1&&(r+=encodeURIComponent(e.substring(s))),r!==void 0?r:e}function us(e){let t;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);r===35||r===63?(t===void 0&&(t=e.substr(0,n)),t+=It[r]):t!==void 0&&(t+=e[n])}return t!==void 0?t:e}function tt(e,t){let n;return e.authority&&e.path.length>1&&e.scheme==="file"?n=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?n=e.path.substr(1):n=e.path[1].toLowerCase()+e.path.substr(2):n=e.path,Se&&(n=n.replace(/\//g,"\\")),n}function nt(e,t){const n=t?us:qt;let r="",{scheme:s,authority:a,path:l,query:o,fragment:c}=e;if(s&&(r+=s,r+=":"),(a||s==="file")&&(r+=Y,r+=Y),a){let u=a.indexOf("@");if(u!==-1){const f=a.substr(0,u);a=a.substr(u+1),u=f.lastIndexOf(":"),u===-1?r+=n(f,!1,!1):(r+=n(f.substr(0,u),!1,!1),r+=":",r+=n(f.substr(u+1),!1,!0)),r+="@"}a=a.toLowerCase(),u=a.lastIndexOf(":"),u===-1?r+=n(a,!1,!0):(r+=n(a.substr(0,u),!1,!0),r+=a.substr(u))}if(l){if(l.length>=3&&l.charCodeAt(0)===47&&l.charCodeAt(2)===58){const u=l.charCodeAt(1);u>=65&&u<=90&&(l=`/${String.fromCharCode(u+32)}:${l.substr(3)}`)}else if(l.length>=2&&l.charCodeAt(1)===58){const u=l.charCodeAt(0);u>=65&&u<=90&&(l=`${String.fromCharCode(u+32)}:${l.substr(2)}`)}r+=n(l,!0,!1)}return o&&(r+="?",r+=n(o,!1,!1)),c&&(r+="#",r+=t?c:qt(c,!1,!1)),r}function Ht(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+Ht(e.substr(3)):e}}const Wt=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function Be(e){return e.match(Wt)?e.replace(Wt,t=>Ht(t)):e}class H{constructor(t,n){this.lineNumber=t,this.column=n}with(t=this.lineNumber,n=this.column){return t===this.lineNumber&&n===this.column?this:new H(t,n)}delta(t=0,n=0){return this.with(this.lineNumber+t,this.column+n)}equals(t){return H.equals(this,t)}static equals(t,n){return!t&&!n?!0:!!t&&!!n&&t.lineNumber===n.lineNumber&&t.column===n.column}isBefore(t){return H.isBefore(this,t)}static isBefore(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<n.column}isBeforeOrEqual(t){return H.isBeforeOrEqual(this,t)}static isBeforeOrEqual(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<=n.column}static compare(t,n){const r=t.lineNumber|0,s=n.lineNumber|0;if(r===s){const a=t.column|0,l=n.column|0;return a-l}return r-s}clone(){return new H(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(t){return new H(t.lineNumber,t.column)}static isIPosition(t){return t&&typeof t.lineNumber=="number"&&typeof t.column=="number"}}class M{constructor(t,n,r,s){t>r||t===r&&n>s?(this.startLineNumber=r,this.startColumn=s,this.endLineNumber=t,this.endColumn=n):(this.startLineNumber=t,this.startColumn=n,this.endLineNumber=r,this.endColumn=s)}isEmpty(){return M.isEmpty(this)}static isEmpty(t){return t.startLineNumber===t.endLineNumber&&t.startColumn===t.endColumn}containsPosition(t){return M.containsPosition(this,t)}static containsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<t.startColumn||n.lineNumber===t.endLineNumber&&n.column>t.endColumn)}static strictContainsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<=t.startColumn||n.lineNumber===t.endLineNumber&&n.column>=t.endColumn)}containsRange(t){return M.containsRange(this,t)}static containsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>t.endColumn)}strictContainsRange(t){return M.strictContainsRange(this,t)}static strictContainsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<=t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>=t.endColumn)}plusRange(t){return M.plusRange(this,t)}static plusRange(t,n){let r,s,a,l;return n.startLineNumber<t.startLineNumber?(r=n.startLineNumber,s=n.startColumn):n.startLineNumber===t.startLineNumber?(r=n.startLineNumber,s=Math.min(n.startColumn,t.startColumn)):(r=t.startLineNumber,s=t.startColumn),n.endLineNumber>t.endLineNumber?(a=n.endLineNumber,l=n.endColumn):n.endLineNumber===t.endLineNumber?(a=n.endLineNumber,l=Math.max(n.endColumn,t.endColumn)):(a=t.endLineNumber,l=t.endColumn),new M(r,s,a,l)}intersectRanges(t){return M.intersectRanges(this,t)}static intersectRanges(t,n){let r=t.startLineNumber,s=t.startColumn,a=t.endLineNumber,l=t.endColumn;const o=n.startLineNumber,c=n.startColumn,u=n.endLineNumber,f=n.endColumn;return r<o?(r=o,s=c):r===o&&(s=Math.max(s,c)),a>u?(a=u,l=f):a===u&&(l=Math.min(l,f)),r>a||r===a&&s>l?null:new M(r,s,a,l)}equalsRange(t){return M.equalsRange(this,t)}static equalsRange(t,n){return!t&&!n?!0:!!t&&!!n&&t.startLineNumber===n.startLineNumber&&t.startColumn===n.startColumn&&t.endLineNumber===n.endLineNumber&&t.endColumn===n.endColumn}getEndPosition(){return M.getEndPosition(this)}static getEndPosition(t){return new H(t.endLineNumber,t.endColumn)}getStartPosition(){return M.getStartPosition(this)}static getStartPosition(t){return new H(t.startLineNumber,t.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(t,n){return new M(this.startLineNumber,this.startColumn,t,n)}setStartPosition(t,n){return new M(t,n,this.endLineNumber,this.endColumn)}collapseToStart(){return M.collapseToStart(this)}static collapseToStart(t){return new M(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}collapseToEnd(){return M.collapseToEnd(this)}static collapseToEnd(t){return new M(t.endLineNumber,t.endColumn,t.endLineNumber,t.endColumn)}delta(t){return new M(this.startLineNumber+t,this.startColumn,this.endLineNumber+t,this.endColumn)}static fromPositions(t,n=t){return new M(t.lineNumber,t.column,n.lineNumber,n.column)}static lift(t){return t?new M(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(t){return t&&typeof t.startLineNumber=="number"&&typeof t.startColumn=="number"&&typeof t.endLineNumber=="number"&&typeof t.endColumn=="number"}static areIntersectingOrTouching(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<t.startColumn)}static areIntersecting(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<=n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<=t.startColumn)}static compareRangesUsingStarts(t,n){if(t&&n){const a=t.startLineNumber|0,l=n.startLineNumber|0;if(a===l){const o=t.startColumn|0,c=n.startColumn|0;if(o===c){const u=t.endLineNumber|0,f=n.endLineNumber|0;if(u===f){const h=t.endColumn|0,d=n.endColumn|0;return h-d}return u-f}return o-c}return a-l}return(t?1:0)-(n?1:0)}static compareRangesUsingEnds(t,n){return t.endLineNumber===n.endLineNumber?t.endColumn===n.endColumn?t.startLineNumber===n.startLineNumber?t.startColumn-n.startColumn:t.startLineNumber-n.startLineNumber:t.endColumn-n.endColumn:t.endLineNumber-n.endLineNumber}static spansMultipleLines(t){return t.endLineNumber>t.startLineNumber}toJSON(){return this}}var $t;(function(e){function t(s){return s<0}e.isLessThan=t;function n(s){return s>0}e.isGreaterThan=n;function r(s){return s===0}e.isNeitherLessOrGreaterThan=r,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0})($t||($t={}));function zt(e){return e<0?0:e>255?255:e|0}function xe(e){return e<0?0:e>4294967295?4294967295:e|0}class cs{constructor(t){this.values=t,this.prefixSum=new Uint32Array(t.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(t,n){t=xe(t);const r=this.values,s=this.prefixSum,a=n.length;return a===0?!1:(this.values=new Uint32Array(r.length+a),this.values.set(r.subarray(0,t),0),this.values.set(r.subarray(t),t+a),this.values.set(n,t),t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(s.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}setValue(t,n){return t=xe(t),n=xe(n),this.values[t]===n?!1:(this.values[t]=n,t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),!0)}removeValues(t,n){t=xe(t),n=xe(n);const r=this.values,s=this.prefixSum;if(t>=r.length)return!1;const a=r.length-t;return n>=a&&(n=a),n===0?!1:(this.values=new Uint32Array(r.length-n),this.values.set(r.subarray(0,t),0),this.values.set(r.subarray(t+n),t),this.prefixSum=new Uint32Array(this.values.length),t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(s.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return this.values.length===0?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(t){return t<0?0:(t=xe(t),this._getPrefixSum(t))}_getPrefixSum(t){if(t<=this.prefixSumValidIndex[0])return this.prefixSum[t];let n=this.prefixSumValidIndex[0]+1;n===0&&(this.prefixSum[0]=this.values[0],n++),t>=this.values.length&&(t=this.values.length-1);for(let r=n;r<=t;r++)this.prefixSum[r]=this.prefixSum[r-1]+this.values[r];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],t),this.prefixSum[t]}getIndexOf(t){t=Math.floor(t),this.getTotalSum();let n=0,r=this.values.length-1,s=0,a=0,l=0;for(;n<=r;)if(s=n+(r-n)/2|0,a=this.prefixSum[s],l=a-this.values[s],t<l)r=s-1;else if(t>=a)n=s+1;else break;return new hs(s,t-l)}}class hs{constructor(t,n){this.index=t,this.remainder=n,this._prefixSumIndexOfResultBrand=void 0,this.index=t,this.remainder=n}}class fs{constructor(t,n,r,s){this._uri=t,this._lines=n,this._eol=r,this._versionId=s,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return this._cachedTextValue===null&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(t){t.eol&&t.eol!==this._eol&&(this._eol=t.eol,this._lineStarts=null);const n=t.changes;for(const r of n)this._acceptDeleteRange(r.range),this._acceptInsertText(new H(r.range.startLineNumber,r.range.startColumn),r.text);this._versionId=t.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){const t=this._eol.length,n=this._lines.length,r=new Uint32Array(n);for(let s=0;s<n;s++)r[s]=this._lines[s].length+t;this._lineStarts=new cs(r)}}_setLineText(t,n){this._lines[t]=n,this._lineStarts&&this._lineStarts.setValue(t,this._lines[t].length+this._eol.length)}_acceptDeleteRange(t){if(t.startLineNumber===t.endLineNumber){if(t.startColumn===t.endColumn)return;this._setLineText(t.startLineNumber-1,this._lines[t.startLineNumber-1].substring(0,t.startColumn-1)+this._lines[t.startLineNumber-1].substring(t.endColumn-1));return}this._setLineText(t.startLineNumber-1,this._lines[t.startLineNumber-1].substring(0,t.startColumn-1)+this._lines[t.endLineNumber-1].substring(t.endColumn-1)),this._lines.splice(t.startLineNumber,t.endLineNumber-t.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(t.startLineNumber,t.endLineNumber-t.startLineNumber)}_acceptInsertText(t,n){if(n.length===0)return;const r=Mr(n);if(r.length===1){this._setLineText(t.lineNumber-1,this._lines[t.lineNumber-1].substring(0,t.column-1)+r[0]+this._lines[t.lineNumber-1].substring(t.column-1));return}r[r.length-1]+=this._lines[t.lineNumber-1].substring(t.column-1),this._setLineText(t.lineNumber-1,this._lines[t.lineNumber-1].substring(0,t.column-1)+r[0]);const s=new Uint32Array(r.length-1);for(let a=1;a<r.length;a++)this._lines.splice(t.lineNumber+a-1,0,r[a]),s[a-1]=r[a].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(t.lineNumber,s)}}const ds="`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?";function ms(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(const n of ds)e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}const Ot=ms();function gs(e){let t=Ot;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}const Gt=new Pe;Gt.unshift({maxLen:1e3,windowSize:15,timeBudget:150});function rt(e,t,n,r,s){if(s||(s=Re.first(Gt)),n.length>s.maxLen){let u=e-s.maxLen/2;return u<0?u=0:r+=u,n=n.substring(u,e+s.maxLen/2),rt(e,t,n,r,s)}const a=Date.now(),l=e-1-r;let o=-1,c=null;for(let u=1;!(Date.now()-a>=s.timeBudget);u++){const f=l-s.windowSize*u;t.lastIndex=Math.max(0,f);const h=bs(t,n,l,o);if(!h&&c||(c=h,f<=0))break;o=f}if(c){const u={word:c[0],startColumn:r+1+c.index,endColumn:r+1+c.index+c[0].length};return t.lastIndex=0,u}return null}function bs(e,t,n,r){let s;for(;s=e.exec(t);){const a=s.index||0;if(a<=n&&e.lastIndex>=n)return s;if(r>0&&a>r)return null}return null}class st{constructor(t){const n=zt(t);this._defaultValue=n,this._asciiMap=st._createAsciiMap(n),this._map=new Map}static _createAsciiMap(t){const n=new Uint8Array(256);return n.fill(t),n}set(t,n){const r=zt(n);t>=0&&t<256?this._asciiMap[t]=r:this._map.set(t,r)}get(t){return t>=0&&t<256?this._asciiMap[t]:this._map.get(t)||this._defaultValue}clear(){this._asciiMap.fill(this._defaultValue),this._map.clear()}}class _s{constructor(t,n,r){const s=new Uint8Array(t*n);for(let a=0,l=t*n;a<l;a++)s[a]=r;this._data=s,this.rows=t,this.cols=n}get(t,n){return this._data[t*this.cols+n]}set(t,n,r){this._data[t*this.cols+n]=r}}class ps{constructor(t){let n=0,r=0;for(let a=0,l=t.length;a<l;a++){const[o,c,u]=t[a];c>n&&(n=c),o>r&&(r=o),u>r&&(r=u)}n++,r++;const s=new _s(r,n,0);for(let a=0,l=t.length;a<l;a++){const[o,c,u]=t[a];s.set(o,c,u)}this._states=s,this._maxCharCode=n}nextState(t,n){return n<0||n>=this._maxCharCode?0:this._states.get(t,n)}}let it=null;function xs(){return it===null&&(it=new ps([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),it}let Ce=null;function ws(){if(Ce===null){Ce=new st(0);const e=` 	<>'"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…`;for(let n=0;n<e.length;n++)Ce.set(e.charCodeAt(n),1);const t=".,;:";for(let n=0;n<t.length;n++)Ce.set(t.charCodeAt(n),2)}return Ce}class Te{static _createLink(t,n,r,s,a){let l=a-1;do{const o=n.charCodeAt(l);if(t.get(o)!==2)break;l--}while(l>s);if(s>0){const o=n.charCodeAt(s-1),c=n.charCodeAt(l);(o===40&&c===41||o===91&&c===93||o===123&&c===125)&&l--}return{range:{startLineNumber:r,startColumn:s+1,endLineNumber:r,endColumn:l+2},url:n.substring(s,l+1)}}static computeLinks(t,n=xs()){const r=ws(),s=[];for(let a=1,l=t.getLineCount();a<=l;a++){const o=t.getLineContent(a),c=o.length;let u=0,f=0,h=0,d=1,m=!1,b=!1,C=!1,N=!1;for(;u<c;){let g=!1;const S=o.charCodeAt(u);if(d===13){let L;switch(S){case 40:m=!0,L=0;break;case 41:L=m?0:1;break;case 91:C=!0,b=!0,L=0;break;case 93:C=!1,L=b?0:1;break;case 123:N=!0,L=0;break;case 125:L=N?0:1;break;case 39:case 34:case 96:h===S?L=1:h===39||h===34||h===96?L=0:L=1;break;case 42:L=h===42?1:0;break;case 124:L=h===124?1:0;break;case 32:L=C?0:1;break;default:L=r.get(S)}L===1&&(s.push(Te._createLink(r,o,a,f,u)),g=!0)}else if(d===12){let L;S===91?(b=!0,L=0):L=r.get(S),L===1?g=!0:d=13}else d=n.nextState(d,S),d===0&&(g=!0);g&&(d=1,m=!1,b=!1,N=!1,f=u+1,h=S),u++}d===13&&s.push(Te._createLink(r,o,a,f,c))}return s}}function Ls(e){return!e||typeof e.getLineCount!="function"||typeof e.getLineContent!="function"?[]:Te.computeLinks(e)}class at{constructor(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}navigateValueSet(t,n,r,s,a){if(t&&n){const l=this.doNavigateValueSet(n,a);if(l)return{range:t,value:l}}if(r&&s){const l=this.doNavigateValueSet(s,a);if(l)return{range:r,value:l}}return null}doNavigateValueSet(t,n){const r=this.numberReplace(t,n);return r!==null?r:this.textReplace(t,n)}numberReplace(t,n){const r=Math.pow(10,t.length-(t.lastIndexOf(".")+1));let s=Number(t);const a=parseFloat(t);return!isNaN(s)&&!isNaN(a)&&s===a?s===0&&!n?null:(s=Math.floor(s*r),s+=n?r:-r,String(s/r)):null}textReplace(t,n){return this.valueSetsReplace(this._defaultValueSet,t,n)}valueSetsReplace(t,n,r){let s=null;for(let a=0,l=t.length;s===null&&a<l;a++)s=this.valueSetReplace(t[a],n,r);return s}valueSetReplace(t,n,r){let s=t.indexOf(n);return s>=0?(s+=r?1:-1,s<0?s=t.length-1:s%=t.length,t[s]):null}}at.INSTANCE=new at;const jt=Object.freeze(function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}});var Ue;(function(e){function t(n){return n===e.None||n===e.Cancelled||n instanceof Ie?!0:!n||typeof n!="object"?!1:typeof n.isCancellationRequested=="boolean"&&typeof n.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Ye.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:jt})})(Ue||(Ue={}));class Ie{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?jt:(this._emitter||(this._emitter=new J),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}}class vs{constructor(t){this._token=void 0,this._parentListener=void 0,this._parentListener=t&&t.onCancellationRequested(this.cancel,this)}get token(){return this._token||(this._token=new Ie),this._token}cancel(){this._token?this._token instanceof Ie&&this._token.cancel():this._token=Ue.Cancelled}dispose(t=!1){var n;t&&this.cancel(),(n=this._parentListener)===null||n===void 0||n.dispose(),this._token?this._token instanceof Ie&&this._token.dispose():this._token=Ue.None}}class lt{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(t,n){this._keyCodeToStr[t]=n,this._strToKeyCode[n.toLowerCase()]=t}keyCodeToStr(t){return this._keyCodeToStr[t]}strToKeyCode(t){return this._strToKeyCode[t.toLowerCase()]||0}}const qe=new lt,ot=new lt,ut=new lt,Ns=new Array(230),Ss=Object.create(null),Cs=Object.create(null);(function(){const e="",t=[[0,1,0,"None",0,"unknown",0,"VK_UNKNOWN",e,e],[0,1,1,"Hyper",0,e,0,e,e,e],[0,1,2,"Super",0,e,0,e,e,e],[0,1,3,"Fn",0,e,0,e,e,e],[0,1,4,"FnLock",0,e,0,e,e,e],[0,1,5,"Suspend",0,e,0,e,e,e],[0,1,6,"Resume",0,e,0,e,e,e],[0,1,7,"Turbo",0,e,0,e,e,e],[0,1,8,"Sleep",0,e,0,"VK_SLEEP",e,e],[0,1,9,"WakeUp",0,e,0,e,e,e],[31,0,10,"KeyA",31,"A",65,"VK_A",e,e],[32,0,11,"KeyB",32,"B",66,"VK_B",e,e],[33,0,12,"KeyC",33,"C",67,"VK_C",e,e],[34,0,13,"KeyD",34,"D",68,"VK_D",e,e],[35,0,14,"KeyE",35,"E",69,"VK_E",e,e],[36,0,15,"KeyF",36,"F",70,"VK_F",e,e],[37,0,16,"KeyG",37,"G",71,"VK_G",e,e],[38,0,17,"KeyH",38,"H",72,"VK_H",e,e],[39,0,18,"KeyI",39,"I",73,"VK_I",e,e],[40,0,19,"KeyJ",40,"J",74,"VK_J",e,e],[41,0,20,"KeyK",41,"K",75,"VK_K",e,e],[42,0,21,"KeyL",42,"L",76,"VK_L",e,e],[43,0,22,"KeyM",43,"M",77,"VK_M",e,e],[44,0,23,"KeyN",44,"N",78,"VK_N",e,e],[45,0,24,"KeyO",45,"O",79,"VK_O",e,e],[46,0,25,"KeyP",46,"P",80,"VK_P",e,e],[47,0,26,"KeyQ",47,"Q",81,"VK_Q",e,e],[48,0,27,"KeyR",48,"R",82,"VK_R",e,e],[49,0,28,"KeyS",49,"S",83,"VK_S",e,e],[50,0,29,"KeyT",50,"T",84,"VK_T",e,e],[51,0,30,"KeyU",51,"U",85,"VK_U",e,e],[52,0,31,"KeyV",52,"V",86,"VK_V",e,e],[53,0,32,"KeyW",53,"W",87,"VK_W",e,e],[54,0,33,"KeyX",54,"X",88,"VK_X",e,e],[55,0,34,"KeyY",55,"Y",89,"VK_Y",e,e],[56,0,35,"KeyZ",56,"Z",90,"VK_Z",e,e],[22,0,36,"Digit1",22,"1",49,"VK_1",e,e],[23,0,37,"Digit2",23,"2",50,"VK_2",e,e],[24,0,38,"Digit3",24,"3",51,"VK_3",e,e],[25,0,39,"Digit4",25,"4",52,"VK_4",e,e],[26,0,40,"Digit5",26,"5",53,"VK_5",e,e],[27,0,41,"Digit6",27,"6",54,"VK_6",e,e],[28,0,42,"Digit7",28,"7",55,"VK_7",e,e],[29,0,43,"Digit8",29,"8",56,"VK_8",e,e],[30,0,44,"Digit9",30,"9",57,"VK_9",e,e],[21,0,45,"Digit0",21,"0",48,"VK_0",e,e],[3,1,46,"Enter",3,"Enter",13,"VK_RETURN",e,e],[9,1,47,"Escape",9,"Escape",27,"VK_ESCAPE",e,e],[1,1,48,"Backspace",1,"Backspace",8,"VK_BACK",e,e],[2,1,49,"Tab",2,"Tab",9,"VK_TAB",e,e],[10,1,50,"Space",10,"Space",32,"VK_SPACE",e,e],[83,0,51,"Minus",83,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[81,0,52,"Equal",81,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[87,0,53,"BracketLeft",87,"[",219,"VK_OEM_4","[","OEM_4"],[89,0,54,"BracketRight",89,"]",221,"VK_OEM_6","]","OEM_6"],[88,0,55,"Backslash",88,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,0,56,"IntlHash",0,e,0,e,e,e],[80,0,57,"Semicolon",80,";",186,"VK_OEM_1",";","OEM_1"],[90,0,58,"Quote",90,"'",222,"VK_OEM_7","'","OEM_7"],[86,0,59,"Backquote",86,"`",192,"VK_OEM_3","`","OEM_3"],[82,0,60,"Comma",82,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[84,0,61,"Period",84,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[85,0,62,"Slash",85,"/",191,"VK_OEM_2","/","OEM_2"],[8,1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL",e,e],[59,1,64,"F1",59,"F1",112,"VK_F1",e,e],[60,1,65,"F2",60,"F2",113,"VK_F2",e,e],[61,1,66,"F3",61,"F3",114,"VK_F3",e,e],[62,1,67,"F4",62,"F4",115,"VK_F4",e,e],[63,1,68,"F5",63,"F5",116,"VK_F5",e,e],[64,1,69,"F6",64,"F6",117,"VK_F6",e,e],[65,1,70,"F7",65,"F7",118,"VK_F7",e,e],[66,1,71,"F8",66,"F8",119,"VK_F8",e,e],[67,1,72,"F9",67,"F9",120,"VK_F9",e,e],[68,1,73,"F10",68,"F10",121,"VK_F10",e,e],[69,1,74,"F11",69,"F11",122,"VK_F11",e,e],[70,1,75,"F12",70,"F12",123,"VK_F12",e,e],[0,1,76,"PrintScreen",0,e,0,e,e,e],[79,1,77,"ScrollLock",79,"ScrollLock",145,"VK_SCROLL",e,e],[7,1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE",e,e],[19,1,79,"Insert",19,"Insert",45,"VK_INSERT",e,e],[14,1,80,"Home",14,"Home",36,"VK_HOME",e,e],[11,1,81,"PageUp",11,"PageUp",33,"VK_PRIOR",e,e],[20,1,82,"Delete",20,"Delete",46,"VK_DELETE",e,e],[13,1,83,"End",13,"End",35,"VK_END",e,e],[12,1,84,"PageDown",12,"PageDown",34,"VK_NEXT",e,e],[17,1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",e],[15,1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",e],[18,1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",e],[16,1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",e],[78,1,89,"NumLock",78,"NumLock",144,"VK_NUMLOCK",e,e],[108,1,90,"NumpadDivide",108,"NumPad_Divide",111,"VK_DIVIDE",e,e],[103,1,91,"NumpadMultiply",103,"NumPad_Multiply",106,"VK_MULTIPLY",e,e],[106,1,92,"NumpadSubtract",106,"NumPad_Subtract",109,"VK_SUBTRACT",e,e],[104,1,93,"NumpadAdd",104,"NumPad_Add",107,"VK_ADD",e,e],[3,1,94,"NumpadEnter",3,e,0,e,e,e],[94,1,95,"Numpad1",94,"NumPad1",97,"VK_NUMPAD1",e,e],[95,1,96,"Numpad2",95,"NumPad2",98,"VK_NUMPAD2",e,e],[96,1,97,"Numpad3",96,"NumPad3",99,"VK_NUMPAD3",e,e],[97,1,98,"Numpad4",97,"NumPad4",100,"VK_NUMPAD4",e,e],[98,1,99,"Numpad5",98,"NumPad5",101,"VK_NUMPAD5",e,e],[99,1,100,"Numpad6",99,"NumPad6",102,"VK_NUMPAD6",e,e],[100,1,101,"Numpad7",100,"NumPad7",103,"VK_NUMPAD7",e,e],[101,1,102,"Numpad8",101,"NumPad8",104,"VK_NUMPAD8",e,e],[102,1,103,"Numpad9",102,"NumPad9",105,"VK_NUMPAD9",e,e],[93,1,104,"Numpad0",93,"NumPad0",96,"VK_NUMPAD0",e,e],[107,1,105,"NumpadDecimal",107,"NumPad_Decimal",110,"VK_DECIMAL",e,e],[92,0,106,"IntlBackslash",92,"OEM_102",226,"VK_OEM_102",e,e],[58,1,107,"ContextMenu",58,"ContextMenu",93,e,e,e],[0,1,108,"Power",0,e,0,e,e,e],[0,1,109,"NumpadEqual",0,e,0,e,e,e],[71,1,110,"F13",71,"F13",124,"VK_F13",e,e],[72,1,111,"F14",72,"F14",125,"VK_F14",e,e],[73,1,112,"F15",73,"F15",126,"VK_F15",e,e],[74,1,113,"F16",74,"F16",127,"VK_F16",e,e],[75,1,114,"F17",75,"F17",128,"VK_F17",e,e],[76,1,115,"F18",76,"F18",129,"VK_F18",e,e],[77,1,116,"F19",77,"F19",130,"VK_F19",e,e],[0,1,117,"F20",0,e,0,"VK_F20",e,e],[0,1,118,"F21",0,e,0,"VK_F21",e,e],[0,1,119,"F22",0,e,0,"VK_F22",e,e],[0,1,120,"F23",0,e,0,"VK_F23",e,e],[0,1,121,"F24",0,e,0,"VK_F24",e,e],[0,1,122,"Open",0,e,0,e,e,e],[0,1,123,"Help",0,e,0,e,e,e],[0,1,124,"Select",0,e,0,e,e,e],[0,1,125,"Again",0,e,0,e,e,e],[0,1,126,"Undo",0,e,0,e,e,e],[0,1,127,"Cut",0,e,0,e,e,e],[0,1,128,"Copy",0,e,0,e,e,e],[0,1,129,"Paste",0,e,0,e,e,e],[0,1,130,"Find",0,e,0,e,e,e],[0,1,131,"AudioVolumeMute",112,"AudioVolumeMute",173,"VK_VOLUME_MUTE",e,e],[0,1,132,"AudioVolumeUp",113,"AudioVolumeUp",175,"VK_VOLUME_UP",e,e],[0,1,133,"AudioVolumeDown",114,"AudioVolumeDown",174,"VK_VOLUME_DOWN",e,e],[105,1,134,"NumpadComma",105,"NumPad_Separator",108,"VK_SEPARATOR",e,e],[110,0,135,"IntlRo",110,"ABNT_C1",193,"VK_ABNT_C1",e,e],[0,1,136,"KanaMode",0,e,0,e,e,e],[0,0,137,"IntlYen",0,e,0,e,e,e],[0,1,138,"Convert",0,e,0,e,e,e],[0,1,139,"NonConvert",0,e,0,e,e,e],[0,1,140,"Lang1",0,e,0,e,e,e],[0,1,141,"Lang2",0,e,0,e,e,e],[0,1,142,"Lang3",0,e,0,e,e,e],[0,1,143,"Lang4",0,e,0,e,e,e],[0,1,144,"Lang5",0,e,0,e,e,e],[0,1,145,"Abort",0,e,0,e,e,e],[0,1,146,"Props",0,e,0,e,e,e],[0,1,147,"NumpadParenLeft",0,e,0,e,e,e],[0,1,148,"NumpadParenRight",0,e,0,e,e,e],[0,1,149,"NumpadBackspace",0,e,0,e,e,e],[0,1,150,"NumpadMemoryStore",0,e,0,e,e,e],[0,1,151,"NumpadMemoryRecall",0,e,0,e,e,e],[0,1,152,"NumpadMemoryClear",0,e,0,e,e,e],[0,1,153,"NumpadMemoryAdd",0,e,0,e,e,e],[0,1,154,"NumpadMemorySubtract",0,e,0,e,e,e],[0,1,155,"NumpadClear",126,"Clear",12,"VK_CLEAR",e,e],[0,1,156,"NumpadClearEntry",0,e,0,e,e,e],[5,1,0,e,5,"Ctrl",17,"VK_CONTROL",e,e],[4,1,0,e,4,"Shift",16,"VK_SHIFT",e,e],[6,1,0,e,6,"Alt",18,"VK_MENU",e,e],[57,1,0,e,57,"Meta",0,"VK_COMMAND",e,e],[5,1,157,"ControlLeft",5,e,0,"VK_LCONTROL",e,e],[4,1,158,"ShiftLeft",4,e,0,"VK_LSHIFT",e,e],[6,1,159,"AltLeft",6,e,0,"VK_LMENU",e,e],[57,1,160,"MetaLeft",57,e,0,"VK_LWIN",e,e],[5,1,161,"ControlRight",5,e,0,"VK_RCONTROL",e,e],[4,1,162,"ShiftRight",4,e,0,"VK_RSHIFT",e,e],[6,1,163,"AltRight",6,e,0,"VK_RMENU",e,e],[57,1,164,"MetaRight",57,e,0,"VK_RWIN",e,e],[0,1,165,"BrightnessUp",0,e,0,e,e,e],[0,1,166,"BrightnessDown",0,e,0,e,e,e],[0,1,167,"MediaPlay",0,e,0,e,e,e],[0,1,168,"MediaRecord",0,e,0,e,e,e],[0,1,169,"MediaFastForward",0,e,0,e,e,e],[0,1,170,"MediaRewind",0,e,0,e,e,e],[114,1,171,"MediaTrackNext",119,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK",e,e],[115,1,172,"MediaTrackPrevious",120,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK",e,e],[116,1,173,"MediaStop",121,"MediaStop",178,"VK_MEDIA_STOP",e,e],[0,1,174,"Eject",0,e,0,e,e,e],[117,1,175,"MediaPlayPause",122,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE",e,e],[0,1,176,"MediaSelect",123,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT",e,e],[0,1,177,"LaunchMail",124,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL",e,e],[0,1,178,"LaunchApp2",125,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2",e,e],[0,1,179,"LaunchApp1",0,e,0,"VK_MEDIA_LAUNCH_APP1",e,e],[0,1,180,"SelectTask",0,e,0,e,e,e],[0,1,181,"LaunchScreenSaver",0,e,0,e,e,e],[0,1,182,"BrowserSearch",115,"BrowserSearch",170,"VK_BROWSER_SEARCH",e,e],[0,1,183,"BrowserHome",116,"BrowserHome",172,"VK_BROWSER_HOME",e,e],[112,1,184,"BrowserBack",117,"BrowserBack",166,"VK_BROWSER_BACK",e,e],[113,1,185,"BrowserForward",118,"BrowserForward",167,"VK_BROWSER_FORWARD",e,e],[0,1,186,"BrowserStop",0,e,0,"VK_BROWSER_STOP",e,e],[0,1,187,"BrowserRefresh",0,e,0,"VK_BROWSER_REFRESH",e,e],[0,1,188,"BrowserFavorites",0,e,0,"VK_BROWSER_FAVORITES",e,e],[0,1,189,"ZoomToggle",0,e,0,e,e,e],[0,1,190,"MailReply",0,e,0,e,e,e],[0,1,191,"MailForward",0,e,0,e,e,e],[0,1,192,"MailSend",0,e,0,e,e,e],[109,1,0,e,109,"KeyInComposition",229,e,e,e],[111,1,0,e,111,"ABNT_C2",194,"VK_ABNT_C2",e,e],[91,1,0,e,91,"OEM_8",223,"VK_OEM_8",e,e],[0,1,0,e,0,e,0,"VK_KANA",e,e],[0,1,0,e,0,e,0,"VK_HANGUL",e,e],[0,1,0,e,0,e,0,"VK_JUNJA",e,e],[0,1,0,e,0,e,0,"VK_FINAL",e,e],[0,1,0,e,0,e,0,"VK_HANJA",e,e],[0,1,0,e,0,e,0,"VK_KANJI",e,e],[0,1,0,e,0,e,0,"VK_CONVERT",e,e],[0,1,0,e,0,e,0,"VK_NONCONVERT",e,e],[0,1,0,e,0,e,0,"VK_ACCEPT",e,e],[0,1,0,e,0,e,0,"VK_MODECHANGE",e,e],[0,1,0,e,0,e,0,"VK_SELECT",e,e],[0,1,0,e,0,e,0,"VK_PRINT",e,e],[0,1,0,e,0,e,0,"VK_EXECUTE",e,e],[0,1,0,e,0,e,0,"VK_SNAPSHOT",e,e],[0,1,0,e,0,e,0,"VK_HELP",e,e],[0,1,0,e,0,e,0,"VK_APPS",e,e],[0,1,0,e,0,e,0,"VK_PROCESSKEY",e,e],[0,1,0,e,0,e,0,"VK_PACKET",e,e],[0,1,0,e,0,e,0,"VK_DBE_SBCSCHAR",e,e],[0,1,0,e,0,e,0,"VK_DBE_DBCSCHAR",e,e],[0,1,0,e,0,e,0,"VK_ATTN",e,e],[0,1,0,e,0,e,0,"VK_CRSEL",e,e],[0,1,0,e,0,e,0,"VK_EXSEL",e,e],[0,1,0,e,0,e,0,"VK_EREOF",e,e],[0,1,0,e,0,e,0,"VK_PLAY",e,e],[0,1,0,e,0,e,0,"VK_ZOOM",e,e],[0,1,0,e,0,e,0,"VK_NONAME",e,e],[0,1,0,e,0,e,0,"VK_PA1",e,e],[0,1,0,e,0,e,0,"VK_OEM_CLEAR",e,e]],n=[],r=[];for(const s of t){const[a,l,o,c,u,f,h,d,m,b]=s;if(r[o]||(r[o]=!0,Ss[c]=o,Cs[c.toLowerCase()]=o),!n[u]){if(n[u]=!0,!f)throw new Error(`String representation missing for key code ${u} around scan code ${c}`);qe.define(u,f),ot.define(u,m||f),ut.define(u,b||m||f)}h&&(Ns[h]=u)}})();var Xt;(function(e){function t(o){return qe.keyCodeToStr(o)}e.toString=t;function n(o){return qe.strToKeyCode(o)}e.fromString=n;function r(o){return ot.keyCodeToStr(o)}e.toUserSettingsUS=r;function s(o){return ut.keyCodeToStr(o)}e.toUserSettingsGeneral=s;function a(o){return ot.strToKeyCode(o)||ut.strToKeyCode(o)}e.fromUserSettings=a;function l(o){if(o>=93&&o<=108)return null;switch(o){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return qe.keyCodeToStr(o)}e.toElectronAccelerator=l})(Xt||(Xt={}));function As(e,t){const n=(t&65535)<<16>>>0;return(e|n)>>>0}class O extends M{constructor(t,n,r,s){super(t,n,r,s),this.selectionStartLineNumber=t,this.selectionStartColumn=n,this.positionLineNumber=r,this.positionColumn=s}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(t){return O.selectionsEqual(this,t)}static selectionsEqual(t,n){return t.selectionStartLineNumber===n.selectionStartLineNumber&&t.selectionStartColumn===n.selectionStartColumn&&t.positionLineNumber===n.positionLineNumber&&t.positionColumn===n.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(t,n){return this.getDirection()===0?new O(this.startLineNumber,this.startColumn,t,n):new O(t,n,this.startLineNumber,this.startColumn)}getPosition(){return new H(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new H(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(t,n){return this.getDirection()===0?new O(t,n,this.endLineNumber,this.endColumn):new O(this.endLineNumber,this.endColumn,t,n)}static fromPositions(t,n=t){return new O(t.lineNumber,t.column,n.lineNumber,n.column)}static fromRange(t,n){return n===0?new O(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):new O(t.endLineNumber,t.endColumn,t.startLineNumber,t.startColumn)}static liftSelection(t){return new O(t.selectionStartLineNumber,t.selectionStartColumn,t.positionLineNumber,t.positionColumn)}static selectionsArrEqual(t,n){if(t&&!n||!t&&n)return!1;if(!t&&!n)return!0;if(t.length!==n.length)return!1;for(let r=0,s=t.length;r<s;r++)if(!this.selectionsEqual(t[r],n[r]))return!1;return!0}static isISelection(t){return t&&typeof t.selectionStartLineNumber=="number"&&typeof t.selectionStartColumn=="number"&&typeof t.positionLineNumber=="number"&&typeof t.positionColumn=="number"}static createWithDirection(t,n,r,s,a){return a===0?new O(t,n,r,s):new O(r,s,t,n)}}const Qt=Object.create(null);function i(e,t){if(Cr(t)){const n=Qt[t];if(n===void 0)throw new Error(`${e} references an unknown codicon: ${t}`);t=n}return Qt[e]=t,{id:e}}const A={add:i("add",6e4),plus:i("plus",6e4),gistNew:i("gist-new",6e4),repoCreate:i("repo-create",6e4),lightbulb:i("lightbulb",60001),lightBulb:i("light-bulb",60001),repo:i("repo",60002),repoDelete:i("repo-delete",60002),gistFork:i("gist-fork",60003),repoForked:i("repo-forked",60003),gitPullRequest:i("git-pull-request",60004),gitPullRequestAbandoned:i("git-pull-request-abandoned",60004),recordKeys:i("record-keys",60005),keyboard:i("keyboard",60005),tag:i("tag",60006),tagAdd:i("tag-add",60006),tagRemove:i("tag-remove",60006),person:i("person",60007),personFollow:i("person-follow",60007),personOutline:i("person-outline",60007),personFilled:i("person-filled",60007),gitBranch:i("git-branch",60008),gitBranchCreate:i("git-branch-create",60008),gitBranchDelete:i("git-branch-delete",60008),sourceControl:i("source-control",60008),mirror:i("mirror",60009),mirrorPublic:i("mirror-public",60009),star:i("star",60010),starAdd:i("star-add",60010),starDelete:i("star-delete",60010),starEmpty:i("star-empty",60010),comment:i("comment",60011),commentAdd:i("comment-add",60011),alert:i("alert",60012),warning:i("warning",60012),search:i("search",60013),searchSave:i("search-save",60013),logOut:i("log-out",60014),signOut:i("sign-out",60014),logIn:i("log-in",60015),signIn:i("sign-in",60015),eye:i("eye",60016),eyeUnwatch:i("eye-unwatch",60016),eyeWatch:i("eye-watch",60016),circleFilled:i("circle-filled",60017),primitiveDot:i("primitive-dot",60017),closeDirty:i("close-dirty",60017),debugBreakpoint:i("debug-breakpoint",60017),debugBreakpointDisabled:i("debug-breakpoint-disabled",60017),debugHint:i("debug-hint",60017),primitiveSquare:i("primitive-square",60018),edit:i("edit",60019),pencil:i("pencil",60019),info:i("info",60020),issueOpened:i("issue-opened",60020),gistPrivate:i("gist-private",60021),gitForkPrivate:i("git-fork-private",60021),lock:i("lock",60021),mirrorPrivate:i("mirror-private",60021),close:i("close",60022),removeClose:i("remove-close",60022),x:i("x",60022),repoSync:i("repo-sync",60023),sync:i("sync",60023),clone:i("clone",60024),desktopDownload:i("desktop-download",60024),beaker:i("beaker",60025),microscope:i("microscope",60025),vm:i("vm",60026),deviceDesktop:i("device-desktop",60026),file:i("file",60027),fileText:i("file-text",60027),more:i("more",60028),ellipsis:i("ellipsis",60028),kebabHorizontal:i("kebab-horizontal",60028),mailReply:i("mail-reply",60029),reply:i("reply",60029),organization:i("organization",60030),organizationFilled:i("organization-filled",60030),organizationOutline:i("organization-outline",60030),newFile:i("new-file",60031),fileAdd:i("file-add",60031),newFolder:i("new-folder",60032),fileDirectoryCreate:i("file-directory-create",60032),trash:i("trash",60033),trashcan:i("trashcan",60033),history:i("history",60034),clock:i("clock",60034),folder:i("folder",60035),fileDirectory:i("file-directory",60035),symbolFolder:i("symbol-folder",60035),logoGithub:i("logo-github",60036),markGithub:i("mark-github",60036),github:i("github",60036),terminal:i("terminal",60037),console:i("console",60037),repl:i("repl",60037),zap:i("zap",60038),symbolEvent:i("symbol-event",60038),error:i("error",60039),stop:i("stop",60039),variable:i("variable",60040),symbolVariable:i("symbol-variable",60040),array:i("array",60042),symbolArray:i("symbol-array",60042),symbolModule:i("symbol-module",60043),symbolPackage:i("symbol-package",60043),symbolNamespace:i("symbol-namespace",60043),symbolObject:i("symbol-object",60043),symbolMethod:i("symbol-method",60044),symbolFunction:i("symbol-function",60044),symbolConstructor:i("symbol-constructor",60044),symbolBoolean:i("symbol-boolean",60047),symbolNull:i("symbol-null",60047),symbolNumeric:i("symbol-numeric",60048),symbolNumber:i("symbol-number",60048),symbolStructure:i("symbol-structure",60049),symbolStruct:i("symbol-struct",60049),symbolParameter:i("symbol-parameter",60050),symbolTypeParameter:i("symbol-type-parameter",60050),symbolKey:i("symbol-key",60051),symbolText:i("symbol-text",60051),symbolReference:i("symbol-reference",60052),goToFile:i("go-to-file",60052),symbolEnum:i("symbol-enum",60053),symbolValue:i("symbol-value",60053),symbolRuler:i("symbol-ruler",60054),symbolUnit:i("symbol-unit",60054),activateBreakpoints:i("activate-breakpoints",60055),archive:i("archive",60056),arrowBoth:i("arrow-both",60057),arrowDown:i("arrow-down",60058),arrowLeft:i("arrow-left",60059),arrowRight:i("arrow-right",60060),arrowSmallDown:i("arrow-small-down",60061),arrowSmallLeft:i("arrow-small-left",60062),arrowSmallRight:i("arrow-small-right",60063),arrowSmallUp:i("arrow-small-up",60064),arrowUp:i("arrow-up",60065),bell:i("bell",60066),bold:i("bold",60067),book:i("book",60068),bookmark:i("bookmark",60069),debugBreakpointConditionalUnverified:i("debug-breakpoint-conditional-unverified",60070),debugBreakpointConditional:i("debug-breakpoint-conditional",60071),debugBreakpointConditionalDisabled:i("debug-breakpoint-conditional-disabled",60071),debugBreakpointDataUnverified:i("debug-breakpoint-data-unverified",60072),debugBreakpointData:i("debug-breakpoint-data",60073),debugBreakpointDataDisabled:i("debug-breakpoint-data-disabled",60073),debugBreakpointLogUnverified:i("debug-breakpoint-log-unverified",60074),debugBreakpointLog:i("debug-breakpoint-log",60075),debugBreakpointLogDisabled:i("debug-breakpoint-log-disabled",60075),briefcase:i("briefcase",60076),broadcast:i("broadcast",60077),browser:i("browser",60078),bug:i("bug",60079),calendar:i("calendar",60080),caseSensitive:i("case-sensitive",60081),check:i("check",60082),checklist:i("checklist",60083),chevronDown:i("chevron-down",60084),dropDownButton:i("drop-down-button",60084),chevronLeft:i("chevron-left",60085),chevronRight:i("chevron-right",60086),chevronUp:i("chevron-up",60087),chromeClose:i("chrome-close",60088),chromeMaximize:i("chrome-maximize",60089),chromeMinimize:i("chrome-minimize",60090),chromeRestore:i("chrome-restore",60091),circle:i("circle",60092),circleOutline:i("circle-outline",60092),debugBreakpointUnverified:i("debug-breakpoint-unverified",60092),circleSlash:i("circle-slash",60093),circuitBoard:i("circuit-board",60094),clearAll:i("clear-all",60095),clippy:i("clippy",60096),closeAll:i("close-all",60097),cloudDownload:i("cloud-download",60098),cloudUpload:i("cloud-upload",60099),code:i("code",60100),collapseAll:i("collapse-all",60101),colorMode:i("color-mode",60102),commentDiscussion:i("comment-discussion",60103),compareChanges:i("compare-changes",60157),creditCard:i("credit-card",60105),dash:i("dash",60108),dashboard:i("dashboard",60109),database:i("database",60110),debugContinue:i("debug-continue",60111),debugDisconnect:i("debug-disconnect",60112),debugPause:i("debug-pause",60113),debugRestart:i("debug-restart",60114),debugStart:i("debug-start",60115),debugStepInto:i("debug-step-into",60116),debugStepOut:i("debug-step-out",60117),debugStepOver:i("debug-step-over",60118),debugStop:i("debug-stop",60119),debug:i("debug",60120),deviceCameraVideo:i("device-camera-video",60121),deviceCamera:i("device-camera",60122),deviceMobile:i("device-mobile",60123),diffAdded:i("diff-added",60124),diffIgnored:i("diff-ignored",60125),diffModified:i("diff-modified",60126),diffRemoved:i("diff-removed",60127),diffRenamed:i("diff-renamed",60128),diff:i("diff",60129),discard:i("discard",60130),editorLayout:i("editor-layout",60131),emptyWindow:i("empty-window",60132),exclude:i("exclude",60133),extensions:i("extensions",60134),eyeClosed:i("eye-closed",60135),fileBinary:i("file-binary",60136),fileCode:i("file-code",60137),fileMedia:i("file-media",60138),filePdf:i("file-pdf",60139),fileSubmodule:i("file-submodule",60140),fileSymlinkDirectory:i("file-symlink-directory",60141),fileSymlinkFile:i("file-symlink-file",60142),fileZip:i("file-zip",60143),files:i("files",60144),filter:i("filter",60145),flame:i("flame",60146),foldDown:i("fold-down",60147),foldUp:i("fold-up",60148),fold:i("fold",60149),folderActive:i("folder-active",60150),folderOpened:i("folder-opened",60151),gear:i("gear",60152),gift:i("gift",60153),gistSecret:i("gist-secret",60154),gist:i("gist",60155),gitCommit:i("git-commit",60156),gitCompare:i("git-compare",60157),gitMerge:i("git-merge",60158),githubAction:i("github-action",60159),githubAlt:i("github-alt",60160),globe:i("globe",60161),grabber:i("grabber",60162),graph:i("graph",60163),gripper:i("gripper",60164),heart:i("heart",60165),home:i("home",60166),horizontalRule:i("horizontal-rule",60167),hubot:i("hubot",60168),inbox:i("inbox",60169),issueClosed:i("issue-closed",60324),issueReopened:i("issue-reopened",60171),issues:i("issues",60172),italic:i("italic",60173),jersey:i("jersey",60174),json:i("json",60175),bracket:i("bracket",60175),kebabVertical:i("kebab-vertical",60176),key:i("key",60177),law:i("law",60178),lightbulbAutofix:i("lightbulb-autofix",60179),linkExternal:i("link-external",60180),link:i("link",60181),listOrdered:i("list-ordered",60182),listUnordered:i("list-unordered",60183),liveShare:i("live-share",60184),loading:i("loading",60185),location:i("location",60186),mailRead:i("mail-read",60187),mail:i("mail",60188),markdown:i("markdown",60189),megaphone:i("megaphone",60190),mention:i("mention",60191),milestone:i("milestone",60192),mortarBoard:i("mortar-board",60193),move:i("move",60194),multipleWindows:i("multiple-windows",60195),mute:i("mute",60196),noNewline:i("no-newline",60197),note:i("note",60198),octoface:i("octoface",60199),openPreview:i("open-preview",60200),package_:i("package",60201),paintcan:i("paintcan",60202),pin:i("pin",60203),play:i("play",60204),run:i("run",60204),plug:i("plug",60205),preserveCase:i("preserve-case",60206),preview:i("preview",60207),project:i("project",60208),pulse:i("pulse",60209),question:i("question",60210),quote:i("quote",60211),radioTower:i("radio-tower",60212),reactions:i("reactions",60213),references:i("references",60214),refresh:i("refresh",60215),regex:i("regex",60216),remoteExplorer:i("remote-explorer",60217),remote:i("remote",60218),remove:i("remove",60219),replaceAll:i("replace-all",60220),replace:i("replace",60221),repoClone:i("repo-clone",60222),repoForcePush:i("repo-force-push",60223),repoPull:i("repo-pull",60224),repoPush:i("repo-push",60225),report:i("report",60226),requestChanges:i("request-changes",60227),rocket:i("rocket",60228),rootFolderOpened:i("root-folder-opened",60229),rootFolder:i("root-folder",60230),rss:i("rss",60231),ruby:i("ruby",60232),saveAll:i("save-all",60233),saveAs:i("save-as",60234),save:i("save",60235),screenFull:i("screen-full",60236),screenNormal:i("screen-normal",60237),searchStop:i("search-stop",60238),server:i("server",60240),settingsGear:i("settings-gear",60241),settings:i("settings",60242),shield:i("shield",60243),smiley:i("smiley",60244),sortPrecedence:i("sort-precedence",60245),splitHorizontal:i("split-horizontal",60246),splitVertical:i("split-vertical",60247),squirrel:i("squirrel",60248),starFull:i("star-full",60249),starHalf:i("star-half",60250),symbolClass:i("symbol-class",60251),symbolColor:i("symbol-color",60252),symbolCustomColor:i("symbol-customcolor",60252),symbolConstant:i("symbol-constant",60253),symbolEnumMember:i("symbol-enum-member",60254),symbolField:i("symbol-field",60255),symbolFile:i("symbol-file",60256),symbolInterface:i("symbol-interface",60257),symbolKeyword:i("symbol-keyword",60258),symbolMisc:i("symbol-misc",60259),symbolOperator:i("symbol-operator",60260),symbolProperty:i("symbol-property",60261),wrench:i("wrench",60261),wrenchSubaction:i("wrench-subaction",60261),symbolSnippet:i("symbol-snippet",60262),tasklist:i("tasklist",60263),telescope:i("telescope",60264),textSize:i("text-size",60265),threeBars:i("three-bars",60266),thumbsdown:i("thumbsdown",60267),thumbsup:i("thumbsup",60268),tools:i("tools",60269),triangleDown:i("triangle-down",60270),triangleLeft:i("triangle-left",60271),triangleRight:i("triangle-right",60272),triangleUp:i("triangle-up",60273),twitter:i("twitter",60274),unfold:i("unfold",60275),unlock:i("unlock",60276),unmute:i("unmute",60277),unverified:i("unverified",60278),verified:i("verified",60279),versions:i("versions",60280),vmActive:i("vm-active",60281),vmOutline:i("vm-outline",60282),vmRunning:i("vm-running",60283),watch:i("watch",60284),whitespace:i("whitespace",60285),wholeWord:i("whole-word",60286),window:i("window",60287),wordWrap:i("word-wrap",60288),zoomIn:i("zoom-in",60289),zoomOut:i("zoom-out",60290),listFilter:i("list-filter",60291),listFlat:i("list-flat",60292),listSelection:i("list-selection",60293),selection:i("selection",60293),listTree:i("list-tree",60294),debugBreakpointFunctionUnverified:i("debug-breakpoint-function-unverified",60295),debugBreakpointFunction:i("debug-breakpoint-function",60296),debugBreakpointFunctionDisabled:i("debug-breakpoint-function-disabled",60296),debugStackframeActive:i("debug-stackframe-active",60297),circleSmallFilled:i("circle-small-filled",60298),debugStackframeDot:i("debug-stackframe-dot",60298),debugStackframe:i("debug-stackframe",60299),debugStackframeFocused:i("debug-stackframe-focused",60299),debugBreakpointUnsupported:i("debug-breakpoint-unsupported",60300),symbolString:i("symbol-string",60301),debugReverseContinue:i("debug-reverse-continue",60302),debugStepBack:i("debug-step-back",60303),debugRestartFrame:i("debug-restart-frame",60304),callIncoming:i("call-incoming",60306),callOutgoing:i("call-outgoing",60307),menu:i("menu",60308),expandAll:i("expand-all",60309),feedback:i("feedback",60310),groupByRefType:i("group-by-ref-type",60311),ungroupByRefType:i("ungroup-by-ref-type",60312),account:i("account",60313),bellDot:i("bell-dot",60314),debugConsole:i("debug-console",60315),library:i("library",60316),output:i("output",60317),runAll:i("run-all",60318),syncIgnored:i("sync-ignored",60319),pinned:i("pinned",60320),githubInverted:i("github-inverted",60321),debugAlt:i("debug-alt",60305),serverProcess:i("server-process",60322),serverEnvironment:i("server-environment",60323),pass:i("pass",60324),stopCircle:i("stop-circle",60325),playCircle:i("play-circle",60326),record:i("record",60327),debugAltSmall:i("debug-alt-small",60328),vmConnect:i("vm-connect",60329),cloud:i("cloud",60330),merge:i("merge",60331),exportIcon:i("export",60332),graphLeft:i("graph-left",60333),magnet:i("magnet",60334),notebook:i("notebook",60335),redo:i("redo",60336),checkAll:i("check-all",60337),pinnedDirty:i("pinned-dirty",60338),passFilled:i("pass-filled",60339),circleLargeFilled:i("circle-large-filled",60340),circleLarge:i("circle-large",60341),circleLargeOutline:i("circle-large-outline",60341),combine:i("combine",60342),gather:i("gather",60342),table:i("table",60343),variableGroup:i("variable-group",60344),typeHierarchy:i("type-hierarchy",60345),typeHierarchySub:i("type-hierarchy-sub",60346),typeHierarchySuper:i("type-hierarchy-super",60347),gitPullRequestCreate:i("git-pull-request-create",60348),runAbove:i("run-above",60349),runBelow:i("run-below",60350),notebookTemplate:i("notebook-template",60351),debugRerun:i("debug-rerun",60352),workspaceTrusted:i("workspace-trusted",60353),workspaceUntrusted:i("workspace-untrusted",60354),workspaceUnspecified:i("workspace-unspecified",60355),terminalCmd:i("terminal-cmd",60356),terminalDebian:i("terminal-debian",60357),terminalLinux:i("terminal-linux",60358),terminalPowershell:i("terminal-powershell",60359),terminalTmux:i("terminal-tmux",60360),terminalUbuntu:i("terminal-ubuntu",60361),terminalBash:i("terminal-bash",60362),arrowSwap:i("arrow-swap",60363),copy:i("copy",60364),personAdd:i("person-add",60365),filterFilled:i("filter-filled",60366),wand:i("wand",60367),debugLineByLine:i("debug-line-by-line",60368),inspect:i("inspect",60369),layers:i("layers",60370),layersDot:i("layers-dot",60371),layersActive:i("layers-active",60372),compass:i("compass",60373),compassDot:i("compass-dot",60374),compassActive:i("compass-active",60375),azure:i("azure",60376),issueDraft:i("issue-draft",60377),gitPullRequestClosed:i("git-pull-request-closed",60378),gitPullRequestDraft:i("git-pull-request-draft",60379),debugAll:i("debug-all",60380),debugCoverage:i("debug-coverage",60381),runErrors:i("run-errors",60382),folderLibrary:i("folder-library",60383),debugContinueSmall:i("debug-continue-small",60384),beakerStop:i("beaker-stop",60385),graphLine:i("graph-line",60386),graphScatter:i("graph-scatter",60387),pieChart:i("pie-chart",60388),bracketDot:i("bracket-dot",60389),bracketError:i("bracket-error",60390),lockSmall:i("lock-small",60391),azureDevops:i("azure-devops",60392),verifiedFilled:i("verified-filled",60393),newLine:i("newline",60394),layout:i("layout",60395),layoutActivitybarLeft:i("layout-activitybar-left",60396),layoutActivitybarRight:i("layout-activitybar-right",60397),layoutPanelLeft:i("layout-panel-left",60398),layoutPanelCenter:i("layout-panel-center",60399),layoutPanelJustify:i("layout-panel-justify",60400),layoutPanelRight:i("layout-panel-right",60401),layoutPanel:i("layout-panel",60402),layoutSidebarLeft:i("layout-sidebar-left",60403),layoutSidebarRight:i("layout-sidebar-right",60404),layoutStatusbar:i("layout-statusbar",60405),layoutMenubar:i("layout-menubar",60406),layoutCentered:i("layout-centered",60407),layoutSidebarRightOff:i("layout-sidebar-right-off",60416),layoutPanelOff:i("layout-panel-off",60417),layoutSidebarLeftOff:i("layout-sidebar-left-off",60418),target:i("target",60408),indent:i("indent",60409),recordSmall:i("record-small",60410),errorSmall:i("error-small",60411),arrowCircleDown:i("arrow-circle-down",60412),arrowCircleLeft:i("arrow-circle-left",60413),arrowCircleRight:i("arrow-circle-right",60414),arrowCircleUp:i("arrow-circle-up",60415),heartFilled:i("heart-filled",60420),map:i("map",60421),mapFilled:i("map-filled",60422),circleSmall:i("circle-small",60423),bellSlash:i("bell-slash",60424),bellSlashDot:i("bell-slash-dot",60425),commentUnresolved:i("comment-unresolved",60426),gitPullRequestGoToChanges:i("git-pull-request-go-to-changes",60427),gitPullRequestNewChanges:i("git-pull-request-new-changes",60428),searchFuzzy:i("search-fuzzy",60429),commentDraft:i("comment-draft",60430),dialogError:i("dialog-error","error"),dialogWarning:i("dialog-warning","warning"),dialogInfo:i("dialog-info","info"),dialogClose:i("dialog-close","close"),treeItemExpanded:i("tree-item-expanded","chevron-down"),treeFilterOnTypeOn:i("tree-filter-on-type-on","list-filter"),treeFilterOnTypeOff:i("tree-filter-on-type-off","list-selection"),treeFilterClear:i("tree-filter-clear","close"),treeItemLoading:i("tree-item-loading","loading"),menuSelection:i("menu-selection","check"),menuSubmenu:i("menu-submenu","chevron-right"),menuBarMore:i("menubar-more","more"),scrollbarButtonLeft:i("scrollbar-button-left","triangle-left"),scrollbarButtonRight:i("scrollbar-button-right","triangle-right"),scrollbarButtonUp:i("scrollbar-button-up","triangle-up"),scrollbarButtonDown:i("scrollbar-button-down","triangle-down"),toolBarMore:i("toolbar-more","more"),quickInputBack:i("quick-input-back","arrow-left")};var ct=function(e,t,n,r){function s(a){return a instanceof n?a:new n(function(l){l(a)})}return new(n||(n=Promise))(function(a,l){function o(f){try{u(r.next(f))}catch(h){l(h)}}function c(f){try{u(r.throw(f))}catch(h){l(h)}}function u(f){f.done?a(f.value):s(f.value).then(o,c)}u((r=r.apply(e,t||[])).next())})};class ys{constructor(){this._map=new Map,this._factories=new Map,this._onDidChange=new J,this.onDidChange=this._onDidChange.event,this._colorMap=null}fire(t){this._onDidChange.fire({changedLanguages:t,changedColorMap:!1})}register(t,n){return this._map.set(t,n),this.fire([t]),ke(()=>{this._map.get(t)===n&&(this._map.delete(t),this.fire([t]))})}registerFactory(t,n){var r;(r=this._factories.get(t))===null||r===void 0||r.dispose();const s=new Rs(this,t,n);return this._factories.set(t,s),ke(()=>{const a=this._factories.get(t);!a||a!==s||(this._factories.delete(t),a.dispose())})}getOrCreate(t){return ct(this,void 0,void 0,function*(){const n=this.get(t);if(n)return n;const r=this._factories.get(t);return!r||r.isResolved?null:(yield r.resolve(),this.get(t))})}get(t){return this._map.get(t)||null}isResolved(t){if(this.get(t))return!0;const r=this._factories.get(t);return!!(!r||r.isResolved)}setColorMap(t){this._colorMap=t,this._onDidChange.fire({changedLanguages:Array.from(this._map.keys()),changedColorMap:!0})}getColorMap(){return this._colorMap}getDefaultBackground(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null}}class Rs extends Me{get isResolved(){return this._isResolved}constructor(t,n,r){super(),this._registry=t,this._languageId=n,this._factory=r,this._isDisposed=!1,this._resolvePromise=null,this._isResolved=!1}dispose(){this._isDisposed=!0,super.dispose()}resolve(){return ct(this,void 0,void 0,function*(){return this._resolvePromise||(this._resolvePromise=this._create()),this._resolvePromise})}_create(){return ct(this,void 0,void 0,function*(){const t=yield Promise.resolve(this._factory.createTokenizationSupport());this._isResolved=!0,t&&!this._isDisposed&&this._register(this._registry.register(this._languageId,t))})}}class ks{constructor(t,n,r){this.offset=t,this.type=n,this.language=r,this._tokenBrand=void 0}toString(){return"("+this.offset+", "+this.type+")"}}var Yt;(function(e){const t=new Map;t.set(0,A.symbolMethod),t.set(1,A.symbolFunction),t.set(2,A.symbolConstructor),t.set(3,A.symbolField),t.set(4,A.symbolVariable),t.set(5,A.symbolClass),t.set(6,A.symbolStruct),t.set(7,A.symbolInterface),t.set(8,A.symbolModule),t.set(9,A.symbolProperty),t.set(10,A.symbolEvent),t.set(11,A.symbolOperator),t.set(12,A.symbolUnit),t.set(13,A.symbolValue),t.set(15,A.symbolEnum),t.set(14,A.symbolConstant),t.set(15,A.symbolEnum),t.set(16,A.symbolEnumMember),t.set(17,A.symbolKeyword),t.set(27,A.symbolSnippet),t.set(18,A.symbolText),t.set(19,A.symbolColor),t.set(20,A.symbolFile),t.set(21,A.symbolReference),t.set(22,A.symbolCustomColor),t.set(23,A.symbolFolder),t.set(24,A.symbolTypeParameter),t.set(25,A.account),t.set(26,A.issues);function n(a){let l=t.get(a);return l||(console.info("No codicon found for CompletionItemKind "+a),l=A.symbolProperty),l}e.toIcon=n;const r=new Map;r.set("method",0),r.set("function",1),r.set("constructor",2),r.set("field",3),r.set("variable",4),r.set("class",5),r.set("struct",6),r.set("interface",7),r.set("module",8),r.set("property",9),r.set("event",10),r.set("operator",11),r.set("unit",12),r.set("value",13),r.set("constant",14),r.set("enum",15),r.set("enum-member",16),r.set("enumMember",16),r.set("keyword",17),r.set("snippet",27),r.set("text",18),r.set("color",19),r.set("file",20),r.set("reference",21),r.set("customcolor",22),r.set("folder",23),r.set("type-parameter",24),r.set("typeParameter",24),r.set("account",25),r.set("issue",26);function s(a,l){let o=r.get(a);return typeof o>"u"&&!l&&(o=9),o}e.fromString=s})(Yt||(Yt={}));var Zt;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"})(Zt||(Zt={}));var Jt;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(Jt||(Jt={}));var Kt;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(Kt||(Kt={}));var en;(function(e){const t=new Map;t.set(0,A.symbolFile),t.set(1,A.symbolModule),t.set(2,A.symbolNamespace),t.set(3,A.symbolPackage),t.set(4,A.symbolClass),t.set(5,A.symbolMethod),t.set(6,A.symbolProperty),t.set(7,A.symbolField),t.set(8,A.symbolConstructor),t.set(9,A.symbolEnum),t.set(10,A.symbolInterface),t.set(11,A.symbolFunction),t.set(12,A.symbolVariable),t.set(13,A.symbolConstant),t.set(14,A.symbolString),t.set(15,A.symbolNumber),t.set(16,A.symbolBoolean),t.set(17,A.symbolArray),t.set(18,A.symbolObject),t.set(19,A.symbolKey),t.set(20,A.symbolNull),t.set(21,A.symbolEnumMember),t.set(22,A.symbolStruct),t.set(23,A.symbolEvent),t.set(24,A.symbolOperator),t.set(25,A.symbolTypeParameter);function n(r){let s=t.get(r);return s||(console.info("No codicon found for SymbolKind "+r),s=A.symbolProperty),s}e.toIcon=n})(en||(en={}));var tn;(function(e){function t(n){return!n||typeof n!="object"?!1:typeof n.id=="string"&&typeof n.title=="string"}e.is=t})(tn||(tn={}));var nn;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(nn||(nn={})),new ys;var rn;(function(e){e[e.Unknown=0]="Unknown",e[e.Disabled=1]="Disabled",e[e.Enabled=2]="Enabled"})(rn||(rn={}));var sn;(function(e){e[e.Invoke=1]="Invoke",e[e.Auto=2]="Auto"})(sn||(sn={}));var an;(function(e){e[e.None=0]="None",e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"})(an||(an={}));var ln;(function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Snippet=27]="Snippet"})(ln||(ln={}));var on;(function(e){e[e.Deprecated=1]="Deprecated"})(on||(on={}));var un;(function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"})(un||(un={}));var cn;(function(e){e[e.EXACT=0]="EXACT",e[e.ABOVE=1]="ABOVE",e[e.BELOW=2]="BELOW"})(cn||(cn={}));var hn;(function(e){e[e.NotSet=0]="NotSet",e[e.ContentFlush=1]="ContentFlush",e[e.RecoverFromMarkers=2]="RecoverFromMarkers",e[e.Explicit=3]="Explicit",e[e.Paste=4]="Paste",e[e.Undo=5]="Undo",e[e.Redo=6]="Redo"})(hn||(hn={}));var fn;(function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(fn||(fn={}));var dn;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(dn||(dn={}));var mn;(function(e){e[e.None=0]="None",e[e.Keep=1]="Keep",e[e.Brackets=2]="Brackets",e[e.Advanced=3]="Advanced",e[e.Full=4]="Full"})(mn||(mn={}));var gn;(function(e){e[e.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",e[e.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",e[e.accessibilitySupport=2]="accessibilitySupport",e[e.accessibilityPageSize=3]="accessibilityPageSize",e[e.ariaLabel=4]="ariaLabel",e[e.autoClosingBrackets=5]="autoClosingBrackets",e[e.autoClosingDelete=6]="autoClosingDelete",e[e.autoClosingOvertype=7]="autoClosingOvertype",e[e.autoClosingQuotes=8]="autoClosingQuotes",e[e.autoIndent=9]="autoIndent",e[e.automaticLayout=10]="automaticLayout",e[e.autoSurround=11]="autoSurround",e[e.bracketPairColorization=12]="bracketPairColorization",e[e.guides=13]="guides",e[e.codeLens=14]="codeLens",e[e.codeLensFontFamily=15]="codeLensFontFamily",e[e.codeLensFontSize=16]="codeLensFontSize",e[e.colorDecorators=17]="colorDecorators",e[e.colorDecoratorsLimit=18]="colorDecoratorsLimit",e[e.columnSelection=19]="columnSelection",e[e.comments=20]="comments",e[e.contextmenu=21]="contextmenu",e[e.copyWithSyntaxHighlighting=22]="copyWithSyntaxHighlighting",e[e.cursorBlinking=23]="cursorBlinking",e[e.cursorSmoothCaretAnimation=24]="cursorSmoothCaretAnimation",e[e.cursorStyle=25]="cursorStyle",e[e.cursorSurroundingLines=26]="cursorSurroundingLines",e[e.cursorSurroundingLinesStyle=27]="cursorSurroundingLinesStyle",e[e.cursorWidth=28]="cursorWidth",e[e.disableLayerHinting=29]="disableLayerHinting",e[e.disableMonospaceOptimizations=30]="disableMonospaceOptimizations",e[e.domReadOnly=31]="domReadOnly",e[e.dragAndDrop=32]="dragAndDrop",e[e.dropIntoEditor=33]="dropIntoEditor",e[e.emptySelectionClipboard=34]="emptySelectionClipboard",e[e.experimentalWhitespaceRendering=35]="experimentalWhitespaceRendering",e[e.extraEditorClassName=36]="extraEditorClassName",e[e.fastScrollSensitivity=37]="fastScrollSensitivity",e[e.find=38]="find",e[e.fixedOverflowWidgets=39]="fixedOverflowWidgets",e[e.folding=40]="folding",e[e.foldingStrategy=41]="foldingStrategy",e[e.foldingHighlight=42]="foldingHighlight",e[e.foldingImportsByDefault=43]="foldingImportsByDefault",e[e.foldingMaximumRegions=44]="foldingMaximumRegions",e[e.unfoldOnClickAfterEndOfLine=45]="unfoldOnClickAfterEndOfLine",e[e.fontFamily=46]="fontFamily",e[e.fontInfo=47]="fontInfo",e[e.fontLigatures=48]="fontLigatures",e[e.fontSize=49]="fontSize",e[e.fontWeight=50]="fontWeight",e[e.fontVariations=51]="fontVariations",e[e.formatOnPaste=52]="formatOnPaste",e[e.formatOnType=53]="formatOnType",e[e.glyphMargin=54]="glyphMargin",e[e.gotoLocation=55]="gotoLocation",e[e.hideCursorInOverviewRuler=56]="hideCursorInOverviewRuler",e[e.hover=57]="hover",e[e.inDiffEditor=58]="inDiffEditor",e[e.inlineSuggest=59]="inlineSuggest",e[e.letterSpacing=60]="letterSpacing",e[e.lightbulb=61]="lightbulb",e[e.lineDecorationsWidth=62]="lineDecorationsWidth",e[e.lineHeight=63]="lineHeight",e[e.lineNumbers=64]="lineNumbers",e[e.lineNumbersMinChars=65]="lineNumbersMinChars",e[e.linkedEditing=66]="linkedEditing",e[e.links=67]="links",e[e.matchBrackets=68]="matchBrackets",e[e.minimap=69]="minimap",e[e.mouseStyle=70]="mouseStyle",e[e.mouseWheelScrollSensitivity=71]="mouseWheelScrollSensitivity",e[e.mouseWheelZoom=72]="mouseWheelZoom",e[e.multiCursorMergeOverlapping=73]="multiCursorMergeOverlapping",e[e.multiCursorModifier=74]="multiCursorModifier",e[e.multiCursorPaste=75]="multiCursorPaste",e[e.multiCursorLimit=76]="multiCursorLimit",e[e.occurrencesHighlight=77]="occurrencesHighlight",e[e.overviewRulerBorder=78]="overviewRulerBorder",e[e.overviewRulerLanes=79]="overviewRulerLanes",e[e.padding=80]="padding",e[e.parameterHints=81]="parameterHints",e[e.peekWidgetDefaultFocus=82]="peekWidgetDefaultFocus",e[e.definitionLinkOpensInPeek=83]="definitionLinkOpensInPeek",e[e.quickSuggestions=84]="quickSuggestions",e[e.quickSuggestionsDelay=85]="quickSuggestionsDelay",e[e.readOnly=86]="readOnly",e[e.renameOnType=87]="renameOnType",e[e.renderControlCharacters=88]="renderControlCharacters",e[e.renderFinalNewline=89]="renderFinalNewline",e[e.renderLineHighlight=90]="renderLineHighlight",e[e.renderLineHighlightOnlyWhenFocus=91]="renderLineHighlightOnlyWhenFocus",e[e.renderValidationDecorations=92]="renderValidationDecorations",e[e.renderWhitespace=93]="renderWhitespace",e[e.revealHorizontalRightPadding=94]="revealHorizontalRightPadding",e[e.roundedSelection=95]="roundedSelection",e[e.rulers=96]="rulers",e[e.scrollbar=97]="scrollbar",e[e.scrollBeyondLastColumn=98]="scrollBeyondLastColumn",e[e.scrollBeyondLastLine=99]="scrollBeyondLastLine",e[e.scrollPredominantAxis=100]="scrollPredominantAxis",e[e.selectionClipboard=101]="selectionClipboard",e[e.selectionHighlight=102]="selectionHighlight",e[e.selectOnLineNumbers=103]="selectOnLineNumbers",e[e.showFoldingControls=104]="showFoldingControls",e[e.showUnused=105]="showUnused",e[e.snippetSuggestions=106]="snippetSuggestions",e[e.smartSelect=107]="smartSelect",e[e.smoothScrolling=108]="smoothScrolling",e[e.stickyScroll=109]="stickyScroll",e[e.stickyTabStops=110]="stickyTabStops",e[e.stopRenderingLineAfter=111]="stopRenderingLineAfter",e[e.suggest=112]="suggest",e[e.suggestFontSize=113]="suggestFontSize",e[e.suggestLineHeight=114]="suggestLineHeight",e[e.suggestOnTriggerCharacters=115]="suggestOnTriggerCharacters",e[e.suggestSelection=116]="suggestSelection",e[e.tabCompletion=117]="tabCompletion",e[e.tabIndex=118]="tabIndex",e[e.unicodeHighlighting=119]="unicodeHighlighting",e[e.unusualLineTerminators=120]="unusualLineTerminators",e[e.useShadowDOM=121]="useShadowDOM",e[e.useTabStops=122]="useTabStops",e[e.wordBreak=123]="wordBreak",e[e.wordSeparators=124]="wordSeparators",e[e.wordWrap=125]="wordWrap",e[e.wordWrapBreakAfterCharacters=126]="wordWrapBreakAfterCharacters",e[e.wordWrapBreakBeforeCharacters=127]="wordWrapBreakBeforeCharacters",e[e.wordWrapColumn=128]="wordWrapColumn",e[e.wordWrapOverride1=129]="wordWrapOverride1",e[e.wordWrapOverride2=130]="wordWrapOverride2",e[e.wrappingIndent=131]="wrappingIndent",e[e.wrappingStrategy=132]="wrappingStrategy",e[e.showDeprecated=133]="showDeprecated",e[e.inlayHints=134]="inlayHints",e[e.editorClassName=135]="editorClassName",e[e.pixelRatio=136]="pixelRatio",e[e.tabFocusMode=137]="tabFocusMode",e[e.layoutInfo=138]="layoutInfo",e[e.wrappingInfo=139]="wrappingInfo"})(gn||(gn={}));var bn;(function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(bn||(bn={}));var _n;(function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"})(_n||(_n={}));var pn;(function(e){e[e.None=0]="None",e[e.Indent=1]="Indent",e[e.IndentOutdent=2]="IndentOutdent",e[e.Outdent=3]="Outdent"})(pn||(pn={}));var xn;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(xn||(xn={}));var wn;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(wn||(wn={}));var Ln;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"})(Ln||(Ln={}));var ht;(function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.Digit0=21]="Digit0",e[e.Digit1=22]="Digit1",e[e.Digit2=23]="Digit2",e[e.Digit3=24]="Digit3",e[e.Digit4=25]="Digit4",e[e.Digit5=26]="Digit5",e[e.Digit6=27]="Digit6",e[e.Digit7=28]="Digit7",e[e.Digit8=29]="Digit8",e[e.Digit9=30]="Digit9",e[e.KeyA=31]="KeyA",e[e.KeyB=32]="KeyB",e[e.KeyC=33]="KeyC",e[e.KeyD=34]="KeyD",e[e.KeyE=35]="KeyE",e[e.KeyF=36]="KeyF",e[e.KeyG=37]="KeyG",e[e.KeyH=38]="KeyH",e[e.KeyI=39]="KeyI",e[e.KeyJ=40]="KeyJ",e[e.KeyK=41]="KeyK",e[e.KeyL=42]="KeyL",e[e.KeyM=43]="KeyM",e[e.KeyN=44]="KeyN",e[e.KeyO=45]="KeyO",e[e.KeyP=46]="KeyP",e[e.KeyQ=47]="KeyQ",e[e.KeyR=48]="KeyR",e[e.KeyS=49]="KeyS",e[e.KeyT=50]="KeyT",e[e.KeyU=51]="KeyU",e[e.KeyV=52]="KeyV",e[e.KeyW=53]="KeyW",e[e.KeyX=54]="KeyX",e[e.KeyY=55]="KeyY",e[e.KeyZ=56]="KeyZ",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.NumLock=78]="NumLock",e[e.ScrollLock=79]="ScrollLock",e[e.Semicolon=80]="Semicolon",e[e.Equal=81]="Equal",e[e.Comma=82]="Comma",e[e.Minus=83]="Minus",e[e.Period=84]="Period",e[e.Slash=85]="Slash",e[e.Backquote=86]="Backquote",e[e.BracketLeft=87]="BracketLeft",e[e.Backslash=88]="Backslash",e[e.BracketRight=89]="BracketRight",e[e.Quote=90]="Quote",e[e.OEM_8=91]="OEM_8",e[e.IntlBackslash=92]="IntlBackslash",e[e.Numpad0=93]="Numpad0",e[e.Numpad1=94]="Numpad1",e[e.Numpad2=95]="Numpad2",e[e.Numpad3=96]="Numpad3",e[e.Numpad4=97]="Numpad4",e[e.Numpad5=98]="Numpad5",e[e.Numpad6=99]="Numpad6",e[e.Numpad7=100]="Numpad7",e[e.Numpad8=101]="Numpad8",e[e.Numpad9=102]="Numpad9",e[e.NumpadMultiply=103]="NumpadMultiply",e[e.NumpadAdd=104]="NumpadAdd",e[e.NUMPAD_SEPARATOR=105]="NUMPAD_SEPARATOR",e[e.NumpadSubtract=106]="NumpadSubtract",e[e.NumpadDecimal=107]="NumpadDecimal",e[e.NumpadDivide=108]="NumpadDivide",e[e.KEY_IN_COMPOSITION=109]="KEY_IN_COMPOSITION",e[e.ABNT_C1=110]="ABNT_C1",e[e.ABNT_C2=111]="ABNT_C2",e[e.AudioVolumeMute=112]="AudioVolumeMute",e[e.AudioVolumeUp=113]="AudioVolumeUp",e[e.AudioVolumeDown=114]="AudioVolumeDown",e[e.BrowserSearch=115]="BrowserSearch",e[e.BrowserHome=116]="BrowserHome",e[e.BrowserBack=117]="BrowserBack",e[e.BrowserForward=118]="BrowserForward",e[e.MediaTrackNext=119]="MediaTrackNext",e[e.MediaTrackPrevious=120]="MediaTrackPrevious",e[e.MediaStop=121]="MediaStop",e[e.MediaPlayPause=122]="MediaPlayPause",e[e.LaunchMediaPlayer=123]="LaunchMediaPlayer",e[e.LaunchMail=124]="LaunchMail",e[e.LaunchApp2=125]="LaunchApp2",e[e.Clear=126]="Clear",e[e.MAX_VALUE=127]="MAX_VALUE"})(ht||(ht={}));var ft;(function(e){e[e.Hint=1]="Hint",e[e.Info=2]="Info",e[e.Warning=4]="Warning",e[e.Error=8]="Error"})(ft||(ft={}));var dt;(function(e){e[e.Unnecessary=1]="Unnecessary",e[e.Deprecated=2]="Deprecated"})(dt||(dt={}));var vn;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(vn||(vn={}));var Nn;(function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.TEXTAREA=1]="TEXTAREA",e[e.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",e[e.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",e[e.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",e[e.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",e[e.CONTENT_TEXT=6]="CONTENT_TEXT",e[e.CONTENT_EMPTY=7]="CONTENT_EMPTY",e[e.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",e[e.CONTENT_WIDGET=9]="CONTENT_WIDGET",e[e.OVERVIEW_RULER=10]="OVERVIEW_RULER",e[e.SCROLLBAR=11]="SCROLLBAR",e[e.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",e[e.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"})(Nn||(Nn={}));var Sn;(function(e){e[e.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",e[e.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",e[e.TOP_CENTER=2]="TOP_CENTER"})(Sn||(Sn={}));var Cn;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(Cn||(Cn={}));var An;(function(e){e[e.Left=0]="Left",e[e.Right=1]="Right",e[e.None=2]="None",e[e.LeftOfInjectedText=3]="LeftOfInjectedText",e[e.RightOfInjectedText=4]="RightOfInjectedText"})(An||(An={}));var yn;(function(e){e[e.Off=0]="Off",e[e.On=1]="On",e[e.Relative=2]="Relative",e[e.Interval=3]="Interval",e[e.Custom=4]="Custom"})(yn||(yn={}));var Rn;(function(e){e[e.None=0]="None",e[e.Text=1]="Text",e[e.Blocks=2]="Blocks"})(Rn||(Rn={}));var kn;(function(e){e[e.Smooth=0]="Smooth",e[e.Immediate=1]="Immediate"})(kn||(kn={}));var Mn;(function(e){e[e.Auto=1]="Auto",e[e.Hidden=2]="Hidden",e[e.Visible=3]="Visible"})(Mn||(Mn={}));var mt;(function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"})(mt||(mt={}));var Pn;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(Pn||(Pn={}));var Dn;(function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"})(Dn||(Dn={}));var En;(function(e){e[e.Deprecated=1]="Deprecated"})(En||(En={}));var Fn;(function(e){e[e.Hidden=0]="Hidden",e[e.Blink=1]="Blink",e[e.Smooth=2]="Smooth",e[e.Phase=3]="Phase",e[e.Expand=4]="Expand",e[e.Solid=5]="Solid"})(Fn||(Fn={}));var Vn;(function(e){e[e.Line=1]="Line",e[e.Block=2]="Block",e[e.Underline=3]="Underline",e[e.LineThin=4]="LineThin",e[e.BlockOutline=5]="BlockOutline",e[e.UnderlineThin=6]="UnderlineThin"})(Vn||(Vn={}));var Bn;(function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"})(Bn||(Bn={}));var Tn;(function(e){e[e.None=0]="None",e[e.Same=1]="Same",e[e.Indent=2]="Indent",e[e.DeepIndent=3]="DeepIndent"})(Tn||(Tn={}));class Ae{static chord(t,n){return As(t,n)}}Ae.CtrlCmd=2048,Ae.Shift=1024,Ae.Alt=512,Ae.WinCtrl=256;function Ms(){return{editor:void 0,languages:void 0,CancellationTokenSource:vs,Emitter:J,KeyCode:ht,KeyMod:Ae,Position:H,Range:M,Selection:O,SelectionDirection:mt,MarkerSeverity:ft,MarkerTag:dt,Uri:ue,Token:ks}}var Un;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(Un||(Un={}));var In;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(In||(In={}));var qn;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(qn||(qn={}));function Ps(e,t,n,r,s){if(r===0)return!0;const a=t.charCodeAt(r-1);if(e.get(a)!==0||a===13||a===10)return!0;if(s>0){const l=t.charCodeAt(r);if(e.get(l)!==0)return!0}return!1}function Ds(e,t,n,r,s){if(r+s===n)return!0;const a=t.charCodeAt(r+s);if(e.get(a)!==0||a===13||a===10)return!0;if(s>0){const l=t.charCodeAt(r+s-1);if(e.get(l)!==0)return!0}return!1}function Es(e,t,n,r,s){return Ps(e,t,n,r,s)&&Ds(e,t,n,r,s)}class Fs{constructor(t,n){this._wordSeparators=t,this._searchRegex=n,this._prevMatchStartIndex=-1,this._prevMatchLength=0}reset(t){this._searchRegex.lastIndex=t,this._prevMatchStartIndex=-1,this._prevMatchLength=0}next(t){const n=t.length;let r;do{if(this._prevMatchStartIndex+this._prevMatchLength===n||(r=this._searchRegex.exec(t),!r))return null;const s=r.index,a=r[0].length;if(s===this._prevMatchStartIndex&&a===this._prevMatchLength){if(a===0){Vr(t,n,this._searchRegex.lastIndex)>65535?this._searchRegex.lastIndex+=2:this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=s,this._prevMatchLength=a,!this._wordSeparators||Es(this._wordSeparators,t,n,s,a))return r}while(r);return null}}function Vs(e,t="Unreachable"){throw new Error(t)}function gt(e){if(!e()){debugger;e(),Lt(new ze("Assertion Failed"))}}function Hn(e,t){let n=0;for(;n<e.length-1;){const r=e[n],s=e[n+1];if(!t(r,s))return!1;n++}return!0}class Bs{static computeUnicodeHighlights(t,n,r){const s=r?r.startLineNumber:1,a=r?r.endLineNumber:t.getLineCount(),l=new Wn(n),o=l.getCandidateCodePoints();let c;o==="allNonBasicAscii"?c=new RegExp("[^\\t\\n\\r\\x20-\\x7E]","g"):c=new RegExp(`${Ts(Array.from(o))}`,"g");const u=new Fs(null,c),f=[];let h=!1,d,m=0,b=0,C=0;e:for(let N=s,g=a;N<=g;N++){const S=t.getLineContent(N),L=S.length;u.reset(0);do if(d=u.next(S),d){let P=d.index,k=d.index+d[0].length;if(P>0){const _=S.charCodeAt(P-1);Ke(_)&&P--}if(k+1<L){const _=S.charCodeAt(k-1);Ke(_)&&k++}const G=S.substring(P,k);let B=rt(P+1,Ot,S,0);B&&B.endColumn<=P+1&&(B=null);const w=l.shouldHighlightNonBasicASCII(G,B?B.word:null);if(w!==0){if(w===3?m++:w===2?b++:w===1?C++:Vs(),f.length>=1e3){h=!0;break e}f.push(new M(N,P+1,N,k+1))}}while(d)}return{ranges:f,hasMore:h,ambiguousCharacterCount:m,invisibleCharacterCount:b,nonBasicAsciiCharacterCount:C}}static computeUnicodeHighlightReason(t,n){const r=new Wn(n);switch(r.shouldHighlightNonBasicASCII(t,null)){case 0:return null;case 2:return{kind:1};case 3:{const a=t.codePointAt(0),l=r.ambiguousCharacters.getPrimaryConfusable(a),o=X.getLocales().filter(c=>!X.getInstance(new Set([...n.allowedLocales,c])).isAmbiguous(a));return{kind:0,confusableWith:String.fromCodePoint(l),notAmbiguousInLocales:o}}case 1:return{kind:2}}}}function Ts(e,t){return`[${kr(e.map(r=>String.fromCodePoint(r)).join(""))}]`}class Wn{constructor(t){this.options=t,this.allowedCodePoints=new Set(t.allowedCodePoints),this.ambiguousCharacters=X.getInstance(new Set(t.allowedLocales))}getCandidateCodePoints(){if(this.options.nonBasicASCII)return"allNonBasicAscii";const t=new Set;if(this.options.invisibleCharacters)for(const n of te.codePoints)$n(String.fromCodePoint(n))||t.add(n);if(this.options.ambiguousCharacters)for(const n of this.ambiguousCharacters.getConfusableCodePoints())t.add(n);for(const n of this.allowedCodePoints)t.delete(n);return t}shouldHighlightNonBasicASCII(t,n){const r=t.codePointAt(0);if(this.allowedCodePoints.has(r))return 0;if(this.options.nonBasicASCII)return 1;let s=!1,a=!1;if(n)for(const l of n){const o=l.codePointAt(0),c=Tr(l);s=s||c,!c&&!this.ambiguousCharacters.isAmbiguous(o)&&!te.isInvisibleCharacter(o)&&(a=!0)}return!s&&a?0:this.options.invisibleCharacters&&!$n(t)&&te.isInvisibleCharacter(r)?2:this.options.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(r)?3:0}}function $n(e){return e===" "||e===`
`||e==="	"}class bt{constructor(t,n,r){this.originalRange=t,this.modifiedRange=n,this.innerChanges=r}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}}class zn{constructor(t,n){this.originalRange=t,this.modifiedRange=n}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}}class ee{constructor(t,n){this.startLineNumber=t,this.endLineNumberExclusive=n}get isEmpty(){return this.startLineNumber===this.endLineNumberExclusive}delta(t){return new ee(this.startLineNumber+t,this.endLineNumberExclusive+t)}get length(){return this.endLineNumberExclusive-this.startLineNumber}join(t){return new ee(Math.min(this.startLineNumber,t.startLineNumber),Math.max(this.endLineNumberExclusive,t.endLineNumberExclusive))}toString(){return`[${this.startLineNumber},${this.endLineNumberExclusive})`}}const Us=3;class Is{computeDiff(t,n,r){var s;const l=new Ws(t,n,{maxComputationTime:r.maxComputationTimeMs,shouldIgnoreTrimWhitespace:r.ignoreTrimWhitespace,shouldComputeCharChanges:!0,shouldMakePrettyDiff:!0,shouldPostProcessCharChanges:!0}).computeDiff(),o=[];let c=null;for(const u of l.changes){let f;u.originalEndLineNumber===0?f=new ee(u.originalStartLineNumber+1,u.originalStartLineNumber+1):f=new ee(u.originalStartLineNumber,u.originalEndLineNumber+1);let h;u.modifiedEndLineNumber===0?h=new ee(u.modifiedStartLineNumber+1,u.modifiedStartLineNumber+1):h=new ee(u.modifiedStartLineNumber,u.modifiedEndLineNumber+1);let d=new bt(f,h,(s=u.charChanges)===null||s===void 0?void 0:s.map(m=>new zn(new M(m.originalStartLineNumber,m.originalStartColumn,m.originalEndLineNumber,m.originalEndColumn),new M(m.modifiedStartLineNumber,m.modifiedStartColumn,m.modifiedEndLineNumber,m.modifiedEndColumn))));c&&(c.modifiedRange.endLineNumberExclusive===d.modifiedRange.startLineNumber||c.originalRange.endLineNumberExclusive===d.originalRange.startLineNumber)&&(d=new bt(c.originalRange.join(d.originalRange),c.modifiedRange.join(d.modifiedRange),c.innerChanges&&d.innerChanges?c.innerChanges.concat(d.innerChanges):void 0),o.pop()),o.push(d),c=d}return gt(()=>Hn(o,(u,f)=>f.originalRange.startLineNumber-u.originalRange.endLineNumberExclusive===f.modifiedRange.startLineNumber-u.modifiedRange.endLineNumberExclusive&&u.originalRange.endLineNumberExclusive<f.originalRange.startLineNumber&&u.modifiedRange.endLineNumberExclusive<f.modifiedRange.startLineNumber)),{quitEarly:l.quitEarly,changes:o}}}function On(e,t,n,r){return new re(e,t,n).ComputeDiff(r)}let Gn=class{constructor(t){const n=[],r=[];for(let s=0,a=t.length;s<a;s++)n[s]=_t(t[s],1),r[s]=pt(t[s],1);this.lines=t,this._startColumns=n,this._endColumns=r}getElements(){const t=[];for(let n=0,r=this.lines.length;n<r;n++)t[n]=this.lines[n].substring(this._startColumns[n]-1,this._endColumns[n]-1);return t}getStrictElement(t){return this.lines[t]}getStartLineNumber(t){return t+1}getEndLineNumber(t){return t+1}createCharSequence(t,n,r){const s=[],a=[],l=[];let o=0;for(let c=n;c<=r;c++){const u=this.lines[c],f=t?this._startColumns[c]:1,h=t?this._endColumns[c]:u.length+1;for(let d=f;d<h;d++)s[o]=u.charCodeAt(d-1),a[o]=c+1,l[o]=d,o++;!t&&c<r&&(s[o]=10,a[o]=c+1,l[o]=u.length+1,o++)}return new qs(s,a,l)}};class qs{constructor(t,n,r){this._charCodes=t,this._lineNumbers=n,this._columns=r}toString(){return"["+this._charCodes.map((t,n)=>(t===10?"\\n":String.fromCharCode(t))+`-(${this._lineNumbers[n]},${this._columns[n]})`).join(", ")+"]"}_assertIndex(t,n){if(t<0||t>=n.length)throw new Error("Illegal index")}getElements(){return this._charCodes}getStartLineNumber(t){return t>0&&t===this._lineNumbers.length?this.getEndLineNumber(t-1):(this._assertIndex(t,this._lineNumbers),this._lineNumbers[t])}getEndLineNumber(t){return t===-1?this.getStartLineNumber(t+1):(this._assertIndex(t,this._lineNumbers),this._charCodes[t]===10?this._lineNumbers[t]+1:this._lineNumbers[t])}getStartColumn(t){return t>0&&t===this._columns.length?this.getEndColumn(t-1):(this._assertIndex(t,this._columns),this._columns[t])}getEndColumn(t){return t===-1?this.getStartColumn(t+1):(this._assertIndex(t,this._columns),this._charCodes[t]===10?1:this._columns[t]+1)}}class we{constructor(t,n,r,s,a,l,o,c){this.originalStartLineNumber=t,this.originalStartColumn=n,this.originalEndLineNumber=r,this.originalEndColumn=s,this.modifiedStartLineNumber=a,this.modifiedStartColumn=l,this.modifiedEndLineNumber=o,this.modifiedEndColumn=c}static createFromDiffChange(t,n,r){const s=n.getStartLineNumber(t.originalStart),a=n.getStartColumn(t.originalStart),l=n.getEndLineNumber(t.originalStart+t.originalLength-1),o=n.getEndColumn(t.originalStart+t.originalLength-1),c=r.getStartLineNumber(t.modifiedStart),u=r.getStartColumn(t.modifiedStart),f=r.getEndLineNumber(t.modifiedStart+t.modifiedLength-1),h=r.getEndColumn(t.modifiedStart+t.modifiedLength-1);return new we(s,a,l,o,c,u,f,h)}}function Hs(e){if(e.length<=1)return e;const t=[e[0]];let n=t[0];for(let r=1,s=e.length;r<s;r++){const a=e[r],l=a.originalStart-(n.originalStart+n.originalLength),o=a.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(l,o)<Us?(n.originalLength=a.originalStart+a.originalLength-n.originalStart,n.modifiedLength=a.modifiedStart+a.modifiedLength-n.modifiedStart):(t.push(a),n=a)}return t}class ye{constructor(t,n,r,s,a){this.originalStartLineNumber=t,this.originalEndLineNumber=n,this.modifiedStartLineNumber=r,this.modifiedEndLineNumber=s,this.charChanges=a}static createFromDiffResult(t,n,r,s,a,l,o){let c,u,f,h,d;if(n.originalLength===0?(c=r.getStartLineNumber(n.originalStart)-1,u=0):(c=r.getStartLineNumber(n.originalStart),u=r.getEndLineNumber(n.originalStart+n.originalLength-1)),n.modifiedLength===0?(f=s.getStartLineNumber(n.modifiedStart)-1,h=0):(f=s.getStartLineNumber(n.modifiedStart),h=s.getEndLineNumber(n.modifiedStart+n.modifiedLength-1)),l&&n.originalLength>0&&n.originalLength<20&&n.modifiedLength>0&&n.modifiedLength<20&&a()){const m=r.createCharSequence(t,n.originalStart,n.originalStart+n.originalLength-1),b=s.createCharSequence(t,n.modifiedStart,n.modifiedStart+n.modifiedLength-1);if(m.getElements().length>0&&b.getElements().length>0){let C=On(m,b,a,!0).changes;o&&(C=Hs(C)),d=[];for(let N=0,g=C.length;N<g;N++)d.push(we.createFromDiffChange(C[N],m,b))}}return new ye(c,u,f,h,d)}}class Ws{constructor(t,n,r){this.shouldComputeCharChanges=r.shouldComputeCharChanges,this.shouldPostProcessCharChanges=r.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=r.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=r.shouldMakePrettyDiff,this.originalLines=t,this.modifiedLines=n,this.original=new Gn(t),this.modified=new Gn(n),this.continueLineDiff=jn(r.maxComputationTime),this.continueCharDiff=jn(r.maxComputationTime===0?0:Math.min(r.maxComputationTime,5e3))}computeDiff(){if(this.original.lines.length===1&&this.original.lines[0].length===0)return this.modified.lines.length===1&&this.modified.lines[0].length===0?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:void 0}]};if(this.modified.lines.length===1&&this.modified.lines[0].length===0)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:void 0}]};const t=On(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),n=t.changes,r=t.quitEarly;if(this.shouldIgnoreTrimWhitespace){const o=[];for(let c=0,u=n.length;c<u;c++)o.push(ye.createFromDiffResult(this.shouldIgnoreTrimWhitespace,n[c],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:r,changes:o}}const s=[];let a=0,l=0;for(let o=-1,c=n.length;o<c;o++){const u=o+1<c?n[o+1]:null,f=u?u.originalStart:this.originalLines.length,h=u?u.modifiedStart:this.modifiedLines.length;for(;a<f&&l<h;){const d=this.originalLines[a],m=this.modifiedLines[l];if(d!==m){{let b=_t(d,1),C=_t(m,1);for(;b>1&&C>1;){const N=d.charCodeAt(b-2),g=m.charCodeAt(C-2);if(N!==g)break;b--,C--}(b>1||C>1)&&this._pushTrimWhitespaceCharChange(s,a+1,1,b,l+1,1,C)}{let b=pt(d,1),C=pt(m,1);const N=d.length+1,g=m.length+1;for(;b<N&&C<g;){const S=d.charCodeAt(b-1),L=d.charCodeAt(C-1);if(S!==L)break;b++,C++}(b<N||C<g)&&this._pushTrimWhitespaceCharChange(s,a+1,b,N,l+1,C,g)}}a++,l++}u&&(s.push(ye.createFromDiffResult(this.shouldIgnoreTrimWhitespace,u,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),a+=u.originalLength,l+=u.modifiedLength)}return{quitEarly:r,changes:s}}_pushTrimWhitespaceCharChange(t,n,r,s,a,l,o){if(this._mergeTrimWhitespaceCharChange(t,n,r,s,a,l,o))return;let c;this.shouldComputeCharChanges&&(c=[new we(n,r,n,s,a,l,a,o)]),t.push(new ye(n,n,a,a,c))}_mergeTrimWhitespaceCharChange(t,n,r,s,a,l,o){const c=t.length;if(c===0)return!1;const u=t[c-1];return u.originalEndLineNumber===0||u.modifiedEndLineNumber===0?!1:u.originalEndLineNumber===n&&u.modifiedEndLineNumber===a?(this.shouldComputeCharChanges&&u.charChanges&&u.charChanges.push(new we(n,r,n,s,a,l,a,o)),!0):u.originalEndLineNumber+1===n&&u.modifiedEndLineNumber+1===a?(u.originalEndLineNumber=n,u.modifiedEndLineNumber=a,this.shouldComputeCharChanges&&u.charChanges&&u.charChanges.push(new we(n,r,n,s,a,l,a,o)),!0):!1}}function _t(e,t){const n=Pr(e);return n===-1?t:n+1}function pt(e,t){const n=Dr(e);return n===-1?t:n+2}function jn(e){if(e===0)return()=>!0;const t=Date.now();return()=>Date.now()-t<e}class K{constructor(t,n){this.seq1Range=t,this.seq2Range=n}reverse(){return new K(this.seq2Range,this.seq1Range)}toString(){return`${this.seq1Range} <-> ${this.seq2Range}`}}class z{constructor(t,n){this.start=t,this.endExclusive=n}get isEmpty(){return this.start===this.endExclusive}delta(t){return new z(this.start+t,this.endExclusive+t)}get length(){return this.endExclusive-this.start}toString(){return`[${this.start}, ${this.endExclusive})`}join(t){return new z(Math.min(this.start,t.start),Math.max(this.endExclusive,t.endExclusive))}}class xt{constructor(t,n){this.width=t,this.height=n,this.array=[],this.array=new Array(t*n)}get(t,n){return this.array[t+n*this.width]}set(t,n,r){this.array[t+n*this.width]=r}}class $s{compute(t,n,r){const s=new xt(t.length,n.length),a=new xt(t.length,n.length),l=new xt(t.length,n.length);for(let m=0;m<t.length;m++)for(let b=0;b<n.length;b++){const C=m===0?0:s.get(m-1,b),N=b===0?0:s.get(m,b-1);let g;t.getElement(m)===n.getElement(b)?(m===0||b===0?g=0:g=s.get(m-1,b-1),m>0&&b>0&&a.get(m-1,b-1)===3&&(g+=l.get(m-1,b-1)),g+=r?r(m,b):1):g=-1;const S=Math.max(C,N,g);if(S===g){const L=m>0&&b>0?l.get(m-1,b-1):0;l.set(m,b,L+1),a.set(m,b,3)}else S===C?(l.set(m,b,0),a.set(m,b,1)):S===N&&(l.set(m,b,0),a.set(m,b,2));s.set(m,b,S)}const o=[];let c=t.length,u=n.length;function f(m,b){(m+1!==c||b+1!==u)&&o.push(new K(new z(m+1,c),new z(b+1,u))),c=m,u=b}let h=t.length-1,d=n.length-1;for(;h>=0&&d>=0;)a.get(h,d)===3?(f(h,d),h--,d--):a.get(h,d)===1?h--:d--;return f(-1,-1),o.reverse(),o}}function Xn(e,t,n){let r=n;return r=Os(e,t,r),r=Gs(e,t,r),r}function zs(e,t,n){const r=[];for(const s of n){const a=r[r.length-1];if(!a){r.push(s);continue}s.seq1Range.start-a.seq1Range.endExclusive<=2||s.seq2Range.start-a.seq2Range.endExclusive<=2?r[r.length-1]=new K(a.seq1Range.join(s.seq1Range),a.seq2Range.join(s.seq2Range)):r.push(s)}return r}function Os(e,t,n){const r=[];n.length>0&&r.push(n[0]);for(let s=1;s<n.length;s++){const a=r[r.length-1],l=n[s];if(l.seq1Range.isEmpty){let o=!0;const c=l.seq1Range.start-a.seq1Range.endExclusive;for(let u=1;u<=c;u++)if(t.getElement(l.seq2Range.start-u)!==t.getElement(l.seq2Range.endExclusive-u)){o=!1;break}if(o){r[r.length-1]=new K(a.seq1Range,new z(a.seq2Range.start,l.seq2Range.endExclusive-c));continue}}r.push(l)}return r}function Gs(e,t,n){if(!e.getBoundaryScore||!t.getBoundaryScore)return n;for(let r=0;r<n.length;r++){const s=n[r];if(s.seq1Range.isEmpty){const a=r>0?n[r-1].seq2Range.endExclusive:-1,l=r+1<n.length?n[r+1].seq2Range.start:t.length;n[r]=Qn(s,e,t,l,a)}else if(s.seq2Range.isEmpty){const a=r>0?n[r-1].seq1Range.endExclusive:-1,l=r+1<n.length?n[r+1].seq1Range.start:e.length;n[r]=Qn(s.reverse(),t,e,l,a).reverse()}}return n}function Qn(e,t,n,r,s){let l=1;for(;e.seq2Range.start-l>s&&n.getElement(e.seq2Range.start-l)===n.getElement(e.seq2Range.endExclusive-l)&&l<20;)l++;l--;let o=0;for(;e.seq2Range.start+o<r&&n.getElement(e.seq2Range.start+o)===n.getElement(e.seq2Range.endExclusive+o)&&o<20;)o++;if(l===0&&o===0)return e;let c=0,u=-1;for(let f=-l;f<=o;f++){const h=e.seq2Range.start+f,d=e.seq2Range.endExclusive+f,m=e.seq1Range.start+f,b=t.getBoundaryScore(m)+n.getBoundaryScore(h)+n.getBoundaryScore(d);b>u&&(u=b,c=f)}return c!==0?new K(e.seq1Range.delta(c),e.seq2Range.delta(c)):e}class js{compute(t,n){if(t.length===0)return[new K(new z(0,0),new z(0,n.length))];if(n.length===0)return[new K(new z(0,t.length),new z(0,0))];function r(d,m){for(;d<t.length&&m<n.length&&t.getElement(d)===n.getElement(m);)d++,m++;return d}let s=0;const a=new Xs;a.set(0,r(0,0));const l=new Qs;l.set(0,a.get(0)===0?null:new Yn(null,0,0,a.get(0)));let o=0;e:for(;;)for(s++,o=-s;o<=s;o+=2){const d=o===s?-1:a.get(o+1),m=o===-s?-1:a.get(o-1)+1,b=Math.min(Math.max(d,m),t.length),C=b-o,N=r(b,C);a.set(o,N);const g=b===d?l.get(o+1):l.get(o-1);if(l.set(o,N!==b?new Yn(g,b,C,N-b):g),a.get(o)===t.length&&a.get(o)-o===n.length)break e}let c=l.get(o);const u=[];let f=t.length,h=n.length;for(;;){const d=c?c.x+c.length:0,m=c?c.y+c.length:0;if((d!==f||m!==h)&&u.push(new K(new z(d,f),new z(m,h))),!c)break;f=c.x,h=c.y,c=c.prev}return u.reverse(),u}}class Yn{constructor(t,n,r,s){this.prev=t,this.x=n,this.y=r,this.length=s}}class Xs{constructor(){this.positiveArr=new Int32Array(10),this.negativeArr=new Int32Array(10)}get(t){return t<0?(t=-t-1,this.negativeArr[t]):this.positiveArr[t]}set(t,n){if(t<0){if(t=-t-1,t>=this.negativeArr.length){const r=this.negativeArr;this.negativeArr=new Int32Array(r.length*2),this.negativeArr.set(r)}this.negativeArr[t]=n}else{if(t>=this.positiveArr.length){const r=this.positiveArr;this.positiveArr=new Int32Array(r.length*2),this.positiveArr.set(r)}this.positiveArr[t]=n}}}class Qs{constructor(){this.positiveArr=[],this.negativeArr=[]}get(t){return t<0?(t=-t-1,this.negativeArr[t]):this.positiveArr[t]}set(t,n){t<0?(t=-t-1,this.negativeArr[t]=n):this.positiveArr[t]=n}}class Ys{constructor(){this.dynamicProgrammingDiffing=new $s,this.myersDiffingAlgorithm=new js}computeDiff(t,n,r){const s=new Map;function a(N){let g=s.get(N);return g===void 0&&(g=s.size,s.set(N,g)),g}const l=t.map(N=>a(N.trim())),o=n.map(N=>a(N.trim())),c=new Zn(l,t),u=new Zn(o,n);let f=c.length+u.length<1500?this.dynamicProgrammingDiffing.compute(c,u,(N,g)=>t[N]===n[g]?n[g].length===0?.1:1+Math.log(1+n[g].length):.99):this.myersDiffingAlgorithm.compute(c,u);f=Xn(c,u,f);const h=[],d=N=>{for(let g=0;g<N;g++){const S=m+g,L=b+g;if(t[S]!==n[L]){const P=this.refineDiff(t,n,new K(new z(S,S+1),new z(L,L+1)));for(const k of P)h.push(k)}}};let m=0,b=0;for(const N of f){gt(()=>N.seq1Range.start-m===N.seq2Range.start-b);const g=N.seq1Range.start-m;d(g),m=N.seq1Range.endExclusive,b=N.seq2Range.endExclusive;const S=this.refineDiff(t,n,N);for(const L of S)h.push(L)}return d(t.length-m),{quitEarly:!1,changes:Zs(h)}}refineDiff(t,n,r){const s=new Kn(t,r.seq1Range),a=new Kn(n,r.seq2Range),l=s.length+a.length<500?this.dynamicProgrammingDiffing.compute(s,a):this.myersDiffingAlgorithm.compute(s,a);let o=Xn(s,a,l);return o=zs(s,a,o),o.map(u=>new zn(s.translateRange(u.seq1Range).delta(r.seq1Range.start),a.translateRange(u.seq2Range).delta(r.seq2Range.start)))}}function Zs(e){const t=[];for(const n of Js(e,(r,s)=>s.originalRange.startLineNumber-(r.originalRange.endLineNumber-(r.originalRange.endColumn>1?0:1))<=1||s.modifiedRange.startLineNumber-(r.modifiedRange.endLineNumber-(r.modifiedRange.endColumn>1?0:1))<=1)){const r=n[0],s=n[n.length-1];t.push(new bt(new ee(r.originalRange.startLineNumber,s.originalRange.endLineNumber+(s.originalRange.endColumn>1||s.modifiedRange.endColumn>1?1:0)),new ee(r.modifiedRange.startLineNumber,s.modifiedRange.endLineNumber+(s.originalRange.endColumn>1||s.modifiedRange.endColumn>1?1:0)),n))}return gt(()=>Hn(t,(n,r)=>r.originalRange.startLineNumber-n.originalRange.endLineNumberExclusive===r.modifiedRange.startLineNumber-n.modifiedRange.endLineNumberExclusive&&n.originalRange.endLineNumberExclusive<r.originalRange.startLineNumber&&n.modifiedRange.endLineNumberExclusive<r.modifiedRange.startLineNumber)),t}function*Js(e,t){let n,r;for(const s of e)r!==void 0&&t(r,s)?n.push(s):(n&&(yield n),n=[s]),r=s;n&&(yield n)}class Zn{constructor(t,n){this.trimmedHash=t,this.lines=n}getElement(t){return this.trimmedHash[t]}get length(){return this.trimmedHash.length}getBoundaryScore(t){const n=t===0?0:Jn(this.lines[t-1]),r=t===this.lines.length?0:Jn(this.lines[t]);return 1e3-(n+r)}}function Jn(e){let t=0;for(;t<e.length&&(e.charCodeAt(t)===32||e.charCodeAt(t)===9);)t++;return t}class Kn{constructor(t,n){this.lines=t,this.lineRange=n;let r=0;this.firstCharOnLineOffsets=new Int32Array(n.length);for(let a=n.start;a<n.endExclusive;a++){const l=t[a];r+=l.length,this.firstCharOnLineOffsets[a-n.start]=r+1,r++}this.elements=new Int32Array(r);let s=0;for(let a=n.start;a<n.endExclusive;a++){const l=t[a];for(let o=0;o<l.length;o++)this.elements[s+o]=l.charCodeAt(o);s+=l.length,a<t.length-1&&(this.elements[s]=10,s+=1)}}getElement(t){return this.elements[t]}get length(){return this.elements.length}getBoundaryScore(t){const n=tr(t>0?this.elements[t-1]:-1),r=tr(t<this.elements.length?this.elements[t]:-1);if(n===6&&r===7)return 0;let s=0;return n!==r&&(s+=10,r===1&&(s+=1)),s+=er(n),s+=er(r),s}translateOffset(t){let n=0,r=this.firstCharOnLineOffsets.length;for(;n<r;){const a=Math.floor((n+r)/2);this.firstCharOnLineOffsets[a]>t?r=a:n=a+1}const s=n===0?0:this.firstCharOnLineOffsets[n-1];return new H(n+1,t-s+1)}translateRange(t){return M.fromPositions(this.translateOffset(t.start),this.translateOffset(t.endExclusive))}}const Ks={0:0,1:0,2:0,3:10,4:2,5:3,6:10,7:10};function er(e){return Ks[e]}function tr(e){return e===10?7:e===13?6:ei(e)?5:e>=97&&e<=122?0:e>=65&&e<=90?1:e>=48&&e<=57?2:e===-1?3:4}function ei(e){return e===32||e===9}const nr={smart:new Is,experimental:new Ys};var ce=function(e,t,n,r){function s(a){return a instanceof n?a:new n(function(l){l(a)})}return new(n||(n=Promise))(function(a,l){function o(f){try{u(r.next(f))}catch(h){l(h)}}function c(f){try{u(r.throw(f))}catch(h){l(h)}}function u(f){f.done?a(f.value):s(f.value).then(o,c)}u((r=r.apply(e,t||[])).next())})};class ti extends fs{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(t){return this._lines[t-1]}getWordAtPosition(t,n){const r=rt(t.column,gs(n),this._lines[t.lineNumber-1],0);return r?new M(t.lineNumber,r.startColumn,t.lineNumber,r.endColumn):null}words(t){const n=this._lines,r=this._wordenize.bind(this);let s=0,a="",l=0,o=[];return{*[Symbol.iterator](){for(;;)if(l<o.length){const c=a.substring(o[l].start,o[l].end);l+=1,yield c}else if(s<n.length)a=n[s],o=r(a,t),l=0,s+=1;else break}}}getLineWords(t,n){const r=this._lines[t-1],s=this._wordenize(r,n),a=[];for(const l of s)a.push({word:r.substring(l.start,l.end),startColumn:l.start+1,endColumn:l.end+1});return a}_wordenize(t,n){const r=[];let s;for(n.lastIndex=0;(s=n.exec(t))&&s[0].length!==0;)r.push({start:s.index,end:s.index+s[0].length});return r}getValueInRange(t){if(t=this._validateRange(t),t.startLineNumber===t.endLineNumber)return this._lines[t.startLineNumber-1].substring(t.startColumn-1,t.endColumn-1);const n=this._eol,r=t.startLineNumber-1,s=t.endLineNumber-1,a=[];a.push(this._lines[r].substring(t.startColumn-1));for(let l=r+1;l<s;l++)a.push(this._lines[l]);return a.push(this._lines[s].substring(0,t.endColumn-1)),a.join(n)}offsetAt(t){return t=this._validatePosition(t),this._ensureLineStarts(),this._lineStarts.getPrefixSum(t.lineNumber-2)+(t.column-1)}positionAt(t){t=Math.floor(t),t=Math.max(0,t),this._ensureLineStarts();const n=this._lineStarts.getIndexOf(t),r=this._lines[n.index].length;return{lineNumber:1+n.index,column:1+Math.min(n.remainder,r)}}_validateRange(t){const n=this._validatePosition({lineNumber:t.startLineNumber,column:t.startColumn}),r=this._validatePosition({lineNumber:t.endLineNumber,column:t.endColumn});return n.lineNumber!==t.startLineNumber||n.column!==t.startColumn||r.lineNumber!==t.endLineNumber||r.column!==t.endColumn?{startLineNumber:n.lineNumber,startColumn:n.column,endLineNumber:r.lineNumber,endColumn:r.column}:t}_validatePosition(t){if(!H.isIPosition(t))throw new Error("bad position");let{lineNumber:n,column:r}=t,s=!1;if(n<1)n=1,r=1,s=!0;else if(n>this._lines.length)n=this._lines.length,r=this._lines[n-1].length+1,s=!0;else{const a=this._lines[n-1].length+1;r<1?(r=1,s=!0):r>a&&(r=a,s=!0)}return s?{lineNumber:n,column:r}:t}}class he{constructor(t,n){this._host=t,this._models=Object.create(null),this._foreignModuleFactory=n,this._foreignModule=null}dispose(){this._models=Object.create(null)}_getModel(t){return this._models[t]}_getModels(){const t=[];return Object.keys(this._models).forEach(n=>t.push(this._models[n])),t}acceptNewModel(t){this._models[t.url]=new ti(ue.parse(t.url),t.lines,t.EOL,t.versionId)}acceptModelChanged(t,n){if(!this._models[t])return;this._models[t].onEvents(n)}acceptRemovedModel(t){this._models[t]&&delete this._models[t]}computeUnicodeHighlights(t,n,r){return ce(this,void 0,void 0,function*(){const s=this._getModel(t);return s?Bs.computeUnicodeHighlights(s,n,r):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}})}computeDiff(t,n,r,s){return ce(this,void 0,void 0,function*(){const a=this._getModel(t),l=this._getModel(n);return!a||!l?null:he.computeDiff(a,l,r,s)})}static computeDiff(t,n,r,s){const a=s==="experimental"?nr.experimental:nr.smart,l=t.getLinesContent(),o=n.getLinesContent(),c=a.computeDiff(l,o,r);return{identical:c.changes.length>0?!1:this._modelsAreIdentical(t,n),quitEarly:c.quitEarly,changes:c.changes.map(f=>{var h;return[f.originalRange.startLineNumber,f.originalRange.endLineNumberExclusive,f.modifiedRange.startLineNumber,f.modifiedRange.endLineNumberExclusive,(h=f.innerChanges)===null||h===void 0?void 0:h.map(d=>[d.originalRange.startLineNumber,d.originalRange.startColumn,d.originalRange.endLineNumber,d.originalRange.endColumn,d.modifiedRange.startLineNumber,d.modifiedRange.startColumn,d.modifiedRange.endLineNumber,d.modifiedRange.endColumn])]})}}static _modelsAreIdentical(t,n){const r=t.getLineCount(),s=n.getLineCount();if(r!==s)return!1;for(let a=1;a<=r;a++){const l=t.getLineContent(a),o=n.getLineContent(a);if(l!==o)return!1}return!0}computeMoreMinimalEdits(t,n){return ce(this,void 0,void 0,function*(){const r=this._getModel(t);if(!r)return n;const s=[];let a;n=n.slice(0).sort((l,o)=>{if(l.range&&o.range)return M.compareRangesUsingStarts(l.range,o.range);const c=l.range?0:1,u=o.range?0:1;return c-u});for(let{range:l,text:o,eol:c}of n){if(typeof c=="number"&&(a=c),M.isEmpty(l)&&!o)continue;const u=r.getValueInRange(l);if(o=o.replace(/\r\n|\n|\r/g,r.eol),u===o)continue;if(Math.max(o.length,u.length)>he._diffLimit){s.push({range:l,text:o});continue}const f=jr(u,o,!1),h=r.offsetAt(M.lift(l).getStartPosition());for(const d of f){const m=r.positionAt(h+d.originalStart),b=r.positionAt(h+d.originalStart+d.originalLength),C={text:o.substr(d.modifiedStart,d.modifiedLength),range:{startLineNumber:m.lineNumber,startColumn:m.column,endLineNumber:b.lineNumber,endColumn:b.column}};r.getValueInRange(C.range)!==C.text&&s.push(C)}}return typeof a=="number"&&s.push({eol:a,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),s})}computeLinks(t){return ce(this,void 0,void 0,function*(){const n=this._getModel(t);return n?Ls(n):null})}textualSuggest(t,n,r,s){return ce(this,void 0,void 0,function*(){const a=new Ee(!0),l=new RegExp(r,s),o=new Set;e:for(const c of t){const u=this._getModel(c);if(u){for(const f of u.words(l))if(!(f===n||!isNaN(Number(f)))&&(o.add(f),o.size>he._suggestionsLimit))break e}}return{words:Array.from(o),duration:a.elapsed()}})}computeWordRanges(t,n,r,s){return ce(this,void 0,void 0,function*(){const a=this._getModel(t);if(!a)return Object.create(null);const l=new RegExp(r,s),o=Object.create(null);for(let c=n.startLineNumber;c<n.endLineNumber;c++){const u=a.getLineWords(c,l);for(const f of u){if(!isNaN(Number(f.word)))continue;let h=o[f.word];h||(h=[],o[f.word]=h),h.push({startLineNumber:c,startColumn:f.startColumn,endLineNumber:c,endColumn:f.endColumn})}}return o})}navigateValueSet(t,n,r,s,a){return ce(this,void 0,void 0,function*(){const l=this._getModel(t);if(!l)return null;const o=new RegExp(s,a);n.startColumn===n.endColumn&&(n={startLineNumber:n.startLineNumber,startColumn:n.startColumn,endLineNumber:n.endLineNumber,endColumn:n.endColumn+1});const c=l.getValueInRange(n),u=l.getWordAtPosition({lineNumber:n.startLineNumber,column:n.startColumn},o);if(!u)return null;const f=l.getValueInRange(u);return at.INSTANCE.navigateValueSet(n,c,u,f,r)})}loadForeignModule(t,n,r){const l={host:yr(r,(o,c)=>this._host.fhr(o,c)),getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(l,n),Promise.resolve(Je(this._foreignModule))):Promise.reject(new Error("Unexpected usage"))}fmr(t,n){if(!this._foreignModule||typeof this._foreignModule[t]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+t));try{return Promise.resolve(this._foreignModule[t].apply(this._foreignModule,n))}catch(r){return Promise.reject(r)}}}he._diffLimit=1e5,he._suggestionsLimit=1e4,typeof importScripts=="function"&&(U.monaco=Ms());let wt=!1;function ni(e){if(wt)return;wt=!0;const t=new Or(n=>{self.postMessage(n)},n=>new he(n,e));self.onmessage=n=>{t.onmessage(n.data)}}self.onmessage=e=>{wt||ni(null)}})();
