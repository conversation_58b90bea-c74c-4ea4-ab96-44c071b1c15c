import{c as N,k as $,b,n as A,l as ee,X as te,q as g,i as v,v as U,w as n,J as L,B as w,f as S,h as r,j as y,t as M,o as f,A as le,G as ae,H as oe,y as k,F as W,s as z,E as c}from"./.pnpm-Kv7TmmH8.js";import{g as ne}from"./index-wptcDEeL.js";import{s as P}from"./index-BuqCFB-b.js";const de={key:1,class:"dialog-footer"},re=N({name:"undefined"}),ue=N({...re,props:$({cancelText:{default:"取消"},confrimText:{default:"保存"},width:{default:"60%"},showSave:{type:Boolean,default:!0},showFooter:{type:Boolean,default:!0}},{modelValue:{},modelModifiers:{}}),emits:$(["update:modelValue","cancel","confrim"],["update:modelValue"]),setup(I,{emit:x}){const u=I,E=b(!1),m=x,i=A(I,"modelValue"),D=ee(),o=te();function _(){m("confrim",E)}function V(){m("cancel")}return(d,q)=>{const Q=v("el-button"),C=v("el-dialog");return f(),g(C,{modelValue:i.value,"onUpdate:modelValue":q[0]||(q[0]=B=>i.value=B),width:u.width,"append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1,onOpen:U(D).open},{footer:n(()=>[d.showFooter?L(d.$slots,"footer",{key:0}):w("",!0),d.showFooter&&!U(o).footer?(f(),S("div",de,[r(Q,{onClick:V},{default:n(()=>[y(M(u.cancelText),1)]),_:1}),u.showSave?(f(),g(Q,{key:0,type:"success",onClick:_,loading:U(E)},{default:n(()=>[y(M(u.confrimText),1)]),_:1},8,["loading"])):w("",!0)])):w("",!0)]),default:n(()=>[i.value?L(d.$slots,"default",{key:0}):w("",!0)]),_:3},8,["modelValue","width","onOpen"])}}}),se={key:0},ie={flex:"~ items-center",mb:"10px"},ce={"w-350px":""},pe={ml:"30px"},fe={ml:"30px",style:{display:"flex","align-items":"center"}},ve=k("span",{style:{color:"var(--el-color-danger)"}},"*",-1),me={style:{display:"flex","align-items":"center"}},he=k("span",{style:{color:"var(--el-color-danger)"}},"*",-1),ye=N({name:"addWorkOrder"}),ke=N({...ye,props:$({detailData:{},isEdit:{type:Boolean}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:$(["refresh"],["update:modelValue"]),setup(I,{emit:x}){const u=I,E=x,m=A(I,"modelValue"),i=b(!0),D=b(!0),o=b({id:void 0,scheduleId:void 0,productionScheduleSn:"",ScheduleDate:void 0,workOrderNo:"",products:[]}),_=b([]),V=b([]),d=b([]);function q(){P.pms.production.schedule.page({page:1,size:9999999,status:1}).then(e=>{D.value=!1,_.value=e.list,_.value.length>0&&(_.value.forEach(t=>{t.products.length>0&&t.products.forEach(l=>{l.colorLabel=ne("color",l.color)})}),u.detailData&&Q(u.detailData.scheduleId))})}q();function Q(e){d.value=[],o.value.productionScheduleSn="";const t=_.value.find(l=>l.id===e);t&&(o.value.productionScheduleSn=t.sn,t.products.length>0&&(t.products.forEach(l=>{l.disabled=!1}),V.value=t.products,V.value.forEach(l=>{l.label=`${l.sku} / ${l.name} / ${l.colorLabel}`})))}const C=()=>{const e={};return d.value.forEach(t=>{e[t.productId]?e[t.productId]+=t.planQuantity:e[t.productId]=t.planQuantity}),e};function B(e){if(!o.value.scheduleId)return c.error("请选择生产单");if(!o.value.workOrderNo)return c.error("请输入工单号");if(d.value.length===0)return c.error("请选择产品");for(let l=0;l<d.value.length;l++){const s=d.value[l];if(!o.value.scheduleId)return s.planQuantity=0,c.error("请选择生产单");if(!s.productId)return s.planQuantity=0,c.error("请选择产品");if(!s.quantity)return c.error("请选择输入产品数量");if(C()[s.productId]>s.quantity)return s.planQuantity=0,c.error("计划数量不能大于库存数量")}o.value.products=d.value.map(l=>({productId:l.productId,PieceProductId:l.productId,quantity:l.planQuantity,Piece:l.planQuantity,workOrderNo:o.value.workOrderNo,scheduleDate:o.value.scheduleDate})),e.value=!0,(u.isEdit?P.pms.workOrder.updateWorkOrder(o.value):P.pms.workOrder.addWorkOrder(o.value)).then(l=>{m.value=!1,c.success(u.isEdit?"更新成功":"添加成功"),E("refresh")}).catch(l=>{c.error(l.message)}).finally(()=>{e.value=!1})}function j(){m.value=!1}function G(){d.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),productId:null,quantity:null,planQuantity:null})}const T=e=>V.value.find(t=>t.productId===e);function F(e){const t=T(e.productId);t&&(t.disabled=!0,e.quantity=t==null?void 0:t.quantity,e.inboundQuantity=t==null?void 0:t.inboundQuantity,e.workOrderQty=t==null?void 0:t.workOrderQty)}function H(e){const t=e.index;d.value=d.value.filter(s=>s.index!==t);const l=T(e.productId);l&&(l.disabled=!1)}function K(e){if(!o.value.scheduleId)return e.planQuantity=0,c.error("请选择生产单");if(!e.productId)return e.planQuantity=0,c.error("请选择产品");if(C()[e.productId]>e.quantity)return e.planQuantity=0,c.error("计划数量不能大于库存数量");const l=e.quantity-e.workOrderQty;if(e.planQuantity>l)return e.planQuantity=0,c.error("计划数量不能大于剩余数量")}return le(()=>{if(u.detailData){i.value=u.isEdit===!0,o.value=u.detailData;const e=JSON.parse(u.detailData.productIds),t=JSON.parse(u.detailData.productQtyJson);e.length>0&&e.forEach((l,s)=>{const O={productId:l,planQuantity:t[s]};F(O),d.value.push(O)})}}),(e,t)=>{const l=v("el-option"),s=v("el-select"),O=v("el-input"),X=v("el-date-picker"),h=v("el-table-column"),R=v("el-input-number"),J=v("el-button"),Y=v("el-table"),Z=oe("loading");return f(),g(ue,{modelValue:m.value,"onUpdate:modelValue":t[3]||(t[3]=a=>m.value=a),title:"工单","show-footer":i.value||e.isEdit,onCancel:j,onConfrim:B},{default:n(()=>[m.value?ae((f(),S("div",se,[k("div",ie,[k("div",ce,[r(s,{modelValue:o.value.scheduleId,"onUpdate:modelValue":t[0]||(t[0]=a=>o.value.scheduleId=a),placeholder:"请选择生产单",clearable:"",filterable:"",disabled:!i.value||e.isEdit,onChange:Q},{default:n(()=>[(f(!0),S(W,null,z(_.value,a=>(f(),g(l,{key:a.id,label:a.sn,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),k("div",pe,[r(O,{modelValue:o.value.workOrderNo,"onUpdate:modelValue":t[1]||(t[1]=a=>o.value.workOrderNo=a),placeholder:"请输入工单号","w-300px":"",disabled:!i.value||e.isEdit},null,8,["modelValue","disabled"])]),k("div",fe,[r(X,{modelValue:o.value.scheduleDate,"onUpdate:modelValue":t[2]||(t[2]=a=>o.value.scheduleDate=a),type:"date",disabled:!i.value||e.isEdit,placeholder:"请选择日期",style:{width:"300px"}},null,8,["modelValue","disabled"])])]),r(Y,{data:d.value},{default:n(()=>[r(h,{prop:"sku",label:"*产品",align:"center"},{header:n(()=>[ve,y(" SKU ")]),default:n(a=>[k("div",me,[r(s,{modelValue:a.row.productId,"onUpdate:modelValue":p=>a.row.productId=p,prop:"productId",filterable:"","reserve-keyword":"",placeholder:"请选择产品",disabled:!i.value||e.isEdit,onChange:p=>F(a.row)},{default:n(()=>[(f(!0),S(W,null,z(V.value,p=>(f(),g(l,{key:p.productId,label:p.label,value:p.productId,disabled:p.disabled},null,8,["label","value","disabled"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),_:1}),r(h,{prop:"quantity",label:"*数量",align:"center"},{header:n(()=>[he,y(" 数量 ")]),default:n(a=>[r(R,{modelValue:a.row.planQuantity,"onUpdate:modelValue":p=>a.row.planQuantity=p,"w-full":"",min:0,max:9999999,placeholder:"数量",disabled:!i.value||e.isEdit,onBlur:p=>K(a.row)},null,8,["modelValue","onUpdate:modelValue","disabled","onBlur"])]),_:1}),r(h,{prop:"quantity",label:"排产数量",align:"center"}),r(h,{prop:"workOrderQty",label:"已排产数量",align:"center"}),r(h,{label:"剩余数量",align:"center"},{default:n(a=>[y(M(a.row.quantity-a.row.workOrderQty||0),1)]),_:1}),r(h,{prop:"inboundQuantity",label:"已入库数量",align:"center"}),r(h,{label:"未结单数量",align:"center"},{default:n(a=>[y(M(a.row.quantity-a.row.inboundQuantity||0),1)]),_:1}),i.value&&!e.isEdit?(f(),g(h,{key:0,label:"操作",width:"100",align:"center"},{default:n(a=>[r(J,{type:"danger",size:"small",onClick:p=>H(a.row)},{default:n(()=>[y(" 删除 ")]),_:2},1032,["onClick"])]),_:1})):w("",!0)]),_:1},8,["data"]),i.value&&!e.isEdit?(f(),g(J,{key:0,style:{width:"100%"},class:"btn-data-add",onClick:G},{default:n(()=>[y(" + 选择产品 ")]),_:1})):w("",!0)])),[[Z,D.value]]):w("",!0)]),_:1},8,["modelValue","show-footer"])}}});export{ke as _};
