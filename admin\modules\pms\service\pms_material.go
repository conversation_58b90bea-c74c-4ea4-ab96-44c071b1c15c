package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"io"
	"math"
	"net/url"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gres"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/yc"
	"github.com/r3labs/diff/v3"
	"github.com/xuri/excelize/v2"
)

type PmsMaterialService struct {
	*yc.Service
}

// SetMaterialLevel 设置物料等级
func (s *PmsMaterialService) SetMaterialLevel(ctx context.Context) (err error) {
	// 查询所有的物料信息
	materialList := make([]*model.PmsMaterial, 0)
	err = yc.DBM(s.Model).Ctx(ctx).Scan(&materialList)
	if err != nil {
		return err
	}
	// 查询物料价格信息 取最高价格
	materialPriceList := make([]*model.PmsMaterialPrice, 0)
	err = yc.DBM(model.NewPmsMaterialPrice()).As("p").Ctx(ctx).
		Fields("material_id, MAX(price) AS price").
		Group("material_id").
		Scan(&materialPriceList)

	// 查询物料BOM信息 - 当material_id重复时，查询quantity最大的那条数据
	bomMaterialList := make([]*model.PmsBomMaterial, 0)
	err = yc.DBM(model.NewPmsBomMaterial()).Ctx(ctx).
		Fields("material_id, MAX(quantity) as quantity").
		Group("material_id").
		Scan(&bomMaterialList)
	if err != nil {
		return err
	}

	// TODO: 处理物料等级设置逻辑
	// 这里可以根据 materialList, materialPriceList, bomMaterialList 来设置物料等级
	materialPriceMap := make(map[int64]float64)
	bomMaterialMap := make(map[int64]float64)
	for _, item := range materialPriceList {
		materialPriceMap[item.MaterialId] = item.Price
	}
	for _, item := range bomMaterialList {
		bomMaterialMap[item.MaterialId] = item.Quantity
	}

	type MaterialLevel struct {
		ID    int64  `json:"id"`
		Level string `json:"level"` // 物料等级
	}
	materialInfo := make([]*MaterialLevel, 0)
	for _, material := range materialList {
		bomQuantity := 0.0
		materialPrice := 0.0
		level := ""
		if bomMaterial, ok := bomMaterialMap[material.ID]; ok {
			bomQuantity = bomMaterial
		}
		if price, ok := materialPriceMap[material.ID]; ok {
			materialPrice = price
		}

		// 如果没有匹配到数据
		if bomQuantity <= 0.0 || materialPrice <= 0.0 {
			continue
		}

		if bomQuantity < 1.0 {
			if materialPrice < 1.0 {
				level = "F"
			} else if materialPrice >= 1 && materialPrice < 5 {
				level = "E"
			} else if materialPrice >= 5 {
				level = "D"
			}
		} else if bomQuantity == 1 {
			if materialPrice < 0.5 {
				level = "G"
			} else if materialPrice >= 0.5 && materialPrice < 1 {
				level = "C"
			} else if materialPrice >= 1 {
				level = "A"
			}
		} else if bomQuantity > 1 {
			if materialPrice < 0.1 {
				level = "H"
			} else if materialPrice >= 0.1 {
				level = "B"
			}
		}

		if level != "" {
			materialInfo = append(materialInfo, &MaterialLevel{
				ID:    material.ID,
				Level: level,
			})

		}
	}

	// 方案一：使用事务进行批量更新
	//err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
	//	for _, info := range materialInfo {
	//		_, err := yc.DBM(s.Model).Ctx(ctx).
	//			Data(g.Map{"level": info.Level}).
	//			Where("id", info.ID).
	//			Update()
	//		if err != nil {
	//			return err
	//		}
	//	}
	//	return nil
	//})

	// 方案二：使用原生SQL批量更新（更高效，但需要构建复杂SQL）
	// if len(materialInfo) > 0 {
	// 	var caseWhen strings.Builder
	// 	var ids []interface{}
	// 	caseWhen.WriteString("UPDATE ")
	// 	caseWhen.WriteString(s.Model.TableName())
	// 	caseWhen.WriteString(" SET level = CASE id ")
	//
	// 	for _, info := range materialInfo {
	// 		caseWhen.WriteString(fmt.Sprintf("WHEN ? THEN ? "))
	// 		ids = append(ids, info.ID, info.Level)
	// 	}
	// 	caseWhen.WriteString("END WHERE id IN (")
	// 	for i := range materialInfo {
	// 		if i > 0 {
	// 			caseWhen.WriteString(",")
	// 		}
	// 		caseWhen.WriteString("?")
	// 		ids = append(ids, materialInfo[i].ID)
	// 	}
	// 	caseWhen.WriteString(")")
	//
	// 	_, err = yc.DB(s.Model.GroupName()).Ctx(ctx).Exec(caseWhen.String(), ids...)
	// }

	// 方案三：批量更新使用 BatchSize 分批处理（推荐用于大量数据）
	batchSize := 100 // 每批处理100条
	for i := 0; i < len(materialInfo); i += batchSize {
		end := i + batchSize
		if end > len(materialInfo) {
			end = len(materialInfo)
		}
		batch := materialInfo[i:end]

		err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			for _, info := range batch {
				_, err := yc.DBM(s.Model).Ctx(ctx).
					Data(g.Map{"level": info.Level}).
					Where("id", info.ID).
					Update()
				if err != nil {
					return err
				}
			}
			return nil
		})
		if err != nil {
			return err
		}
	}
	return
}

// ImportMaterialAddress 导入物料地址
func (s *PmsMaterialService) ImportMaterialAddress(ctx context.Context, AddressData []*model.ImportMaterialAddressData) (err error) {
	codes := make([]string, 0)
	// 构造物料编码地址map
	codeMap := make(map[string]string)
	addressMap := make(map[string]struct{})
	// 接口请求传入的地址数据
	addressParam := make([]string, 0)
	for _, item := range AddressData {
		codes = append(codes, item.Code)
		codeMap[item.Code] = item.Address
		tmpAddressArr := strings.Split(item.Address, ",")
		for _, row := range tmpAddressArr {
			if _, ok := addressMap[row]; !ok {
				addressParam = append(addressParam, row)
				addressMap[row] = struct{}{}
			}
		}
	}

	addressMap = make(map[string]struct{})
	addrLists, err := s.GetMaterialAddress(ctx)
	if err != nil {
		return err
	}

	for _, item := range addrLists {
		addressMap[item.Address] = struct{}{}
	}

	// 查询没有录入的地址数据，即本次需要在物料地址表增加的数据
	inputAddress := make([]*model.PmsMaterialAddress, 0)
	for _, item := range addressParam {
		if _, ok := addressMap[item]; !ok {
			inputAddress = append(inputAddress, &model.PmsMaterialAddress{
				Address: item,
			})
		}
	}

	// 构造要修改的物料数据
	materials := make([]*model.PmsMaterial, 0)
	err = yc.DBM(model.NewPmsMaterial()).Ctx(ctx).WhereIn("code", codes).Scan(&materials)

	// 开启事务
	err = yc.DBM(s.Model).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新物料表的地址
		if len(inputAddress) > 0 {
			_, err = yc.DBM(model.NewPmsMaterialAddress()).Ctx(ctx).Data(inputAddress).Insert()
			if err != nil {
				return err
			}
		}

		if len(materials) > 0 {
			for _, item := range materials {
				if _, ok := codeMap[item.Code]; ok {
					item.AddressName = codeMap[item.Code]
					_, err = yc.DBM(model.NewPmsMaterial()).Ctx(ctx).Data(item).Where("id", item.ID).Update()
					if err != nil {
						return err
					}
				}
			}
		}
		return nil
	})

	return
}

// 判断字符串是否在切片中
func contains(slice []string, str string) (bool, string) {
	var flag bool
	var address string
	var step string
	for _, item := range slice {
		if item == str {
			flag = true
		} else {
			address += step + item
			step = ","
		}
	}
	return flag, address
}

// 在物料表中删除一条物料地址
func deleteMaterIalOneAddress(ctx context.Context, addressName string) (err error) {
	//查询物料表中地址不为空的数据
	materials := make([]*model.PmsMaterial, 0)
	err = yc.DBM(model.NewPmsMaterial()).Ctx(ctx).WhereNot("address_name", "").Scan(&materials)
	for _, item := range materials {
		tmpAddress := item.AddressName
		arrAddress := strings.Split(tmpAddress, ",")
		// 调用函数判断
		if isContains, inputAddress := contains(arrAddress, addressName); isContains {
			item.AddressName = inputAddress
			_, err = yc.DBM(model.NewPmsMaterial()).Ctx(ctx).Data(item).Where("id", item.ID).Update()
			if err != nil {
				return err
			}
		}
	}
	return err
}

// 删除物料出库位置
func deleteMaterialOutboundAddress(ctx context.Context, addressName string) (err error) {
	//查询出库产品表中地址不为空的数据
	materialOutboundProduct := make([]*model.PmsMaterialOutboundProduct, 0)
	err = yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).WhereNot("address", "").Scan(&materialOutboundProduct)
	for _, item := range materialOutboundProduct {
		tmpAddress := item.Address
		arrAddress := strings.Split(tmpAddress, ",")
		// 调用函数判断
		if isContains, inputAddress := contains(arrAddress, addressName); isContains {
			item.Address = inputAddress
			_, err = yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).Data(item).Where("id", item.ID).Update()
			if err != nil {
				return err
			}
		}
	}
	return err
}

// 删除物料入库位置
func deleteMaterialInboundAddress(ctx context.Context, addressName string) (err error) {
	//查询入库产品表中地址不为空的数据
	materialInboundProduct := make([]*model.PmsMaterialInboundProduct, 0)
	err = yc.DBM(model.NewPmsMaterialInboundProduct()).Ctx(ctx).WhereNot("address", "").Scan(&materialInboundProduct)
	for _, item := range materialInboundProduct {
		tmpAddress := item.Address
		arrAddress := strings.Split(tmpAddress, ",")
		// 调用函数判断是否包含要删除的地址
		if isContains, inputAddress := contains(arrAddress, addressName); isContains {
			item.Address = inputAddress
			_, err = yc.DBM(model.NewPmsMaterialInboundProduct()).Ctx(ctx).Data(item).Where("id", item.ID).Update()
			if err != nil {
				return err
			}
		}
	}
	return err
}

// DeleteMaterialAddress 删除物料地址列
func (s *PmsMaterialService) DeleteMaterialAddress(ctx context.Context, addressName string) (err error) {
	if addressName == "" {
		return gerror.New("物料地址不能为空")
	}

	// 开启事务
	err = yc.DBM(s.Model).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 删除物料表的相关地址
		err = deleteMaterIalOneAddress(ctx, addressName)
		if err != nil {
			return err
		}
		//删除物料出库位置相关功能
		err = deleteMaterialOutboundAddress(ctx, addressName)
		if err != nil {
			return err
		}
		//删除物料入库位置相关功能
		err = deleteMaterialInboundAddress(ctx, addressName)
		if err != nil {
			return err
		}

		// 删除地址表内的数据
		_, err = yc.DBM(model.NewPmsMaterialAddress()).Ctx(ctx).Where("address", addressName).Delete()
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return err
	}

	return err
}

// AddMaterialAddress 获取物料地址列表
func (s *PmsMaterialService) AddMaterialAddress(ctx context.Context, addressName string) (id int64, err error) {
	// 查询重复项
	count, err := yc.DBM(model.NewPmsMaterialAddress()).Ctx(ctx).
		Where("address", addressName).Count()
	if err != nil {
		return 0, err
	}
	if count > 0 {
		return 0, gerror.New("地址已存在")
	}

	//插入数据
	id, err = yc.DBM(model.NewPmsMaterialAddress()).Ctx(ctx).InsertAndGetId(model.PmsMaterialAddress{
		Address: addressName,
	})
	return id, err
}

// GetMaterialAddress 获取物料地址列表
func (s *PmsMaterialService) GetMaterialAddress(ctx context.Context) (data []model.PmsMaterialAddress, err error) {
	addrLists := make([]model.PmsMaterialAddress, 0)
	err = yc.DBM(model.NewPmsMaterialAddress()).Ctx(ctx).Scan(&addrLists)
	if err != nil {
		return nil, err
	}
	return addrLists, err
}

// ReturnInventory 库存退货
func (s *PmsMaterialService) ReturnInventory(ctx context.Context, outbound *model.PmsMaterialOutbound, isRevert bool) (data interface{}, err error) {
	if outbound.ID == 0 {
		return nil, errors.New("入库单id不能为空")
	}
	if len(outbound.Products) == 0 {
		return nil, errors.New("退货物料不能为空")
	}
	actionName := "库存退货"
	if isRevert {
		actionName = "撤销" + actionName
	}
	for _, product := range outbound.Products {
		quantity := product.Quantity
		if !isRevert {
			cr := &model.PmsPurchaseContractReturn{}
			cr.Quantity = quantity
			cr.OutboundId = outbound.ID
			_, err = yc.DBM(model.NewPmsPurchaseContractReturn()).Ctx(ctx).Data(cr).Insert()
			if err != nil {
				return nil, err
			}
		}

		ms := &model.MaterialStock{MaterialId: product.MaterialId}
		if isRevert {
			// 撤销出库  +库存， -在途
			ms.Stock = quantity
			ms.ExpectedInbound = -quantity
		} else {
			// 出库 -库存， +在途
			ms.Stock = -quantity
			ms.ExpectedInbound = quantity
		}
		ms.Remark = fmt.Sprintf("%s：%s，更新在途：%f，更新库存：%f，", actionName, outbound.No, ms.Stock, ms.ExpectedInbound)
		err := s.updateMaterialStockV2(ctx, ms)
		if err != nil {
			return nil, err
		}
	}
	return nil, err
}

//func (s *PmsMaterialService) ModifyAfter(ctx context.Context, method string, param g.MapStrAny, data interface{}) (err error) {
//	if method == "Add" || method == "Update" {
//		// 如果是新增，从data中读取添加后的ID  g.Map{"id": lastInsertId}
//		var id int64
//		if method == "Add" {
//			id = gconv.Int64(data.(g.Map)["id"])
//		} else {
//			id = gconv.Int64(param["id"])
//		}
//		addressName := gconv.String(param["address_name"])
//		lists, err := s.GetMaterialAddress(ctx)
//		if err != nil {
//			return err
//		}
//		info := model.PmsMaterial{}
//
//		if addressName != "" {
//			flag := false
//			for _, item := range lists {
//				//如果匹配成功则填入位置id
//				if item.Address == addressName {
//					info.AddressId = item.ID
//					info.AddressName = addressName
//					flag = true
//				}
//			}
//			//没有匹配成功，则创建位置信息
//			if !flag {
//				id, err := yc.DBM(model.NewPmsMaterialAddress()).Ctx(ctx).InsertAndGetId(model.PmsMaterialAddress{
//					Address: addressName,
//				})
//				if err != nil {
//					return err
//				}
//				info.AddressId = id
//				info.AddressName = addressName
//			}
//			_, err = yc.DBM(s.Model).Ctx(ctx).OmitEmptyData().Data(info).Where("id", id).Update()
//			if err != nil {
//				return err
//			}
//		}
//		return nil
//	}
//	return nil
//}

func (s *PmsMaterialService) ModifyBefore(ctx context.Context, method string, para g.MapStrAny) (err error) {
	// 如果是删除，则需要判断此物料是否被使用
	if method == "Delete" {
		ids := gconv.Int64s(para["ids"])
		if len(ids) > 0 {
			// 判断此物料是否被使用
			count, err2 := yc.DBM(model.NewPmsBomMaterial()).Ctx(ctx).WhereIn("material_id", ids).Count()
			if err2 != nil {
				return err2
			}

			if count > 0 {
				return gerror.New("此物料已被使用，无法删除")
			}

			// 如果有任何库存数据，也不能删除
			materials := make([]*model.PmsMaterial, 0)
			err2 = yc.DBM(s.Model).Ctx(ctx).WhereIn("id", ids).Scan(&materials)
			if err2 != nil && !errors.Is(err2, sql.ErrNoRows) {
				return err2
			}

			for _, material := range materials {
				if material.Inventory > 0 || material.ExpectedInbound > 0 || material.LockedInventory > 0 || material.OccupiedInventory > 0 || material.DeductibleExpectedInbound > 0 || material.UsedExpectedInbound > 0 {
					return gerror.New("此物料已有库存数据，无法删除")
				}
			}

		}
	}
	if method == "Add" || method == "Update" {
		// 删除禁止修改的字段
		r := g.RequestFromCtx(ctx)

		r.SetParam("inventory", nil)
		r.SetParam("expectedInbound", nil)

		// 绑定创建人
		isBindUser := gconv.Bool(para["isBindUser"])
		if isBindUser {
			// 获取当前登录用户
			userId := yc.GetAdmin(ctx).UserId
			r.SetParam("bindUserId", userId)
			// 删除 isBindUser 字段
			r.SetParam("isBindUser", nil)
		}

		if method == "Add" {
			// 将物料编码去除空格
			code := r.Get("code").String()
			r.SetParam("code", gstr.Trim(code))
		}

		// 禁止修改物料编码
		if method == "Update" {
			g.RequestFromCtx(ctx).SetParam("code", nil)

			material := &model.PmsMaterial{}
			err = gconv.Struct(para, material)
			if err != nil {
				return err
			}

			nm := &model.PmsMaterial{}
			err = yc.DBM(nm).Ctx(ctx).Where("id", material.ID).Scan(&nm)
			if err != nil && !errors.Is(err, sql.ErrNoRows) {
				return err
			}

			if nm.ID > 0 {
				// 判断2个物料是否发生变化
				from := &model.MaterialChangeEntity{}
				to := &model.MaterialChangeEntity{}
				err1 := gconv.Struct(nm, &from)
				err2 := gconv.Struct(material, &to)
				if err1 == nil || err2 == nil {
					changelog, err := diff.Diff(from, to)
					if err == nil {
						// 保存变更记录
						var changeLogs []model.MaterialInfoChangeLog
						for _, log := range changelog {
							changeLogs = append(changeLogs, model.MaterialInfoChangeLog{
								Type:   model.MaterialChangeLogType(log.Type),
								Path:   gstr.CaseCamelLower(log.Path[0]),
								From:   log.From,
								To:     log.To,
								Before: from,
								After:  to,
							})
						}
						err = NewPmsMaterialBomChangeLogService().AddChangeLogs(ctx, material.ID, 0, model.MaterialBomChangeLogOperationTypeInfo, changeLogs, false)
						if err != nil {
							return err
						}
					}
				}
			}
		}
	}
	return nil
}

// updateMaterialStock 更新单个物料库存，可同时更新多个库存属性
func (s *PmsMaterialService) updateMaterialStock(ctx context.Context, materialStock *model.MaterialStock) (err error) {
	if materialStock.Stock == 0 && materialStock.ExpectedInbound == 0 && materialStock.LockedStock == 0 &&
		materialStock.OccupiedInventory == 0 && materialStock.DeductibleExpectedInbound == 0 && materialStock.UsedExpectedInbound == 0 {
		return nil
	}

	materialId := materialStock.MaterialId

	if materialId == 0 {
		return gerror.New("物料ID有误")
	}

	// 获取物料信息
	var material *model.PmsMaterial
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", materialId).Scan(&material)
	if err != nil {
		return
	}

	if material == nil {
		return gerror.New("物料不存在")
	}

	// 记录原始数据，用于更新时判断
	originMaterial := &model.PmsMaterial{}
	err = gconv.Struct(material, originMaterial)
	if err != nil {
		return
	}

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		materialStockLogs := make([]*model.PmsMaterialStockLog, 0)

		// 先处理库存变化
		if materialStock.Stock != 0 {
			before := material.Inventory
			after := material.Inventory + materialStock.Stock

			if after < 0 {
				g.Log().Debugf(ctx, "更新库存失败，库存不足，物料ID：%d，库存：%f，更新库存：%f", materialId, material.Inventory, materialStock.Stock)
				return gerror.New("更新库存失败，库存不足")
			}

			materialStockLogs = append(materialStockLogs, &model.PmsMaterialStockLog{
				MaterialId: materialId,
				Before:     before,
				After:      after,
				Type:       int(model.MaterialStockTypeAvailable),
				Quantity:   materialStock.Stock,
				Remark:     materialStock.Remark,
			})

			material.Inventory = after
		}

		if materialStock.ExpectedInbound != 0 {
			before := material.ExpectedInbound
			after := material.ExpectedInbound + materialStock.ExpectedInbound

			if after < 0 {
				g.Log().Debugf(ctx, "更新在途数量失败，在途数量不足，物料ID：%d，在途数量：%f，更新在途数量：%f", materialId, material.ExpectedInbound, materialStock.ExpectedInbound)
				return gerror.New("更新在途数量失败，在途数量不足")
			}

			materialStockLogs = append(materialStockLogs, &model.PmsMaterialStockLog{
				MaterialId: materialId,
				Before:     before,
				After:      after,
				Type:       int(model.MaterialStockTypeExpected),
				Quantity:   materialStock.ExpectedInbound,
				Remark:     materialStock.Remark,
			})

			material.ExpectedInbound = after
		}

		if materialStock.LockedStock != 0 {
			before := material.LockedInventory
			after := material.LockedInventory + materialStock.LockedStock

			if after < 0 {
				g.Log().Debugf(ctx, "更新锁定库存失败，锁定库存不足，物料ID：%d，物料编码：%s，锁定库存：%f，更新锁定库存：%f", materialId, material.Code, material.LockedInventory, materialStock.LockedStock)
				return gerror.New("更新锁定库存失败，锁定库存不足")
			}

			materialStockLogs = append(materialStockLogs, &model.PmsMaterialStockLog{
				MaterialId: materialId,
				Before:     before,
				After:      after,
				Type:       int(model.MaterialStockTypeLocked),
				Quantity:   materialStock.LockedStock,
				Remark:     materialStock.Remark,
			})

			material.LockedInventory = after
		}

		if materialStock.OccupiedInventory != 0 {
			before := material.OccupiedInventory
			after := material.OccupiedInventory + materialStock.OccupiedInventory

			if after < 0 {
				msg := fmt.Sprintf("更新占用库存失败，占用库存不足，物料ID：%d，物料编码：%s，占用库存：%f，更新占用库存：%f", materialId, material.Code, material.OccupiedInventory, materialStock.OccupiedInventory)
				g.Log().Debugf(ctx, msg)
				return gerror.New(msg)
			}

			materialStockLogs = append(materialStockLogs, &model.PmsMaterialStockLog{
				MaterialId: materialId,
				Before:     before,
				After:      after,
				Type:       int(model.MaterialStockTypeOccupied),
				Quantity:   materialStock.OccupiedInventory,
				Remark:     materialStock.Remark,
			})

			material.OccupiedInventory = after
		}

		if materialStock.DeductibleExpectedInbound != 0 {
			before := material.DeductibleExpectedInbound
			after := material.DeductibleExpectedInbound + materialStock.DeductibleExpectedInbound

			if after < 0 {
				g.Log().Debugf(ctx, "更新可用在途数量失败，可用在途数量不足，物料ID：%d，物料编码：%s，可用在途数量：%f，更新可用在途数量：%f", materialId, material.Code, material.ExpectedInbound, materialStock.DeductibleExpectedInbound)
				return gerror.New("更新可用在途数量失败，可用在途数量不足")
			}

			materialStockLogs = append(materialStockLogs, &model.PmsMaterialStockLog{
				MaterialId: materialId,
				Before:     before,
				After:      after,
				Type:       int(model.MaterialStockTypeDeductibleExpected),
				Quantity:   materialStock.DeductibleExpectedInbound,
				Remark:     materialStock.Remark,
			})

			material.DeductibleExpectedInbound = after
		}

		if materialStock.UsedExpectedInbound != 0 {
			before := material.UsedExpectedInbound
			after := material.UsedExpectedInbound + materialStock.UsedExpectedInbound

			if after < 0 {
				g.Log().Debugf(ctx, "更新不可用在途数量失败，不可用在途数量不足，物料ID：%d，物料编码：%s，不可用在途数量：%f，更新不可用在途数量：%f", materialId, material.Code, material.DeductibleExpectedInbound, materialStock.UsedExpectedInbound)
				return gerror.New("更新不可用在途数量失败，不可用在途数量不足")
			}

			materialStockLogs = append(materialStockLogs, &model.PmsMaterialStockLog{
				MaterialId: materialId,
				Before:     before,
				After:      after,
				Type:       int(model.MaterialStockTypeUsedExpected),
				Quantity:   materialStock.UsedExpectedInbound,
				Remark:     materialStock.Remark,
			})

			material.UsedExpectedInbound = after
		}

		// 添加库存变更记录
		if len(materialStockLogs) > 0 {
			_, err = yc.DBM(model.NewPmsMaterialStockLog()).Ctx(ctx).Data(materialStockLogs).Insert()
			if err != nil {
				return err
			}
		}

		// 执行更新
		affected, err := yc.DBM(s.Model).Ctx(ctx).Data(material).Where("id", materialId).
			Where("inventory", originMaterial.Inventory).
			Where("expected_inbound", originMaterial.ExpectedInbound).
			Where("locked_inventory", originMaterial.LockedInventory).
			Where("occupied_inventory", originMaterial.OccupiedInventory).
			Where("deductible_expected_inbound", originMaterial.DeductibleExpectedInbound).
			Where("used_expected_inbound", originMaterial.UsedExpectedInbound).
			UpdateAndGetAffected()

		if err != nil {
			return err
		}

		if affected == 0 {
			return gerror.New("更新库存失败，库存已发生变化")
		}

		return nil
	})

	if err != nil {
		return
	}

	return
}

func (s *PmsMaterialService) updateMaterialStockV2(ctx context.Context, materialStock *model.MaterialStock) (err error) {
	materialId := materialStock.MaterialId
	if materialId == 0 {
		return gerror.New("物料ID有误")
	}
	//if materialStock.OperationType == 0 {
	//	return gerror.New("操作类型不能为空")
	//}
	// 获取物料信息
	var material *model.PmsMaterial
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", materialId).Scan(&material)
	if err != nil {
		return
	}

	if material == nil {
		return gerror.New("物料不存在")
	}

	// 记录原始数据，用于更新时判断
	originMaterial := &model.PmsMaterial{}
	updatePmsMaterial := g.MapStrAny{}
	err = gconv.Struct(material, originMaterial)
	if err != nil {
		return
	}

	materialStockLogs := make([]*model.PmsMaterialStockLog, 0)
	// 采购下单 + 在途库存
	if materialStock.ExpectedInbound != 0 {

		before := material.ExpectedInbound
		after := material.ExpectedInbound + materialStock.ExpectedInbound

		if after < 0 {
			g.Log().Debugf(ctx, "更新在途数量失败，在途数量不足，物料ID：%d，在途数量：%f，更新在途数量：%f", materialId, material.ExpectedInbound, materialStock.ExpectedInbound)
			return gerror.New("更新在途数量失败，在途数量不足")
		}
		materialStockLog := s.CreatePmsMaterialStockLog(ctx, materialStock)
		materialStockLog.Quantity = materialStock.ExpectedInbound
		materialStockLog.Before = before
		materialStockLog.After = after
		materialStockLog.Type = 3
		materialStockLogs = append(materialStockLogs, materialStockLog)

		material.ExpectedInbound = after
		updatePmsMaterial["expected_inbound"] = after
	}

	// 物料入库 +库存数量, -在途库存
	if materialStock.Stock != 0 {
		before := material.Inventory
		//仓库要求采购下单扣减库存时库存不再变化
		after := material.Inventory

		if materialStock.OperationType != -model.OperationTypePurchaseOrder && materialStock.OperationType != model.OperationTypePurchaseOrder {
			after = material.Inventory + materialStock.Stock
		}

		if after < 0 {
			g.Log().Debugf(ctx, "更新库存失败，库存不足，物料ID：%d，库存：%f，更新库存：%f", materialId, material.Inventory, materialStock.Stock)
			return gerror.New("更新库存失败，库存不足")
		}
		materialStockLog := s.CreatePmsMaterialStockLog(ctx, materialStock)
		materialStockLog.Quantity = 0

		if materialStock.OperationType != -model.OperationTypePurchaseOrder && materialStock.OperationType != model.OperationTypePurchaseOrder {
			materialStockLog.Quantity = materialStock.Stock
		}

		materialStockLog.Before = before
		materialStockLog.After = after
		materialStockLogs = append(materialStockLogs, materialStockLog)
		material.Inventory = after
		updatePmsMaterial["inventory"] = after
		stockLog := s.CreatePmsMaterialStockLog(ctx, materialStock)
		stockLog.Before = material.ExpectedInbound
		stockLog.Quantity = materialStock.Stock
		// 采购单入库  更新在途库存
		if materialStock.OperationType == model.OperationTypeInboundPurchase || materialStock.OperationType == -model.OperationTypeInboundPurchase {
			expectedInboundAfter := material.ExpectedInbound - materialStock.Stock
			if expectedInboundAfter < 0 {
				expectedInboundAfter = 0
			}
			updatePmsMaterial["expected_inbound"] = expectedInboundAfter
			//stockLog.Remark = stockLog.Remark + "修改前在途库存" + gconv.String(material.ExpectedInbound) + "修改后在途库存" + gconv.String(expectedInboundAfter)
			stockLog.After = expectedInboundAfter
			//stockLog.After = after
			stockLog.Remark = gstr.Replace(materialStock.Remark, "库存", "更新在途库存")
			stockLog.Type = 4
			materialStockLogs = append(materialStockLogs, stockLog)
		}
	}

	// 领料出库 -库存数量
	if materialStock.UsedQuantity != 0 {
		// 当前库存
		before := material.Inventory
		// 更新后库存 注意（materialStock.UsedQuantity 正数，还是负数）
		after := material.Inventory + materialStock.UsedQuantity

		if after < 0 {
			g.Log().Debugf(ctx, "更新锁定库存失败，锁定库存不足，物料ID：%d，物料编码：%s，锁定库存：%f，更新锁定库存：%f", materialId, material.Code, material.LockedInventory, materialStock.LockedStock)
			return gerror.New("更新库存失败，库存不足")
		}

		materialStockLog := s.CreatePmsMaterialStockLog(ctx, materialStock)
		materialStockLog.Quantity = materialStock.UsedQuantity
		materialStockLog.Before = before
		materialStockLog.After = after
		materialStockLog.Type = 4
		materialStockLogs = append(materialStockLogs, materialStockLog)

		material.Inventory = after
		updatePmsMaterial["inventory"] = after
		//material.UsedQuantity += materialStock.UsedQuantity
		//updatePmsMaterial["used_quantity"] = material.UsedQuantity
	}

	_, err = yc.DBM(s.Model).Ctx(ctx).Data(updatePmsMaterial).Where("id", materialId).UpdateAndGetAffected()
	if err != nil {
		return err
	}

	//if affected == 0 {
	//	return gerror.New("更新库存失败，库存已发生变化")
	//}
	// 添加库存变更记录
	if len(materialStockLogs) > 0 {
		admin := yc.GetAdmin(ctx)
		for i := range materialStockLogs {
			materialStockLogs[i].ContractId = admin.UserId
			materialStockLogs[i].CreateTime = gtime.Now()
		}
		_, err = yc.DBM(model.NewPmsMaterialStockLog()).Ctx(ctx).Data(materialStockLogs).Insert()
		if err != nil {
			return err
		}
	}

	return nil
}

// UpdateMaterialStocks 批量更新物料库存
func (s *PmsMaterialService) UpdateMaterialStockBatchV2(ctx context.Context, stocks []*model.MaterialStock) error {
	// 遍历stocks，更新每个物料的库存
	for _, stock := range stocks {
		err := s.updateMaterialStockV2(ctx, stock)

		if err != nil {
			return err
		}
	}

	return nil
}

// UpdateExpectedInbound 更新预计入库数量
/*func (s *PmsMaterialService) UpdateExpectedInbound(ctx context.Context, materialId int64, quantity float64) (err error) {
	if quantity == 0 {
		return gerror.New("物料预计入库数量有误")
	}
	// 获取物料信息
	var material *model.PmsMaterial
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", materialId).Scan(&material)
	if err != nil {
		return
	}

	if material == nil {
		return gerror.New("物料不存在")
	}

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 修改预计入库数量
		newExpectedInbound := material.ExpectedInbound + quantity
		if newExpectedInbound < 0 {
			newExpectedInbound = 0
		}

		// 更新物料库存
		affected, err := yc.DBM(s.Model).Ctx(ctx).Data(g.Map{
			"expected_inbound": newExpectedInbound,
		}).Where("id", materialId).Where("expected_inbound", material.ExpectedInbound).UpdateAndGetAffected()

		if err != nil {
			return err
		}

		if affected == 0 {
			return gerror.New("预计入库数量更新失败")
		}

		return nil
	})

	if err != nil {
		return
	}

	return
}*/

// GetAvailableInventory 获取可用库存
func (s *PmsMaterialService) GetAvailableInventory(ctx context.Context, id int64) (material *model.PmsMaterial, inventory float64, err error) {
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", id).Scan(&material)
	if err != nil {
		return
	}

	if material == nil {
		return nil, 0, gerror.New("物料不存在")
	}

	return material, math.Max(material.Inventory-material.LockedInventory, 0), nil
}

// GetInventory 获取库存
func (s *PmsMaterialService) GetInventory(ctx context.Context, id int64) (inventory float64, err error) {
	var material *model.PmsMaterial
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", id).Scan(&material)
	if err != nil {
		return
	}

	if material == nil {
		return 0, gerror.New("物料不存在")
	}

	return material.Inventory, nil
}

// GetMaterialByName 获取物料信息
func (s *PmsMaterialService) GetMaterialByName(ctx context.Context, keyword string) (material []*model.PmsMaterial, err error) {
	if keyword == "" {
		return nil, gerror.New("物料名称不能为空")
	}
	materials := make([]*model.PmsMaterial, 0)
	err = yc.DBM(s.Model).Ctx(ctx).Where("name like ? OR code like ?", "%"+keyword+"%", "%"+keyword+"%").Scan(&materials)
	if err != nil {
		return nil, err
	}
	return materials, err
}

// GetMaterialById 获取物料信息
func (s *PmsMaterialService) GetMaterialById(ctx context.Context, id int64) (material *model.PmsMaterial, err error) {
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", id).Scan(&material)
	if err != nil {
		return
	}

	if material == nil {
		return nil, gerror.New("物料不存在")
	}

	return
}

func (s *PmsMaterialService) MaterialInboundSummary(ctx context.Context, year int, month int) (date interface{}, err error) {
	// 年月不能同时为空
	if year == 0 && month == 0 {
		return nil, gerror.New("时间有误")
	}

	// 月范围
	if month < 0 || month > 12 {
		return nil, gerror.New("月份有误")
	}

	// 年不能大于当前年
	if year > gtime.Now().Year() {
		return nil, gerror.New("年份有误")
	}

	// 如果年月同时存在，要判断选择日期不能大于当前日期
	if year > 0 && month > 0 {
		if year > gtime.Now().Year() || (year == gtime.Now().Year() && month > gtime.Now().Month()) {
			return nil, gerror.New("年月有误")
		}
	}

	isYear := month == 0

	type Material struct {
		Id   int64  `json:"id"`
		Code string `json:"code"`
		Name string `json:"name"`
		Unit string `json:"unit"`
	}

	type MaterialSummary struct {
		MaterialId int64       `json:"materialId"`
		Quantity   int         `json:"quantity"`
		Date       *gtime.Time `json:"date"`
	}

	materials := make([]*Material, 0)
	err = yc.DBM(s.Model).Ctx(ctx).Fields("id, code, name, unit").Scan(&materials)
	if err != nil {
		return nil, err
	}

	// 获取当月范围内的入库记录
	materialInbound := make([]*MaterialSummary, 0)
	// 设置开始时间为今年月份 month 为5 年为今年
	var startTime, endTime *gtime.Time
	if isYear {
		startTime = gtime.NewFromStr(fmt.Sprintf("%02d-01-01", year)).StartOfYear()
		endTime = startTime.EndOfYear()
	} else {
		startTime = gtime.NewFromStr(fmt.Sprintf("%d-%02d-01", year, month))
		endTime = startTime.EndOfMonth()
	}

	// 获取物料入库记录
	err = yc.DBM(model.NewPmsMaterialInboundProduct()).Ctx(ctx).
		As("mip").
		Fields("mip.material_id,mip.quantity as quantity,mi.inbound_time as date").
		LeftJoin(fmt.Sprintf("%s AS mi", model.NewPmsMaterialInbound().TableName()), "mip.inbound_id = mi.id AND mi.status = 3").
		WhereBetween("mi.inbound_time", startTime, endTime).
		Scan(&materialInbound)

	if err != nil {
		return nil, err
	}

	inboundMaterialMap := map[int64]map[string]int{}
	// 遍历入库记录先按产品ID分组再按日期分组
	for _, im := range materialInbound {
		if _, ok := inboundMaterialMap[im.MaterialId]; !ok {
			inboundMaterialMap[im.MaterialId] = map[string]int{}
		}

		d := fmt.Sprintf("date-%s", im.Date.Format("Ymd"))
		if isYear {
			d = fmt.Sprintf("date-%s", im.Date.Format("Ym"))
		}

		// 查找当前产品的日期是否存在
		if _, ok := inboundMaterialMap[im.MaterialId][d]; !ok {
			inboundMaterialMap[im.MaterialId][d] = 0
		}

		// 将当前产品的数量加上
		inboundMaterialMap[im.MaterialId][d] += im.Quantity
	}

	// 获取物料出库记录
	materialOutbound := make([]*MaterialSummary, 0)
	err = yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).
		As("mop").
		Fields("mop.material_id,mop.quantity as quantity,mo.outbound_time as date").
		LeftJoin(fmt.Sprintf("%s AS mo", model.NewPmsMaterialOutbound().TableName()), "mop.outbound_id = mo.id AND mo.status = 3").
		WhereBetween("mo.outbound_time", startTime, endTime).
		Scan(&materialOutbound)

	if err != nil {
		return nil, err
	}

	outboundMaterialMap := map[int64]map[string]int{}
	// 遍历出库记录先按产品ID分组再按日期分组
	for _, om := range materialOutbound {
		if _, ok := outboundMaterialMap[om.MaterialId]; !ok {
			outboundMaterialMap[om.MaterialId] = map[string]int{}
		}

		d := fmt.Sprintf("date-%s", om.Date.Format("Ymd"))
		if isYear {
			d = fmt.Sprintf("date-%s", om.Date.Format("Ym"))
		}

		// 查找当前产品的日期是否存在
		if _, ok := outboundMaterialMap[om.MaterialId][d]; !ok {
			outboundMaterialMap[om.MaterialId][d] = 0
		}

		// 将当前产品的数量加上
		outboundMaterialMap[om.MaterialId][d] += om.Quantity
	}

	materialSlice := g.Slice{}
	// 遍历物料列表，将入库记录填充到物料列表中
	for _, m := range materials {
		mp := gconv.Map(m)

		if _, ok := inboundMaterialMap[m.Id]; ok {
			for key, value := range inboundMaterialMap[m.Id] {
				keyInbound := fmt.Sprintf("%s-inbound", key)
				mp[key] = gconv.Int(mp[key]) + value
				mp[keyInbound] = value

				mp["total-inbound"] = gconv.Int(mp["total-inbound"]) + value
				mp["total"] = gconv.Int(mp["total"]) + value
			}
		}

		if _, ok := outboundMaterialMap[m.Id]; ok {
			for key, value := range outboundMaterialMap[m.Id] {
				keyOutbound := fmt.Sprintf("%s-outbound", key)
				mp[key] = gconv.Int(mp[key]) - value
				mp[keyOutbound] = value

				mp["total-outbound"] = gconv.Int(mp["total-outbound"]) + value
				mp["total"] = gconv.Int(mp["total"]) + value
			}
		}
		materialSlice = append(materialSlice, mp)
	}

	return materialSlice, nil
}

// UpdateMaterialStocks 批量更新物料库存
func (s *PmsMaterialService) UpdateMaterialStocks(ctx context.Context, stocks []*model.MaterialStock) error {
	// 遍历stocks，更新每个物料的库存
	for _, stock := range stocks {
		err := s.updateMaterialStock(ctx, stock)

		if err != nil {
			return err
		}
	}

	return nil
}

// UpdateMaterialVersion 更新物料版本
func (s *PmsMaterialService) UpdateMaterialVersion(ctx context.Context, materialId int64) error {
	isSkipAudit := false

	// 根据物料ID获取到包含该物料的所有BOM
	bomIds, err := yc.DBM(model.NewPmsBomMaterial()).Ctx(ctx).Where("material_id", materialId).Fields("bom_id").Array()
	if err != nil {
		return err
	}

	// 遍历BOM列表添加审核记录
	for _, bid := range bomIds {
		bomId := bid.Int64()
		isSkipAudit, err = NewPmsBomService().AddAudit(ctx, bomId)
		if err != nil {
			return err
		}
	}

	// 更新BOM的版本
	if isSkipAudit {
		// 先获取到BOM的版本号
		bom := make([]*model.PmsBom, 0)
		err = yc.DBM(model.NewPmsBom()).Ctx(ctx).WhereIn("id", bomIds).Scan(&bom)
		if err != nil {
			return err
		}

		// 遍历BOM列表，更新版本号
		for _, b := range bom {
			// 更新版本号
			b.Version = math.Round(b.Version + 0.01)
			b.Status = int(model.BomStatusNormal)
			_, err = yc.DBM(model.NewPmsBom()).Ctx(ctx).Data(b).OmitNilData().Where("id", b.ID).Update()
			if err != nil {
				return err
			}
		}

	} else {
		_, err = yc.DBM(model.NewPmsBom()).Ctx(ctx).WhereIn("id", bomIds).Increment("version", 0.01)

	}

	if err != nil {
		return err
	}

	return nil
}

// UpdateMaterialsWithChangeLog 更新物料信息并记录变更日志
func (s *PmsMaterialService) UpdateMaterialsWithChangeLog(ctx context.Context, materialsAfter []*model.PmsMaterial, materialsBefore []*model.PmsMaterial) error {
	// 更新物料信息
	_, err := yc.DBM(s.Model).Ctx(ctx).Data(materialsAfter).Save()
	if err != nil {
		return err
	}

	changeLogs := make([]*model.MaterialInfoChangeLog, 0)

	for _, materialAfter := range materialsAfter {
		var materialBefore *model.PmsMaterial
		for _, material := range materialsBefore {
			if materialAfter.ID == material.ID {
				materialBefore = material
				break
			}
		}

		if materialBefore == nil {
			continue
		}

		from, to := &model.MaterialChangeEntity{}, &model.MaterialChangeEntity{}
		if err := gconv.Struct(materialBefore, from); err != nil {
			continue
		}
		if err := gconv.Struct(materialAfter, to); err != nil {
			continue
		}

		changelog, err := diff.Diff(from, to)
		if err != nil {
			continue
		}

		for _, log := range changelog {
			changeLogs = append(changeLogs, &model.MaterialInfoChangeLog{
				Type:   model.MaterialChangeLogType(log.Type),
				Path:   gstr.CaseCamelLower(log.Path[0]),
				From:   log.From,
				To:     log.To,
				Before: from,
				After:  to,
			})
		}
	}

	if len(changeLogs) > 0 {
		err = NewPmsMaterialBomChangeLogService().AddChangeLogs(ctx, materialsAfter[0].ID, 0, model.MaterialBomChangeLogOperationTypeInfo, changeLogs, false)
		return err
	}

	return nil
}

// ExportMaterialList 导出物料列表
func (s *PmsMaterialService) ExportMaterialList(ctx context.Context) (fileName string, bytes []byte, err error) {
	// 获取物料列表
	materials := make([]*model.PmsMaterial, 0)
	err = yc.DBM(s.Model).Ctx(ctx).Scan(&materials)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return
	}

	// 创建一个新的 Excel 文件
	sheetName := "Sheet1"
	// 在打包资源你获取模板文件
	templatePath := "resource/template/excel/material_template.xlsx"
	var f *excelize.File
	// 判断资源中是否存在文件，如果存在则使用资源文件。否则查找本地文件
	if gres.Contains(templatePath) {
		// 获取模板文件
		reader, err := gres.Get(templatePath).Open()
		if err != nil {
			g.Log().Errorf(ctx, "模版文件不存在，读取模板文件失败%s", templatePath)
			return "", nil, gerror.New(yc.T(ctx, "exportFailedReadTemplateFileFailed"))
		}

		defer func(reader io.ReadCloser) {
			_ = reader.Close()
		}(reader)

		f, _ = excelize.OpenReader(reader)
	} else if gfile.Exists(templatePath) {
		f, err = excelize.OpenFile(templatePath)
	} else {
		g.Log().Errorf(ctx, "模版文件不存在，模板路径%s", templatePath)
		return "", nil, gerror.New(yc.T(ctx, "exportFailedTemplateFileNotExist"))
	}

	defer func(f *excelize.File) {
		_ = f.Close()
	}(f)

	startRow := 2
	for i, material := range materials {
		// 从第二行开始写入数据  循环物料列表，写入到 Excel 文件中 ID 物料编码 物料名称	型号 尺寸 材质 工艺 颜色 单位 库存 锁定库存 可用在库  在途库存	可用在途	不可用在途 订单占用
		_ = f.SetCellValue(sheetName, fmt.Sprintf("A%d", startRow+i), material.ID)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("B%d", startRow+i), material.Code)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("C%d", startRow+i), material.Name)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("D%d", startRow+i), material.Model)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("E%d", startRow+i), material.Size)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("F%d", startRow+i), material.Material)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("G%d", startRow+i), material.Process)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("H%d", startRow+i), material.CoverColor)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("I%d", startRow+i), material.Unit)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("J%d", startRow+i), material.Inventory)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("K%d", startRow+i), material.ExpectedInbound)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("L%d", startRow+i), material.AddressName)
		//_ = f.SetCellValue(sheetName, fmt.Sprintf("K%d", startRow+i), material.LockedInventory)
		//_ = f.SetCellValue(sheetName, fmt.Sprintf("L%d", startRow+i), math.Max(material.Inventory-material.LockedInventory, 0))
		//_ = f.SetCellValue(sheetName, fmt.Sprintf("M%d", startRow+i), material.ExpectedInbound)
		//_ = f.SetCellValue(sheetName, fmt.Sprintf("N%d", startRow+i), math.Max(material.DeductibleExpectedInbound-material.UsedExpectedInbound, 0))
		//_ = f.SetCellValue(sheetName, fmt.Sprintf("O%d", startRow+i), material.UsedExpectedInbound)
		//_ = f.SetCellValue(sheetName, fmt.Sprintf("P%d", startRow+i), material.OccupiedInventory)
	}

	// 如果temp文件夹不存在， 在当前项目中创建临时文件夹
	dir := "temp/"

	fileName = fmt.Sprintf("物料列表_%s.xlsx", gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)
	filePath := gfile.Join(dir, fileName)

	// 不存则创建
	if !gfile.IsDir(dir) {
		_ = gfile.Mkdir(dir)
	}

	// 导出 Excel 文件
	if err = f.SaveAs(filePath); err != nil {
		g.Log().Error(ctx, err)
		return
	}

	// 读取 Excel 文件内容
	bytes = gfile.GetBytes(filePath)
	// 删除临时文件
	_ = gfile.Remove(filePath)
	// 返回字节流
	return
}

func (s *PmsMaterialService) CreatePmsMaterialStockLog(ctx context.Context, materialStock *model.MaterialStock) *model.PmsMaterialStockLog {
	result := &model.PmsMaterialStockLog{
		MaterialId:           materialStock.MaterialId,
		ContractId:           materialStock.ContractId,
		ProductionScheduleId: materialStock.ProductionScheduleId,
		ProductionScheduleSn: materialStock.ProductionScheduleSn,
		PurchaseOrderId:      materialStock.PurchaseOrderId,
		PurchaseOrderSn:      materialStock.PurchaseOrderSn,
		InboundId:            materialStock.InboundId,
		InboundSn:            materialStock.InboundSn,
		OutboundId:           materialStock.OutboundId,
		OutboundSn:           materialStock.OutboundSn,
		OperationType:        materialStock.OperationType,
		OtherRemark:          materialStock.OtherRemark,
		WorkOrderId:          materialStock.WorkOrderId,
		WorkOrderNo:          materialStock.WorkOrderNo,
		Remark:               materialStock.Remark,
		CreatorId:            yc.GetAdmin(ctx).UserId,
		CreateTime:           gtime.Now(),
	}
	return result
}

/*func (s *PmsMaterialService) GetAvailableInventory(_ context.Context, info *model.PmsMaterial) float64 {
	// 可用库存 = 库存 + 预计入库 - 锁定库存
	return info.Inventory + info.ExpectedInbound - info.LockedInventory
}*/

func (s *PmsMaterialService) ExportMaterialInboundSummary(ctx context.Context, start *gtime.Time, end *gtime.Time) (fileName string, bytes []byte, err error) {
	// 将start转为当天的开始时间
	start = start.StartOfDay()
	// 将end转为当天的结束时间
	end = end.EndOfDay()

	// 获取物料入库记录
	type mt struct {
		*model.PmsMaterial
		MaterialId       int64   `json:"materialId"`
		InboundTotal     float64 `json:"inboundTotal"`
		OutboundTotal    float64 `json:"outboundTotal"`
		InitialInventory float64 `json:"initialInventory"`
		FinalInventory   float64 `json:"finalInventory"`
		BomTotal         float64 `json:"bomTotal"`
	}

	materials := make([]*mt, 0)
	err = yc.DBM(s.Model).Ctx(ctx).Scan(&materials)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return
	}

	// 获取范围内入库的总数量
	mi := make([]*mt, 0)
	inboundIds, err := yc.DBM(model.NewPmsMaterialInbound()).Ctx(ctx).
		Fields("id").
		Where("status", MaterialInboundStatusCompleted).
		WhereBetween("inbound_time", start, end).
		Array("id")
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return
	}

	err = yc.DBM(model.NewPmsMaterialInboundProduct()).Ctx(ctx).
		Fields("material_id, sum(quantity) as inboundTotal").
		WhereIn("inbound_id", inboundIds).
		Group("material_id").
		Scan(&mi)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return
	}

	// 定义物料id和入库总数量的map
	materialInboundMap := map[int64]float64{}
	for _, m := range mi {
		materialInboundMap[m.MaterialId] = m.InboundTotal
	}
	// 获取范围内出库的总数量
	mo := make([]*mt, 0)
	outboundIds, err := yc.DBM(model.NewPmsMaterialOutbound()).Ctx(ctx).
		Fields("id").
		Where("status", MaterialOutboundStatusCompleted).
		WhereBetween("outbound_time", start, end).
		Array("id")
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return
	}

	err = yc.DBM(model.NewPmsMaterialOutboundProduct()).Ctx(ctx).
		Fields("material_id, sum(quantity) as outboundTotal").
		WhereIn("outbound_id", outboundIds).
		Group("material_id").
		Scan(&mo)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return
	}

	// 定义物料id和出库总数量的map
	materialOutboundMap := map[int64]float64{}
	for _, m := range mo {
		materialOutboundMap[m.MaterialId] = m.OutboundTotal
	}

	// 获取范围内工单物料占用的总数量
	mc := make([]*mt, 0)
	err = yc.DBM(model.NewPmsWorkOrderDetail()).Ctx(ctx).
		Fields("material_id, sum(calc_bom_quantity) as bomTotal").
		WhereBetween("createTime", start, end).
		Group("material_id").
		Scan(&mc)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return
	}

	// 定义物料id和出库总数量的map
	bomQuantityMap := map[int64]float64{}
	for _, m := range mo {
		bomQuantityMap[m.MaterialId] = m.BomTotal
	}

	// 获取期初库存
	materialInitialInventory := make([]*mt, 0)

	initialQuery := fmt.Sprintf(`SELECT msl.material_id, msl.after AS initialInventory FROM %s AS msl 
										 JOIN (SELECT material_id, MAX(id) AS max_id FROM %s
										 WHERE type = %d AND createTime < '%s' GROUP BY material_id) AS sq ON msl.material_id = sq.material_id AND msl.id = sq.max_id`,
		model.NewPmsMaterialStockLog().TableName(),
		model.NewPmsMaterialStockLog().TableName(),
		model.MaterialStockTypeAvailable,
		start.String(),
	)

	result, err := g.DB().Query(ctx, initialQuery)

	if err != nil {
		return
	}

	err = result.Structs(&materialInitialInventory)
	if err != nil {
		return "", nil, err
	}

	// 定义物料id和期初库存的map
	materialInitialInventoryMap := map[int64]float64{}
	for _, m := range materialInitialInventory {
		materialInitialInventoryMap[m.MaterialId] = m.InitialInventory
	}

	// 获取期末库存
	materialFinalInventory := make([]*mt, 0)

	finalQuery := fmt.Sprintf(`SELECT msl.material_id, msl.after AS finalInventory FROM %s AS msl
										 JOIN (SELECT material_id, MAX(id) AS max_id FROM %s	
										 WHERE type = %d AND createTime <= '%s' GROUP BY material_id) AS sq ON msl.material_id = sq.material_id AND msl.id = sq.max_id`,
		model.NewPmsMaterialStockLog().TableName(),
		model.NewPmsMaterialStockLog().TableName(),
		model.MaterialStockTypeAvailable,
		end.String(),
	)

	result, err = g.DB().Query(ctx, finalQuery)
	if err != nil {
		return
	}

	err = result.Structs(&materialFinalInventory)
	if err != nil {
		return "", nil, err
	}

	// 定义物料id和期末库存的map
	materialFinalInventoryMap := map[int64]float64{}
	for _, m := range materialFinalInventory {
		materialFinalInventoryMap[m.MaterialId] = m.FinalInventory
	}

	// 将入库总数量和出库总数量填充到物料列表中
	for _, material := range materials {
		// 入库总数量
		if inboundTotal, ok := materialInboundMap[material.ID]; ok {
			material.InboundTotal = inboundTotal
		}

		// 出库总数量
		if outboundTotal, ok := materialOutboundMap[material.ID]; ok {
			material.OutboundTotal = outboundTotal
		}

		// 期初库存
		if initialInventory, ok := materialInitialInventoryMap[material.ID]; ok {
			material.InitialInventory = initialInventory
		}

		// 期末库存
		if finalInventory, ok := materialFinalInventoryMap[material.ID]; ok {
			material.FinalInventory = finalInventory
		}

		// 工单物料占用总数量
		if bomTotal, ok := bomQuantityMap[material.ID]; ok {
			material.BomTotal = bomTotal
		}
	}

	sheetName := "Sheet1"
	// 创建一个新的 Excel 文件
	f := excelize.NewFile()
	defer func() {
		if err = f.Close(); err != nil {
			g.Log().Error(ctx, "关闭文件失败", err)
			return
		}
	}()
	// 创建一个新的工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		g.Log().Error(ctx, "创建工作表失败", err)
		return
	}
	// 设置当前活动的工作表
	f.SetActiveSheet(index)

	// 设置表头 序号 物料编码 物料名称	型号 尺寸 材质 工艺 颜色 单位 期初库存 汇总入库数量 汇总出库数量 期末库存 现有库存 现有在途库存
	_ = f.SetCellValue(sheetName, "A1", "序号")
	_ = f.SetCellValue(sheetName, "B1", "物料编码")
	_ = f.SetCellValue(sheetName, "C1", "物料名称")
	_ = f.SetCellValue(sheetName, "D1", "型号")
	_ = f.SetCellValue(sheetName, "E1", "尺寸")
	_ = f.SetCellValue(sheetName, "F1", "材质")
	_ = f.SetCellValue(sheetName, "G1", "工艺")
	_ = f.SetCellValue(sheetName, "H1", "颜色")
	_ = f.SetCellValue(sheetName, "I1", "单位")
	_ = f.SetCellValue(sheetName, "J1", "期初库存")
	_ = f.SetCellValue(sheetName, "K1", "汇总入库数量")
	_ = f.SetCellValue(sheetName, "L1", "汇总出库数量")
	_ = f.SetCellValue(sheetName, "M1", "期末库存")
	_ = f.SetCellValue(sheetName, "N1", "现有库存")
	_ = f.SetCellValue(sheetName, "O1", "现有在途库存")
	_ = f.SetCellValue(sheetName, "P1", "系统结存")
	_ = f.SetCellValue(sheetName, "Q1", "实际盈亏")
	_ = f.SetCellValue(sheetName, "R1", "最终系统结存")

	startRow := 2

	for i, material := range materials {
		_ = f.SetCellValue(sheetName, fmt.Sprintf("A%d", startRow+i), i+1)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("B%d", startRow+i), material.Code)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("C%d", startRow+i), material.Name)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("D%d", startRow+i), material.Model)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("E%d", startRow+i), material.Size)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("F%d", startRow+i), material.Material)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("G%d", startRow+i), material.Process)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("H%d", startRow+i), material.CoverColor)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("I%d", startRow+i), material.Unit)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("J%d", startRow+i), material.InitialInventory)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("K%d", startRow+i), material.InboundTotal)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("L%d", startRow+i), material.OutboundTotal)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("M%d", startRow+i), material.FinalInventory)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("N%d", startRow+i), material.Inventory)
		_ = f.SetCellValue(sheetName, fmt.Sprintf("O%d", startRow+i), material.ExpectedInbound)
		//系统结存 物料系统结存：系统物料期初数量+入库数量+在途数量-出库数量
		closingBalance := material.InitialInventory + material.InboundTotal - material.OutboundTotal + material.ExpectedInbound
		_ = f.SetCellValue(sheetName, fmt.Sprintf("P%d", startRow+i), closingBalance)
		//实际盈亏 物料实际盈亏：期末库存-期初库存-BOM数量
		profitAndLoss := material.FinalInventory - material.InitialInventory - material.BomTotal
		_ = f.SetCellValue(sheetName, fmt.Sprintf("Q%d", startRow+i), profitAndLoss)
	}

	// 如果temp文件夹不存在， 在当前项目中创建临时文件夹
	dir := "temp/"

	fileName = fmt.Sprintf("物料出入库汇总_%s.xlsx", gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)
	filePath := gfile.Join(dir, fileName)

	// 不存则创建
	if !gfile.IsDir(dir) {
		_ = gfile.Mkdir(dir)
	}

	// 导出 Excel 文件
	if err = f.SaveAs(filePath); err != nil {
		g.Log().Error(ctx, err)
		return
	}

	// 读取 Excel 文件内容
	bytes = gfile.GetBytes(filePath)
	// 删除临时文件
	_ = gfile.Remove(filePath)
	// 返回字节流
	return
}

func (s *PmsMaterialService) ImportExcel(ctx context.Context, list []*model.PmsMaterial) error {
	if len(list) == 0 {
		return gerror.New("导入数据为空")
	}
	// 开启事务
	err := yc.DBM(s.Model).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := yc.DBM(s.Model).Ctx(ctx).OmitEmptyData().Insert(list)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (s *PmsMaterialService) QueryAllMaterial(ctx context.Context) (data *model.MaterialMap, err error) {
	data = &model.MaterialMap{}
	materialList := make([]*model.PmsMaterial, 0)
	materialIdMap := make(map[int64]*model.PmsMaterial)
	materialCodeMap := make(map[string]*model.PmsMaterial)
	err = yc.DBM(model.NewPmsMaterial()).Ctx(ctx).Scan(&materialList)
	if err != nil {
		return
	}
	for _, v := range materialList {
		materialIdMap[v.ID] = v
		materialCodeMap[v.Code] = v
	}
	data.MaterialIdMap = materialIdMap
	data.MaterialCodeMap = materialCodeMap
	data.MaterialList = materialList
	return
}

func (s *PmsMaterialService) GetAllMaterial(ctx context.Context, vo *model.PmsMaterialInboundVo) (data *model.MaterialDataVo, err error) {
	data = &model.MaterialDataVo{}
	materialList := make([]*model.PmsMaterial, 0)
	IdMap := make(map[int64]*model.PmsMaterial)
	CodeMap := make(map[string]*model.PmsMaterial)
	queryWrap := yc.DBM(model.NewPmsMaterial()).Ctx(ctx)
	if vo != nil && vo.MaterialIdList != nil && len(vo.MaterialIdList) > 0 {
		queryWrap = queryWrap.WhereIn("id", vo.MaterialIdList)
	}
	err = queryWrap.Scan(&materialList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return data, err
	}
	for _, item := range materialList {
		IdMap[item.ID] = item
		CodeMap[item.Code] = item
	}
	return data, nil
}

/*func (s *PmsMaterialService) GetMaterialDrawingVersion(ctx context.Context, materialId int64) (version string, err error) {
	// 获取物料信息
	var material *model.PmsMaterial
	err = yc.DBM(s.Model).Ctx(ctx).Where("id", materialId).Scan(&material)

	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return
	}

	if material == nil {
		return "", gerror.New("物料不存在")
	}

	// 获取物料的所有图纸信息
	var drawings []*model.PmsMaterialDrawing

	err = yc.DBM(model.NewPmsMaterialDrawing()).Ctx(ctx).
		Where("material_id", materialId).
		Where("status", MaterialStatusNormal).
		Scan(&drawings)

	if err != nil {
		return
	}

	// 从图纸中拼接版本信息 version = 3D图纸.2D图纸.工艺图纸
	var d3version, d2version, techVersion int

	for _, drawing := range drawings {
		// 从图纸信息中遍历出type=1的最大版本
		switch {
		case drawing.Type == 1:
			if drawing.Version > d3version {
				d3version = drawing.Version
			}
		case drawing.Type == 2:
			if drawing.Version > d2version {
				d2version = drawing.Version
			}
		case drawing.Type == 3:
			if drawing.Version > techVersion {
				techVersion = drawing.Version
			}
		}
	}

	version = fmt.Sprintf("%d.%d.%d", d3version, d2version, techVersion)

	return
}*/

func addQueryByCodesCondition(ctx context.Context, builder *gdb.WhereBuilder) *gdb.WhereBuilder {
	// 获取物料编码
	r := g.RequestFromCtx(ctx)
	codes := r.Get("codeList")
	if codes != nil {
		codesString := codes.String()
		if codesString != "" {
			// 逗号分割
			codeArr := strings.Split(codesString, ",")
			// 遍历codeArr，去除空格
			for i, code := range codeArr {
				codeArr[i] = strings.TrimSpace(code)
			}
			// 添加条件
			builder = builder.WhereIn("code", codeArr)
		} else {
			builder = builder.Where("1=2")
		}
	}
	return builder
}

func addMaterialPageQueryCondition(ctx context.Context, builder *gdb.WhereBuilder) *gdb.WhereBuilder {
	r := g.RequestFromCtx(ctx)

	// 过滤0库存
	hasStock := r.Get("hasStock").Bool()
	if hasStock {
		builder = builder.WhereGT("inventory", 0)
	}

	// isShowUnused 仅显示未使用的物料
	isShowUnused := r.Get("isShowUnused").Bool()
	if isShowUnused {
		subQuery := g.Model(model.NewPmsBomMaterial().TableName()).Distinct().Fields("material_id")
		builder = builder.WhereNotIn("id", subQuery)
	}

	return builder
}

func NewPmsMaterialService() *PmsMaterialService {
	return &PmsMaterialService{
		&yc.Service{
			Model: model.NewPmsMaterial(),
			UniqueKey: map[string]string{
				"code": "物料编码必须唯一",
			},
			PageQueryOp: &yc.QueryOp{
				FieldEQ:      []string{"code", "name"},
				KeyWordField: []string{"code", "name", "model"},
				// 添加额外的查询条件
				Where: addMaterialPageQueryCondition,
			},
			ListQueryOp: &yc.QueryOp{
				FieldEQ:      []string{"code", "name"},
				KeyWordField: []string{"code", "name", "model"},
				Where:        addQueryByCodesCondition,
			},
		},
	}
}
