import{s as m,e as z}from"./index-BuqCFB-b.js";import{_ as N}from"./UploadBtn.vue_vue_type_script_setup_true_name_UploadBtn_lang-9IHOAomH.js";import{c as h,b as k,f as p,y as a,J as q,t as i,h as _,v as $,i as v,w as u,j as g,E as f,o as l,S as H,q as B,F,s as V,W as R,B as C,ae as W,e as J,V as P}from"./.pnpm-Kv7TmmH8.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";const G={class:"data-summary"},K={class:"header"},O={flex:"~ items-center"},Q=h({name:"undefined"}),X=h({...Q,props:{title:{}},setup(b){const c=b,d=k(!1);async function y(){d.value=!0;try{const n=await m.pms.allDataCompare.request({url:"/exportSummaryData",method:"get",responseType:"blob"});z(n,`${c.title}汇总数据.xlsx`)&&f.success("导出成功")}catch(n){console.error("导出汇总数据时出错:",n),f.error("导出失败")}finally{d.value=!1}}return(n,x)=>{const D=v("el-button");return l(),p("div",G,[a("div",K,[a("h2",null,i(n.title),1),a("div",O,[_(N,{api:$(m).pms.dataDiff,url:"mergeMaterialData",label:"上传物料数据汇总",mr:"10px",download:!0},null,8,["api"]),_(N,{api:$(m).pms.dataDiff,url:"uploadDataDiff",label:"上传数据合并",mr:"10px",download:!0},null,8,["api"]),_(D,{type:"primary",loading:d.value,onClick:y},{default:u(()=>[g(" 导出汇总数据 ")]),_:1},8,["loading"])])]),q(n.$slots,"default",{},void 0,!0)])}}}),Y=E(X,[["__scopeId","data-v-edc02399"]]),Z={flex:"~ row nowrap justify-between",class:"w-full"},ee={class:"card-header"},te={key:0,class:"mb-4 flex items-center overflow-hidden rounded-t-md bg-gray-100 shadow-sm"},ae=a("div",{class:"mr-3 h-8 w-1 bg-blue-500"},null,-1),se={class:"py-2 text-lg text-gray-700 font-semibold"},oe={class:"flex items-center justify-between p-4"},le={class:"mt-2 flex items-center"},ne={key:0,class:"ml-2 text-xs"},re={class:"mr-1 text-gray-7"},ie=a("span",{class:"mr-1 text-gray-500"}," 最后上传文件 ",-1),ce={class:"mr-1 text-gray-7"},de=a("span",{class:"mr-1 text-gray-500"},"于",-1),ue={class:"text-gray-7"},pe=h({name:"PurchaseDetails"}),_e=h({...pe,setup(b){const c=k(null),d=k([]),y={0:"成品",1:"原料"};function n(s){let e=0;for(let o=0;o<s.length;o++)e=s.charCodeAt(o)+((e<<5)-e);return["success","warning","danger"][Math.abs(e)%3]}async function x(){const s=await m.pms.allDataCompare.getHeaderColumns();d.value=s}function D(s){const e=c.value;e&&(e.setAttribute("data-column-id",s),e.click())}async function A(s){const e=s.target;if(e&&e.files&&e.files.length>0){const r=e.getAttribute("data-column-id"),o=e.files[0];await L(r,o)}}async function L(s,e){if(!s){f.error("列ID不存在");return}const r=new FormData;r.append("file",e),r.append("columnId",s);try{await m.pms.allDataCompare.uploadColumnData(r),f.success("上传成功"),await x()}catch(o){console.error("上传文件时出错:",o),f.error("上传文件时出错")}finally{c.value&&(c.value.value="")}}async function M(s){m.pms.allDataCompare.request({url:"/downloadLatestColumnData",method:"get",params:{columnId:s.id},responseType:"blob"}).then(e=>{z(e)&&f.success("下载功")}).catch(()=>{f.error("下载失败")})}return H(x),(s,e)=>{const r=v("el-button"),o=v("el-tag"),U=v("el-card");return l(),B(Y,{title:"模版数据汇总"},{default:u(()=>[a("div",Z,[(l(!0),p(F,null,V(d.value,(I,T)=>(l(),p("div",{key:T,class:"mb-10 w-1/2 px-2"},[_(U,{flex:"~ col nowrap",class:"h-full"},{header:u(()=>[a("div",ee,[a("span",null,i(y[T])+" 相关数据",1)])]),default:u(()=>[(l(!0),p(F,null,V(I,(t,w)=>{var S;return l(),p("div",{key:t.id,class:R(["w-full border border-gray-300 border-gray-400 rounded",{"bg-gray-50":w%2===0}])},[w===0||t.blockTitle!==I[w-1].blockTitle?(l(),p("div",te,[ae,a("div",se,i(t.blockTitle),1)])):C("",!0),a("div",oe,[a("div",null,[_(r,{class:"block text-left",onClick:j=>D(t.id)},{default:u(()=>[g(" 上传【"+i(t.name)+"】数据 ",1)]),_:2},1032,["onClick"]),a("div",le,[_(o,{class:"mr-2",type:n(t.handler)},{default:u(()=>[g(i(t.handler),1)]),_:2},1032,["type"]),_(o,{type:t.latestData?"success":"danger"},{default:u(()=>[g(i(t.latestData?"有数据":"无数据"),1)]),_:2},1032,["type"]),t!=null&&t.latestData?(l(),p("div",ne,[a("span",re,i(t.latestData.uploaderName||"未知"),1),ie,a("span",ce,i(((S=t.latestData.uploadInfo)==null?void 0:S.fileName)||"未知文件"),1),de,a("span",ue,i(new Date(t.latestData.createdAt).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).replace(/\//g,"-")),1)])):C("",!0)])]),t.latestData?(l(),B(r,{key:0,type:"success",size:"small",class:"transition-colors duration-300 ease-in-out hover:bg-green-600",onClick:j=>M(t)},{default:u(()=>[g(" 下载数据 ")]),_:2},1032,["onClick"])):C("",!0)])],2)}),128))]),_:2},1024)]))),128))]),a("input",{ref_key:"fileInputRef",ref:c,type:"file",class:"hidden",accept:".xlsx, .xls",onChange:A},null,544)]),_:1})}}}),fe=h({name:"undefined"}),me=h({...fe,setup(b){const{height:c}=W(),d=J(()=>c.value-240);return(y,n)=>(l(),p("div",{style:P(`height:${$(d)}px`)},[_(_e)],4))}}),be=E(me,[["__scopeId","data-v-e18438e1"]]);export{be as default};
