package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/container/gset"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/modules/pms/utils"
	"github.com/imhuso/lookah-erp/admin/yc"
	"github.com/xuri/excelize/v2"
	"math"
	"net/url"
	"path"
	"strings"
)

type PmsPurchaseContractService struct {
	*yc.Service
}

type InsertContractDTO struct {
	No string `json:"no"`
}

type PO struct {
	Po string `json:"po"`
}

// UpdateContractStatus 批量更新合同付款状态
func (s *PmsPurchaseContractService) UpdateContractStatus(ctx context.Context, ids []int64, payment_status string) (data interface{}, err error) {
	if len(ids) == 0 {
		return nil, gerror.New("合同ID不能为空")
	}
	if payment_status == "" {
		return nil, gerror.New("合同状态不能为空")
	}
	// 更新出库单
	update, err := yc.DBM(s.Model).Ctx(ctx).WhereIn("id", ids).UpdateAndGetAffected(g.Map{
		"payment_status": payment_status,
	})
	if err != nil || update == 0 {
		err = gerror.New("更新合同状态失败:" + gconv.String(update) + "条数据." + gconv.String(err))
		return nil, err
	}
	return update, nil
}

// GetContractListByPoAndSupplierId 查询未完成的PO列表
func (s *PmsPurchaseContractService) GetContractListByPoAndSupplierId(ctx context.Context, po string, supplier_id int64) (res []*model.Contract, err error) {
	if po == "" || supplier_id <= 0 {
		return nil, errors.New("PO号和供应商ID不能为空")
	}
	contract := make([]*model.Contract, 0)
	err = yc.DBM(s.Model).Ctx(ctx).
		With(&model.PmsPurchaseOrderContractMaterialOutput{}).
		Where("po", po).
		Where("supplier_id", supplier_id).
		WhereNot("expected_quantity", 0).
		Scan(&contract)
	if err != nil {
		return nil, err
	}
	return contract, nil
}

// GetUnfinishedPo 查询未完成的PO列表
func (s *PmsPurchaseContractService) GetUnfinishedPo(ctx context.Context, supplier_id int64) (res []*PO, err error) {
	poList := make([]*PO, 0)
	err = yc.DBM(s.Model).Ctx(ctx).
		Where("supplier_id", supplier_id).
		WhereNot("expected_quantity", 0).
		Group("po").
		Scan(&poList)
	if err != nil {
		return nil, err
	}
	return poList, nil
}

func (s *PmsPurchaseContractService) QueryAllContract(ctx context.Context, _ *model.QueryVo) (res *model.PmsPurchaseContractMap, err error) {
	res = &model.PmsPurchaseContractMap{}
	res.IdMap = make(map[int64]*model.PmsPurchaseContract)
	res.PoMap = make(map[string]*model.PmsPurchaseContract)
	purchaseContractList := make([]*model.PmsPurchaseContract, 0)
	// 查询采购订单
	err = yc.DBM(s.Model).Ctx(ctx).Scan(&purchaseContractList)
	if err != nil {
		return nil, err
	}
	for i := range purchaseContractList {
		item := purchaseContractList[i]
		res.IdMap[item.ID] = item
		res.PoMap[item.Po] = item
	}
	return res, nil
}

// InboundOrOutboundByContractId  试算入库或出库，库存的变化
// contractId 合同ID
// materialId 物料ID
// quantity 物料变化数量
// isInbound 是否是入库
// isUpdateRelateData 是否更新关联数据

// 处理导入采购合同数据转类型
func (s *PmsPurchaseContractService) ImportContractExcelData(ctx context.Context, _ *model.QueryVo) (err error) {
	uploadFile := utils.GetUploadFile(ctx, "")
	err = utils.ValidateExcelFile(uploadFile)
	if err != nil {
		return err
	}
	filesuffix := path.Ext(uploadFile.Filename) // 文件类型
	if filesuffix != ".xlsx" && filesuffix != ".xls" {
		return errors.New("文件类型错误")
	}
	open, err := uploadFile.Open()
	if err != nil {
		return errors.New("打开文件失败")
	}
	purchaseContractData, err := utils.ReadExcelFileList(open, 0)
	if err != nil {
		return err
	}
	if len(purchaseContractData) == 0 {
		return errors.New("文件为空")
	}
	allMaterial, err := NewPmsMaterialService().QueryAllMaterial(ctx)
	if err != nil {
		return err
	}
	// 更新物料在途数量 key = 物料编码_Po
	//updateMaterailExpectedMap := make(map[string]*model.PmsMaterialStockLog)
	insterPurchaseContract := make([]*model.PmsPurchaseContract, 0)  // 正常在途
	insterPurchaseContractA := make([]*model.PmsPurchaseContract, 0) // 不良退货在途
	codeMap := allMaterial.MaterialCodeMap
	for i, item := range purchaseContractData[0].Rows {
		if i == 0 {
			continue
		}
		poType := gconv.String(item[20]) // 采购类型 空白  不良在途
		tmp := &model.PmsPurchaseContract{
			//Quantity:         gconv.Float64(item[9]),            // 采购数量
			Quantity:         gconv.Float64(item[19]),           // 采购数量
			Po:               gstr.ToUpper(gstr.Trim(item[11])), // 采购单号
			ReceivedQuantity: 0,                                 // 已收数量
			ExpectedQuantity: gconv.Float64(item[19]),           // 在途数量
			PaymentTerm:      0,                                 // 0 月付
			UnitPrice:        gconv.Float64(item[13]),
			//Subtotal:         gconv.Float64(item[14]),
			Subtotal:     0,
			TaxRate:      13.0,
			VirtualOrder: 1,
			Remark:       "2024年1~6月采购在途打包下单",
		}
		code := gstr.ToUpper(gstr.Trim(item[0])) // 物料编码
		materialItem, ok := codeMap[code]
		if !ok {
			return gerror.New("物料编码不存在" + fmt.Sprintf("第%d行", i+1))
		}
		supplierCn := gstr.ToUpper(gstr.Trim(item[10])) // 供应商
		tmp.MaterialId = materialItem.ID
		tmp.Code = materialItem.Code
		if supplierCn == "" {
			return gerror.New("供应商不能为空" + fmt.Sprintf("第%d行", i+1))
		}
		supplierMap, err := NewPmsSupplierService().QueryAll(ctx)
		if err != nil {
			return err
		}
		nameMap := supplierMap.NameMap
		supplierItem, ok := nameMap[supplierCn]
		if !ok {
			return gerror.New("供应商不存在" + fmt.Sprintf("第%d行", i+1))
		}
		tmp.SupplierId = supplierItem.ID

		deliveryDate := gconv.String(item[12]) // 交货日期
		if len(deliveryDate) == 0 {
			return gerror.New("交货日期不能为空" + fmt.Sprintf("第%d行", i+1))
		}
		tmp.DeliveryDate = gconv.GTime(deliveryDate)
		if len(poType) == 0 {
			if tmp.ExpectedQuantity > 0 {
				insterPurchaseContract = append(insterPurchaseContract, tmp)
			}
		} else {
			tmp.Remark = gconv.String(item[20])
			tmp.Po = "LYKJP202401-0831-退货在途"
			tmp.DeliveryDate = gtime.Now()
			if tmp.ExpectedQuantity > 0 {
				insterPurchaseContractA = append(insterPurchaseContractA, tmp)
			}
		}
	}
	if len(insterPurchaseContract) > 0 {
		listMap := make(map[int64][]*model.PmsPurchaseContract)
		materialMap := make(map[int64]*model.PmsPurchaseContract)
		materialCodeMap := make(map[string]*model.PmsPurchaseContract)
		for _, item := range insterPurchaseContract {
			materialStock := &model.MaterialStock{
				MaterialId:      item.MaterialId,
				OperationType:   -model.OperationTypePurchaseOrder,
				ExpectedInbound: -item.ExpectedQuantity,
			}
			err = NewPmsMaterialService().updateMaterialStockV2(ctx, materialStock)
			if err != nil {
				return err
			}
			_, ok := materialMap[item.MaterialId]
			if !ok {
				materialMap[item.MaterialId] = item
				materialCodeMap[item.Code] = item
			} else {
				_, ok = listMap[item.MaterialId]
				if !ok {
					listMap[item.MaterialId] = make([]*model.PmsPurchaseContract, 0)
				}
				listMap[item.MaterialId] = append(listMap[item.MaterialId], item)
			}
		}

		dataList := make([]*model.PmsPurchaseContract, 0)
		for _, v := range materialMap {
			dataList = append(dataList, v)
		}
		if len(dataList) > 0 {
			dto := InsertContractDTO{No: "LY-SG202401-083102"}
			err := s.InsertContractBatch(ctx, dataList, dto)
			if err != nil {
				return err
			}
		}
		count := 3
		// 这个8 我知道提前知道的 所有写死
		for i := 0; i < 8; i++ {
			repeatList := make([]*model.PmsPurchaseContract, 0)
			for _, value := range listMap {
				if len(value) > i {
					repeatList = append(repeatList, value[i])
				}
			}
			no := fmt.Sprintf("LY-SG202401-08310%d", count)
			dto := InsertContractDTO{No: no}
			err := s.InsertContractBatch(ctx, repeatList, dto)
			if err != nil {
				return err
			}
			count++
		}
	}
	if len(insterPurchaseContractA) > 0 {
		insterPurchaseContractMap := make(map[int64]*model.PmsPurchaseContract)
		for _, item := range insterPurchaseContractA {
			materialStock := &model.MaterialStock{
				MaterialId:      item.MaterialId,
				OperationType:   -model.OperationTypePurchaseOrder,
				ExpectedInbound: -item.ExpectedQuantity,
			}
			err = NewPmsMaterialService().updateMaterialStockV2(ctx, materialStock)
			if err != nil {
				return err
			}
			_, ok := insterPurchaseContractMap[item.MaterialId]
			if !ok {
				insterPurchaseContractMap[item.MaterialId] = item
			} else {
				insterPurchaseContractMap[item.MaterialId].Quantity += item.Quantity
				insterPurchaseContractMap[item.MaterialId].ExpectedQuantity += item.ExpectedQuantity
			}
		}
		dataList := make([]*model.PmsPurchaseContract, 0) // 不良退货在途
		for _, v := range insterPurchaseContractMap {
			dataList = append(dataList, v)
		}
		dto := InsertContractDTO{No: "LY-SG202401-083101"}
		err := s.InsertContractBatch(ctx, dataList, dto)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *PmsPurchaseContractService) InsertContractBatch(ctx context.Context, list []*model.PmsPurchaseContract, insertContractDTO InsertContractDTO) (err error) {
	// 创建采购订单
	purchaseOrder := model.NewPmsPurchaseOrder()
	purchaseOrder.OrderNo = insertContractDTO.No
	purchaseOrder.Status = 2
	purchaseOrder.Step = 5
	purchaseOrderId, err := yc.DBM(purchaseOrder).Ctx(ctx).Data(purchaseOrder).InsertAndGetId()
	if err != nil {
		return err
	}
	purchaseOrder.ID = purchaseOrderId
	// 创建生产采购需求
	order := model.NewPmsProductionPurchaseOrder()
	order.PurchaseId = purchaseOrderId
	order.OrderId = 0
	order.Status = 2
	_, err = yc.DBM(order).Ctx(ctx).Data(order).Insert()
	if err != nil {
		return err
	}
	for i := range list {
		item := list[i]
		item.PurchaseId = purchaseOrderId
		bomContent := "{\"id\": 0, \"status\": 0, \"product\": null, \"version\": 0, \"changeLog\": null, \"isWarning\": false, \"materials\": null, \"productId\": 0, \"lastUpdateTime\": null, \"materialBundles\": null}"
		detail := model.PmsPurchaseOrderDetail{PurchaseId: purchaseOrderId, GenerateId: item.MaterialId, GenerateQuantity: item.ExpectedQuantity, BomContent: bomContent}
		detail.GenerateType = 0
		detail.BomVersion = 0
		_, err = yc.DBM(&detail).Ctx(ctx).Data(&detail).Insert()
		if err != nil {
			return err
		}
		contractId, err := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Data(item).InsertAndGetId()
		if err != nil {
			return err
		}
		detailExtra := model.PmsPurchaseOrderDetailExtra{
			PurchaseId:        purchaseOrderId,
			MaterialId:        item.MaterialId,
			PurchaseTotal:     item.ExpectedQuantity,
			Total:             item.ExpectedQuantity,
			AvailableQuantity: item.ExpectedQuantity,
			ContractId:        contractId,
		}
		_, err = yc.DBM(&detailExtra).Ctx(ctx).Data(&detailExtra).Insert()
		if err != nil {
			return err
		}
		materialStock := &model.MaterialStock{
			MaterialId:      item.MaterialId,
			OperationType:   model.OperationTypePurchaseOrder,
			ExpectedInbound: item.ExpectedQuantity,
		}
		err = NewPmsMaterialService().updateMaterialStockV2(ctx, materialStock)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *PmsPurchaseContractService) InboundOrOutboundByContractId(ctx context.Context, contractId int64, materialId int64, quantity float64, isInbound bool, isUpdateRelateData bool) (ms *model.MaterialStock, err error) {
	if contractId == 0 {
		return nil, gerror.New("合同ID不能为空")
	}

	if materialId == 0 {
		return nil, gerror.New("物料ID不能为空")
	}

	if quantity <= 0 {
		return nil, gerror.New("数量不能为空")
	}

	if isInbound {
		//return s.getInboundStockChangeByContract(ctx, contractId, materialId, quantity, isUpdateRelateData)
		return s.getInboundStockChangeByContractV2(ctx, contractId, materialId, quantity, isUpdateRelateData)
	}
	// return s.getOutboundStockChangeByContract(ctx, contractId, materialId, quantity, isUpdateRelateData)
	return s.getOutboundStockChangeByContractV2(ctx, contractId, materialId, quantity, isUpdateRelateData)
}

func (s *PmsPurchaseContractService) getInboundStockChangeByContract(ctx context.Context, contractId int64, materialId int64, quantity float64, isUpdateRelateData bool) (ms *model.MaterialStock, err error) {
	contract := &model.PmsPurchaseContract{}
	subContract := &model.PmsPurchaseContract{}

	err = yc.DBM(model.NewPmsPurchaseContract()).Where("id", contractId).Scan(&contract)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	purchaseOrderId := contract.PurchaseId
	if purchaseOrderId == 0 {
		return nil, errors.New("合同不存在")
	}

	// 获取采购单
	order := &model.PmsPurchaseOrder{}
	err = yc.DBM(model.NewPmsPurchaseOrder()).Where("id", purchaseOrderId).Scan(&order)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	isSubOrder := false
	if order.ParentOrderId > 0 {
		isSubOrder = true
	}

	rootPurchaseOrderId := purchaseOrderId
	// 如果是子订单，则获取根订单ID
	if isSubOrder {
		// 修改关联的合同为根订单的合同
		subContract = contract

		// 获取父采购订单
		rootPurchaseOrderId = order.ParentOrderId
		contract = &model.PmsPurchaseContract{}
		err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Where("purchase_id", rootPurchaseOrderId).Where("material_id", materialId).Scan(&contract)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return nil, err
		}

		if contract.ID == 0 {
			return nil, gerror.New("采购订单不存在")
		}
	}

	// 1. 更新采购单合同信息
	originExpectedQuantity := contract.ExpectedQuantity
	originReceivedQuantity := contract.ReceivedQuantity
	// 不需要减去转单数量
	maxReceivedQuantity := contract.Quantity

	newReceivedQuantity := contract.ReceivedQuantity + quantity
	newExpectedQuantity := contract.ExpectedQuantity - quantity

	// 如果总收货数量大于订单数量，则报错
	if newReceivedQuantity > maxReceivedQuantity+MaterialInboundThreshold {
		return nil, gerror.New("收货数量不能大于订单数量")
	}
	if newExpectedQuantity+MaterialInboundThreshold < 0 {
		return nil, gerror.New("收货数量不能大于可收货数量")
	}

	newExpectedQuantity = math.Max(newExpectedQuantity, 0)

	// 如果是子订单
	if isUpdateRelateData {
		if isSubOrder {
			// 获取原合同
			subContractNewReceivedQuantity := subContract.ReceivedQuantity + quantity
			subContractNewExpectedQuantity := subContract.ExpectedQuantity - quantity
			if subContractNewExpectedQuantity < 0 {
				subContractNewExpectedQuantity = 0
			}

			affected, err2 := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).
				Where("id", subContract.ID).
				Where("purchase_id", subContract.PurchaseId).
				Where("material_id", materialId).
				Where("received_quantity", subContract.ReceivedQuantity).
				Where("expected_quantity", subContract.ExpectedQuantity).
				Data(g.Map{
					"received_quantity": subContractNewReceivedQuantity,
					"expected_quantity": subContractNewExpectedQuantity,
				}).
				UpdateAndGetAffected()

			if err2 != nil {
				return nil, err2
			}

			if affected == 0 {
				return nil, gerror.New("更新订单合同收货数量失败")
			}
		}

		// 更新父订单的合同
		affected, err2 := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).
			Where("id", contract.ID).
			Where("purchase_id", contract.PurchaseId).
			Where("material_id", materialId).
			Where("received_quantity", originReceivedQuantity).
			Where("expected_quantity", originExpectedQuantity).
			Data(g.Map{
				"received_quantity": newReceivedQuantity,
				"expected_quantity": newExpectedQuantity,
			}).
			UpdateAndGetAffected()

		if err2 != nil {
			return nil, err2
		}

		if affected == 0 {
			g.Log().Errorf(ctx, "更新订单合同收货数量失败, purchase_id: %d, material_id: %d, received_quantity: %f, newReceivedQuantity: %f，expected_quantity: %f, newExpectedQuantity: %f", contract.PurchaseId, materialId, contract.ReceivedQuantity, newReceivedQuantity, contract.ExpectedQuantity, newExpectedQuantity)
			return nil, gerror.New("更新订单合同收货数量失败")
		}
	}

	// 2. 更新采购单扩展信息
	orderDetailExtra := &model.PmsPurchaseOrderDetailExtra{}
	err = yc.DBM(model.NewPmsPurchaseOrderDetailExtra()).Ctx(ctx).Where("purchase_id", rootPurchaseOrderId).Where("material_id", materialId).Scan(&orderDetailExtra)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	if orderDetailExtra == nil {
		return nil, gerror.New("采购单的信息获取失败")
	}

	originAvailableQuantity := orderDetailExtra.AvailableQuantity
	originInboundQuantity := orderDetailExtra.InboundQuantity
	newInboundQuantity := orderDetailExtra.InboundQuantity + quantity

	// 计算超出了采购数量的部分 [有可能是否赠品]
	materialContractTotal := contract.Quantity
	splitCount, err2 := yc.DBM(model.NewPmsPurchaseOrderDetailSplit()).Ctx(ctx).Where("purchase_id", order.ID).Where("material_id", materialId).Count()
	if err2 != nil {
		return nil, err2
	}

	if splitCount > 0 {
		sum, err3 := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Where("purchase_id", order.ID).Where("material_id", materialId).Sum("quantity")
		if err3 != nil {
			return nil, err3
		}

		materialContractTotal = gconv.Float64(sum)
	}

	exceedInboundQuantity := newInboundQuantity - materialContractTotal
	// 如果是拆分订单，则可以超出单个物料被拆分的N倍数
	// 1. 查询拆分订单数量
	splitCount = splitCount + 1
	if exceedInboundQuantity > gconv.Float64(splitCount*MaterialInboundThreshold) {
		return nil, gerror.New("入库数量不能大于采购数量")
	}

	if exceedInboundQuantity < 0 {
		exceedInboundQuantity = 0
	}

	// 最大可出库数量
	maxAvailableQuantity := orderDetailExtra.PurchaseTotal
	newAvailableQuantity := newInboundQuantity + orderDetailExtra.DeductInventory
	if newAvailableQuantity >= orderDetailExtra.PurchaseTotal {
		newAvailableQuantity = maxAvailableQuantity
	}

	if isUpdateRelateData {
		odeAffected, err2 := yc.DBM(model.NewPmsPurchaseOrderDetailExtra()).Ctx(ctx).
			Data(
				g.Map{
					"available_quantity": newAvailableQuantity,
					"inbound_quantity":   newInboundQuantity,
				},
			).
			Where("purchase_id", rootPurchaseOrderId).
			Where("material_id", materialId).
			Where("available_quantity", originAvailableQuantity).
			Where("inbound_quantity", originInboundQuantity).
			UpdateAndGetAffected()

		if err2 != nil {
			return nil, err2
		}

		if odeAffected == 0 {
			return nil, gerror.New("更新采购单的可用数量失败")
		}
	}

	var stock, expectedInbound, occupiedInventory, deductibleExpectedInbound, usedExpectedInbound, maxProductPurchaseQuantity, preparedQuantity float64

	// 1. 采购需求数量
	// 最大采购需求入库数量，即入到这里的会到订单占用中
	maxProductPurchaseQuantity = orderDetailExtra.Total - originInboundQuantity
	if maxProductPurchaseQuantity < 0 {
		maxProductPurchaseQuantity = 0
	}

	if order.OrderType == int(model.PurchaseOrderTypeSelfSelect) {
		maxProductPurchaseQuantity = 0
	}

	// 2. 备货数量，即超出采购需求的部分不包括赠品
	preparedQuantity = quantity - maxProductPurchaseQuantity - exceedInboundQuantity
	if preparedQuantity < 0 {
		preparedQuantity = 0
	}

	// 3. exceedInboundQuantity 赠品部分
	// 实际本次入库的数量
	totalInbound := quantity - exceedInboundQuantity
	if totalInbound < 0 {
		totalInbound = 0
	}

	material, err1 := s.getOrSetMaterialWitchCache(ctx, materialId)
	if err1 != nil {
		return nil, err1
	}
	// 4. 计算库存变化
	// 在途库存
	expectedInbound = -totalInbound
	// 占用库存 等于实际入库数量
	occupiedInventory = totalInbound
	// 如果超过了采购需求数量，则只增加不超过的部分
	if preparedQuantity > 0 {
		occupiedInventory = maxProductPurchaseQuantity
	}

	// 如果物料存在不可用在途，则优先填充不可用在途
	materialUsedExpectedInbound := material.UsedExpectedInbound
	// 已用在途
	if preparedQuantity+exceedInboundQuantity > materialUsedExpectedInbound {
		// 物料可用在途
		materialDeductibleExpectedInbound := material.DeductibleExpectedInbound
		// 物料可用库存
		stock = preparedQuantity + exceedInboundQuantity - materialUsedExpectedInbound
		usedExpectedInbound = -materialUsedExpectedInbound

		deductibleExpectedInbound = preparedQuantity
		/*if materialUsedExpectedInbound == 0 {
			deductibleExpectedInbound = preparedQuantity
		} else {
			deductibleExpectedInbound = preparedQuantity - stock
		}*/

		if deductibleExpectedInbound > materialDeductibleExpectedInbound {
			deductibleExpectedInbound = -materialDeductibleExpectedInbound
		} else {
			deductibleExpectedInbound = -deductibleExpectedInbound
		}
	} else {
		stock = 0
		usedExpectedInbound = -preparedQuantity
		deductibleExpectedInbound = -preparedQuantity
	}

	stockChange := &model.MaterialStock{
		MaterialId:                materialId,
		Stock:                     stock,
		LockedStock:               0,
		ExpectedInbound:           expectedInbound,
		OccupiedInventory:         occupiedInventory,
		DeductibleExpectedInbound: deductibleExpectedInbound,
		UsedExpectedInbound:       usedExpectedInbound,
	}

	// 将更新后的物料信息存入缓存
	material.Inventory = material.Inventory + stock
	material.ExpectedInbound = material.ExpectedInbound + expectedInbound
	material.OccupiedInventory = material.OccupiedInventory + occupiedInventory
	material.DeductibleExpectedInbound = material.DeductibleExpectedInbound + deductibleExpectedInbound
	material.UsedExpectedInbound = material.UsedExpectedInbound + usedExpectedInbound

	// 将物料信息更新到Redis中
	err3 := s.setMaterialCache(ctx, material)
	if err3 != nil {
		return nil, err3
	}

	return stockChange, nil
}

func (s *PmsPurchaseContractService) getInboundStockChangeByContractV2(ctx context.Context, contractId int64, materialId int64, quantity float64, isUpdateRelateData bool) (ms *model.MaterialStock, err error) {
	contract := &model.PmsPurchaseContract{}
	subContract := &model.PmsPurchaseContract{}

	err = yc.DBM(model.NewPmsPurchaseContract()).Where("id", contractId).Scan(&contract)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	purchaseOrderId := contract.PurchaseId
	if purchaseOrderId == 0 {
		return nil, errors.New("合同不存在")
	}

	// 获取采购单
	order := &model.PmsPurchaseOrder{}
	err = yc.DBM(model.NewPmsPurchaseOrder()).Where("id", purchaseOrderId).Scan(&order)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	isSubOrder := false
	if order.ParentOrderId > 0 {
		isSubOrder = true
	}

	rootPurchaseOrderId := purchaseOrderId
	// 如果是子订单，则获取根订单ID
	if isSubOrder {
		// 修改关联的合同为根订单的合同
		subContract = contract

		// 获取父采购订单
		rootPurchaseOrderId = order.ParentOrderId
		contract = &model.PmsPurchaseContract{}
		err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Where("purchase_id", rootPurchaseOrderId).Where("material_id", materialId).Scan(&contract)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return nil, err
		}

		if contract.ID == 0 {
			return nil, gerror.New("采购订单不存在")
		}
	}

	// 1. 更新采购单合同信息
	originExpectedQuantity := contract.ExpectedQuantity
	originReceivedQuantity := contract.ReceivedQuantity
	// 不需要减去转单数量
	maxReceivedQuantity := contract.Quantity

	newReceivedQuantity := contract.ReceivedQuantity + quantity
	newExpectedQuantity := contract.ExpectedQuantity - quantity

	// 如果总收货数量大于订单数量，则报错
	if newReceivedQuantity > maxReceivedQuantity+MaterialInboundThreshold {
		return nil, gerror.New("收货数量不能大于订单数量")
	}
	if newExpectedQuantity+MaterialInboundThreshold < 0 {
		return nil, gerror.New("收货数量不能大于可收货数量")
	}

	newExpectedQuantity = math.Max(newExpectedQuantity, 0)

	// 如果是子订单
	if isUpdateRelateData {
		if isSubOrder {
			// 获取原合同
			subContractNewReceivedQuantity := subContract.ReceivedQuantity + quantity
			subContractNewExpectedQuantity := subContract.ExpectedQuantity - quantity
			if subContractNewExpectedQuantity < 0 {
				subContractNewExpectedQuantity = 0
			}

			affected, err2 := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).
				Where("id", subContract.ID).
				Where("purchase_id", subContract.PurchaseId).
				Where("material_id", materialId).
				Where("received_quantity", subContract.ReceivedQuantity).
				Where("expected_quantity", subContract.ExpectedQuantity).
				Data(g.Map{
					"received_quantity": subContractNewReceivedQuantity,
					"expected_quantity": subContractNewExpectedQuantity,
				}).
				UpdateAndGetAffected()

			if err2 != nil {
				return nil, err2
			}

			if affected == 0 {
				return nil, gerror.New("更新订单合同收货数量失败")
			}
		}

		// 更新父订单的合同
		affected, err2 := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).
			Where("id", contract.ID).
			Where("purchase_id", contract.PurchaseId).
			Where("material_id", materialId).
			Where("received_quantity", originReceivedQuantity).
			Where("expected_quantity", originExpectedQuantity).
			Data(g.Map{
				"received_quantity": newReceivedQuantity,
				"expected_quantity": newExpectedQuantity,
			}).
			UpdateAndGetAffected()

		if err2 != nil {
			return nil, err2
		}

		if affected == 0 {
			g.Log().Errorf(ctx, "更新订单合同收货数量失败, purchase_id: %d, material_id: %d, received_quantity: %f, newReceivedQuantity: %f，expected_quantity: %f, newExpectedQuantity: %f", contract.PurchaseId, materialId, contract.ReceivedQuantity, newReceivedQuantity, contract.ExpectedQuantity, newExpectedQuantity)
			return nil, gerror.New("更新订单合同收货数量失败")
		}
	}

	// 2. 更新采购单扩展信息
	orderDetailExtra := &model.PmsPurchaseOrderDetailExtra{}
	err = yc.DBM(model.NewPmsPurchaseOrderDetailExtra()).Ctx(ctx).Where("purchase_id", rootPurchaseOrderId).Where("material_id", materialId).Scan(&orderDetailExtra)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	if orderDetailExtra == nil {
		return nil, gerror.New("采购单的信息获取失败")
	}

	originAvailableQuantity := orderDetailExtra.AvailableQuantity
	originInboundQuantity := orderDetailExtra.InboundQuantity
	newInboundQuantity := orderDetailExtra.InboundQuantity + quantity

	// 计算超出了采购数量的部分 [有可能是否赠品]
	materialContractTotal := contract.Quantity
	splitCount, err2 := yc.DBM(model.NewPmsPurchaseOrderDetailSplit()).Ctx(ctx).Where("purchase_id", order.ID).Where("material_id", materialId).Count()
	if err2 != nil {
		return nil, err2
	}

	if splitCount > 0 {
		sum, err3 := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Where("purchase_id", order.ID).Where("material_id", materialId).Sum("quantity")
		if err3 != nil {
			return nil, err3
		}

		materialContractTotal = gconv.Float64(sum)
	}

	exceedInboundQuantity := newInboundQuantity - materialContractTotal
	// 如果是拆分订单，则可以超出单个物料被拆分的N倍数
	// 1. 查询拆分订单数量
	splitCount = splitCount + 1
	if exceedInboundQuantity > gconv.Float64(splitCount*MaterialInboundThreshold) {
		return nil, gerror.New("入库数量不能大于采购数量")
	}

	if exceedInboundQuantity < 0 {
		exceedInboundQuantity = 0
	}

	// 最大可出库数量
	maxAvailableQuantity := orderDetailExtra.PurchaseTotal
	newAvailableQuantity := newInboundQuantity + orderDetailExtra.DeductInventory
	if newAvailableQuantity >= orderDetailExtra.PurchaseTotal {
		newAvailableQuantity = maxAvailableQuantity
	}

	if isUpdateRelateData {
		odeAffected, err2 := yc.DBM(model.NewPmsPurchaseOrderDetailExtra()).Ctx(ctx).
			Data(
				g.Map{
					"available_quantity": newAvailableQuantity,
					"inbound_quantity":   newInboundQuantity,
				},
			).
			Where("purchase_id", rootPurchaseOrderId).
			Where("material_id", materialId).
			Where("available_quantity", originAvailableQuantity).
			Where("inbound_quantity", originInboundQuantity).
			UpdateAndGetAffected()

		if err2 != nil {
			return nil, err2
		}

		if odeAffected == 0 {
			return nil, gerror.New("更新采购单的可用数量失败")
		}
	}

	// 处理总库存变化 pms_material
	material, err1 := s.getOrSetMaterialWitchCache(ctx, materialId)
	if err1 != nil {
		return nil, err1
	}

	// 将更新后的物料信息存入缓存
	material.Inventory = material.Inventory + quantity
	material.ExpectedInbound = material.ExpectedInbound - quantity
	if material.ExpectedInbound < 0 {
		material.ExpectedInbound = 0
	}

	stockChange := &model.MaterialStock{
		MaterialId:   materialId,
		Stock:        quantity,
		UsedQuantity: 0,
	}

	// 将物料信息更新到Redis中
	err3 := s.setMaterialCache(ctx, material)
	if err3 != nil {
		return nil, err3
	}

	return stockChange, nil
}

func (s *PmsPurchaseContractService) getOutboundStockChangeByContract(ctx context.Context, contractId int64, materialId int64, quantity float64, isUpdateRelateData bool) (ms *model.MaterialStock, err error) {
	contract := &model.PmsPurchaseContract{}
	subContract := &model.PmsPurchaseContract{}

	err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Where("id", contractId).Scan(&contract)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	purchaseOrderId := contract.PurchaseId
	if purchaseOrderId == 0 {
		return nil, gerror.New("合同不存在")
	}

	// 获取采购单
	order := *model.NewPmsPurchaseOrder()
	err = yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).Where("id", purchaseOrderId).Scan(&order)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	isSubOrder := false
	if order.ParentOrderId > 0 {
		isSubOrder = true
	}

	rootPurchaseOrderId := purchaseOrderId
	// 如果是子订单，则获取根订单ID
	if isSubOrder {
		// 修改关联的合同为根订单的合同
		subContract = contract

		// 获取父采购订单
		rootPurchaseOrderId = order.ParentOrderId
		contract = &model.PmsPurchaseContract{}
		err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Where("purchase_id", rootPurchaseOrderId).Where("material_id", materialId).Scan(&contract)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return nil, err
		}

		if contract.ID == 0 {
			return nil, gerror.New("采购订单不存在")
		}
	}

	// 1. 更新采购单合同信息
	originReceivedQuantity := contract.ReceivedQuantity
	newReceivedQuantity := originReceivedQuantity - quantity

	if newReceivedQuantity < 0 {
		return nil, errors.New("退货数量不能大于已收货数量")
	}

	originExpectedQuantity := contract.ExpectedQuantity
	newExpectedQuantity := originExpectedQuantity + quantity
	if newExpectedQuantity < 0 {
		newExpectedQuantity = 0
	}

	if newExpectedQuantity > contract.Quantity {
		newExpectedQuantity = contract.Quantity
	}

	if isUpdateRelateData {
		// 如果有父订单，则更新父订单的合同
		if isSubOrder {
			subContractNewReceivedQuantity := subContract.ReceivedQuantity - quantity
			if subContractNewReceivedQuantity < 0 {
				subContractNewReceivedQuantity = 0
			}

			subContractNewExpectedQuantity := subContract.ExpectedQuantity + quantity
			if subContractNewExpectedQuantity > subContract.Quantity {
				subContractNewExpectedQuantity = subContract.Quantity
			}

			affected, err2 := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).
				Where("id", subContract.ID).
				Where("purchase_id", subContract.PurchaseId).
				Where("material_id", materialId).
				Where("received_quantity", subContract.ReceivedQuantity).
				Where("expected_quantity", subContract.ExpectedQuantity).
				Data(g.Map{
					"received_quantity": subContractNewReceivedQuantity,
					"expected_quantity": subContractNewExpectedQuantity,
				}).
				UpdateAndGetAffected()

			if err2 != nil {
				return nil, err2
			}

			if affected == 0 {
				return nil, gerror.New("更新订单合同收货数量失败")
			}
		}

		// 更新父订单的合同
		affected, err2 := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).
			Where("id", contract.ID).
			Where("purchase_id", contract.PurchaseId).
			Data(gdb.Map{
				"received_quantity": newReceivedQuantity,
				"expected_quantity": newExpectedQuantity,
			}).
			Where("received_quantity", contract.ReceivedQuantity).
			Where("expected_quantity", contract.ExpectedQuantity).
			UpdateAndGetAffected()

		if err2 != nil {
			return nil, err2
		}

		if affected == 0 {
			return nil, errors.New("更新已收货数量失败")
		}
	}

	// 2. 更新采购单扩展信息更新采购单信息
	orderDetailExtra := &model.PmsPurchaseOrderDetailExtra{}
	err = yc.DBM(model.NewPmsPurchaseOrderDetailExtra()).Ctx(ctx).Where("purchase_id", rootPurchaseOrderId).Where("material_id", contract.MaterialId).Scan(&orderDetailExtra)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	if orderDetailExtra == nil {
		return nil, gerror.New("采购单的信息获取失败")
	}

	// TODO 拆分、转单考虑
	originInboundQuantity := orderDetailExtra.InboundQuantity

	newInboundQuantity := originInboundQuantity - quantity
	if newInboundQuantity < 0 {
		return nil, gerror.New("退货数量不能大于已收货数量")
	}

	originAvailableQuantity := orderDetailExtra.AvailableQuantity

	// 最大可出库数量
	maxAvailableQuantity := orderDetailExtra.PurchaseTotal
	newAvailableQuantity := newInboundQuantity + orderDetailExtra.DeductInventory

	// 最小可出库数量
	if newAvailableQuantity > maxAvailableQuantity {
		newAvailableQuantity = maxAvailableQuantity
	}

	if isUpdateRelateData {
		odeAffected, err2 := yc.DBM(model.NewPmsPurchaseOrderDetailExtra()).Ctx(ctx).
			Data(
				g.Map{
					"available_quantity": newAvailableQuantity,
					"inbound_quantity":   newInboundQuantity,
				},
			).
			Where("purchase_id", rootPurchaseOrderId).
			Where("material_id", contract.MaterialId).
			Where("available_quantity", originAvailableQuantity).
			Where("inbound_quantity", originInboundQuantity).
			UpdateAndGetAffected()

		if err2 != nil {
			return nil, err2
		}

		if odeAffected == 0 {
			return nil, gerror.New("更新采购单扩展信息失败")
		}
	}

	// 3. 更新库存
	var stock, occupiedInventory, deductibleExpectedInbound, usedExpectedInbound, productionPurchase, expectedInbound, exceedQuantity float64
	// 超出合同的数量
	maxExceedQuantity := originReceivedQuantity - contract.Quantity
	if maxExceedQuantity < 0 {
		maxExceedQuantity = 0
	}

	// 排除赠品后的入库数量， 无论如何都需要增加在途数量
	if quantity > maxExceedQuantity {
		exceedQuantity = maxExceedQuantity
	} else {
		exceedQuantity = quantity
	}

	expectedInbound = quantity - exceedQuantity
	if expectedInbound < 0 {
		expectedInbound = 0
	}

	material, err1 := s.getOrSetMaterialWitchCache(ctx, materialId)
	if err1 != nil {
		return nil, err1
	}

	materialStock := material.Inventory

	// 1. 赠品数量 exceedQuantity

	// 2. 超出采购单的部分
	maxOccupiedInventory := orderDetailExtra.Total
	maxPurchaseQuantity := originInboundQuantity - maxExceedQuantity - maxOccupiedInventory
	if maxPurchaseQuantity < 0 {
		maxPurchaseQuantity = 0
	}

	exceedPurchaseQuantity := expectedInbound

	if order.OrderType == int(model.PurchaseOrderTypeGenerated) {
		if expectedInbound > maxPurchaseQuantity {
			exceedPurchaseQuantity = maxPurchaseQuantity
		}

		// 3. 超出了采购需求的部分
		productionPurchase = expectedInbound - exceedPurchaseQuantity
		if productionPurchase < 0 {
			productionPurchase = 0
		}
	}

	// 根据物料当前的库存，决定超出采购需求的部分如何抵扣
	stock = exceedQuantity + exceedPurchaseQuantity

	// 超过库存数量
	exceedStock := 0.0000
	// 计算超过了库存的数量
	if materialStock < stock {
		// 这里的顺序不能错
		exceedStock = stock - materialStock
		stock = materialStock
	}

	// 可用在途 【用可用在途抵扣库存】
	deductibleExpectedInbound = exceedPurchaseQuantity
	// 占用库存
	occupiedInventory = -productionPurchase
	// 已用在途
	usedExpectedInbound = exceedStock
	// 在库库存
	stock = -stock
	// 将更新后的物料信息存入缓存
	material.Inventory = material.Inventory + stock
	material.ExpectedInbound = material.ExpectedInbound + expectedInbound
	material.OccupiedInventory = material.OccupiedInventory + occupiedInventory
	material.DeductibleExpectedInbound = material.DeductibleExpectedInbound + deductibleExpectedInbound
	material.UsedExpectedInbound = material.UsedExpectedInbound + usedExpectedInbound

	// 将物料信息更新到Redis中
	err3 := s.setMaterialCache(ctx, material)
	if err3 != nil {
		return nil, err3
	}

	ms = &model.MaterialStock{
		MaterialId:                materialId,
		Stock:                     stock,
		ExpectedInbound:           expectedInbound,
		OccupiedInventory:         occupiedInventory,
		DeductibleExpectedInbound: deductibleExpectedInbound,
		UsedExpectedInbound:       usedExpectedInbound,
	}

	return
}

func (s *PmsPurchaseContractService) getOutboundStockChangeByContractV2(ctx context.Context, contractId int64, materialId int64, quantity float64, isUpdateRelateData bool) (ms *model.MaterialStock, err error) {
	contract := &model.PmsPurchaseContract{}
	subContract := &model.PmsPurchaseContract{}

	err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Where("id", contractId).Scan(&contract)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	purchaseOrderId := contract.PurchaseId
	if purchaseOrderId == 0 {
		return nil, gerror.New("合同不存在")
	}

	// 获取采购单
	order := *model.NewPmsPurchaseOrder()
	err = yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).Where("id", purchaseOrderId).Scan(&order)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	isSubOrder := false
	if order.ParentOrderId > 0 {
		isSubOrder = true
	}

	rootPurchaseOrderId := purchaseOrderId
	// 如果是子订单，则获取根订单ID
	if isSubOrder {
		// 修改关联的合同为根订单的合同
		subContract = contract

		// 获取父采购订单
		rootPurchaseOrderId = order.ParentOrderId
		contract = &model.PmsPurchaseContract{}
		err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Where("purchase_id", rootPurchaseOrderId).Where("material_id", materialId).Scan(&contract)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return nil, err
		}

		if contract.ID == 0 {
			return nil, gerror.New("采购订单不存在")
		}
	}

	// 1. 更新采购单合同信息
	originReceivedQuantity := contract.ReceivedQuantity
	newReceivedQuantity := originReceivedQuantity - quantity

	if newReceivedQuantity < 0 {
		return nil, errors.New("退货数量不能大于已收货数量")
	}

	originExpectedQuantity := contract.ExpectedQuantity
	newExpectedQuantity := originExpectedQuantity + quantity
	if newExpectedQuantity < 0 {
		newExpectedQuantity = 0
	}

	if newExpectedQuantity > contract.Quantity {
		newExpectedQuantity = contract.Quantity
	}

	if isUpdateRelateData {
		// 如果有父订单，则更新父订单的合同
		if isSubOrder {
			subContractNewReceivedQuantity := subContract.ReceivedQuantity - quantity
			if subContractNewReceivedQuantity < 0 {
				subContractNewReceivedQuantity = 0
			}

			subContractNewExpectedQuantity := subContract.ExpectedQuantity + quantity
			if subContractNewExpectedQuantity > subContract.Quantity {
				subContractNewExpectedQuantity = subContract.Quantity
			}

			affected, err2 := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).
				Where("id", subContract.ID).
				Where("purchase_id", subContract.PurchaseId).
				Where("material_id", materialId).
				Where("received_quantity", subContract.ReceivedQuantity).
				Where("expected_quantity", subContract.ExpectedQuantity).
				Data(g.Map{
					"received_quantity": subContractNewReceivedQuantity,
					"expected_quantity": subContractNewExpectedQuantity,
				}).
				UpdateAndGetAffected()

			if err2 != nil {
				return nil, err2
			}

			if affected == 0 {
				return nil, gerror.New("更新订单合同收货数量失败")
			}
		}

		// 更新父订单的合同
		affected, err2 := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).
			Where("id", contract.ID).
			Where("purchase_id", contract.PurchaseId).
			Data(gdb.Map{
				"received_quantity": newReceivedQuantity,
				"expected_quantity": newExpectedQuantity,
			}).
			Where("received_quantity", contract.ReceivedQuantity).
			Where("expected_quantity", contract.ExpectedQuantity).
			UpdateAndGetAffected()

		if err2 != nil {
			return nil, err2
		}

		if affected == 0 {
			return nil, errors.New("更新已收货数量失败")
		}
	}

	// 2. 更新采购单扩展信息更新采购单信息
	orderDetailExtra := &model.PmsPurchaseOrderDetailExtra{}
	err = yc.DBM(model.NewPmsPurchaseOrderDetailExtra()).Ctx(ctx).Where("purchase_id", rootPurchaseOrderId).Where("material_id", contract.MaterialId).Scan(&orderDetailExtra)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	if orderDetailExtra == nil {
		return nil, gerror.New("采购单的信息获取失败")
	}

	// TODO 拆分、转单考虑
	originInboundQuantity := orderDetailExtra.InboundQuantity

	newInboundQuantity := originInboundQuantity - quantity
	if newInboundQuantity < 0 {
		return nil, gerror.New("退货数量不能大于已收货数量")
	}

	originAvailableQuantity := orderDetailExtra.AvailableQuantity

	// 最大可出库数量
	maxAvailableQuantity := orderDetailExtra.PurchaseTotal
	newAvailableQuantity := newInboundQuantity + orderDetailExtra.DeductInventory

	// 最小可出库数量
	if newAvailableQuantity > maxAvailableQuantity {
		newAvailableQuantity = maxAvailableQuantity
	}

	if isUpdateRelateData {
		odeAffected, err2 := yc.DBM(model.NewPmsPurchaseOrderDetailExtra()).Ctx(ctx).
			Data(
				g.Map{
					"available_quantity": newAvailableQuantity,
					"inbound_quantity":   newInboundQuantity,
				},
			).
			Where("purchase_id", rootPurchaseOrderId).
			Where("material_id", contract.MaterialId).
			Where("available_quantity", originAvailableQuantity).
			Where("inbound_quantity", originInboundQuantity).
			UpdateAndGetAffected()

		if err2 != nil {
			return nil, err2
		}

		if odeAffected == 0 {
			return nil, gerror.New("更新采购单扩展信息失败")
		}
	}

	material, err1 := s.getOrSetMaterialWitchCache(ctx, materialId)
	if err1 != nil {
		return nil, err1
	}

	// 在库库存
	stock := -quantity
	// 将更新后的物料信息存入缓存
	material.Inventory = material.Inventory + stock

	// 将物料信息更新到Redis中
	err3 := s.setMaterialCache(ctx, material)
	if err3 != nil {
		return nil, err3
	}

	ms = &model.MaterialStock{
		MaterialId: materialId,
		Stock:      stock,
	}

	return ms, nil
}

func (s *PmsPurchaseContractService) UpdateReturnOrRevertV2(ctx context.Context, outbound *model.PmsMaterialOutbound, products []*model.PmsMaterialOutboundProduct, isRevert bool) (err error) {
	purchaseOrderId := outbound.OrderId
	materialStocks := make([]*model.MaterialStock, 0)

	purchaseOrder := &model.PmsPurchaseOrder{}
	err = yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).Where("id", purchaseOrderId).Scan(&purchaseOrder)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}

	if purchaseOrder.ID == 0 {
		return errors.New("采购订单不存在")
	}

	actionName := "退货"
	if isRevert {
		actionName = "撤销退货"
	}
	//
	contractIdList := make([]int64, 0)
	contractList := make([]*model.PmsPurchaseContract, 0)
	contractMap := make(map[int64]*model.PmsPurchaseContract)
	for _, product := range products {
		contractIdList = append(contractIdList, product.ContractId)
	}
	err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).WhereIn("id", contractIdList).Scan(&contractList)
	if err != nil {
		return err
	}
	for _, contract := range contractList {
		contractMap[contract.ID] = contract
	}
	// 遍历产品
	materialIdSet := gset.New()
	for _, product := range products {
		quantity := product.Quantity
		contractId := product.ContractId
		if !isRevert {
			cr := &model.PmsPurchaseContractReturn{}
			cr.ContractId = contractId
			cr.Quantity = quantity

			_, err = yc.DBM(model.NewPmsPurchaseContractReturn()).Ctx(ctx).Data(cr).Insert()

			if err != nil {
				return err
			}
		}

		var ms *model.MaterialStock
		var err2 error

		if isRevert {
			// 入库 撤销退货  +库存， -在途
			ms, err2 = s.InboundOrOutboundByContractId(ctx, contractId, product.MaterialId, quantity, true, true)
			if err2 != nil {
				return gerror.New(fmt.Sprintf("%s失败, 原因：%s", actionName, err2.Error()))
			}

			ms.Stock = quantity
			if outbound.Type == int(model.MaterialOutboundTypeReturn) {
				ms.ExpectedInbound = -quantity
			}
			ms.UsedQuantity = 0
			ms.OperationType = -StatusConvert(outbound.Type)
		} else {
			// 出库 -库存， +在途
			ms, err2 = s.InboundOrOutboundByContractId(ctx, contractId, product.MaterialId, quantity, false, true)
			ms.Stock = -quantity
			if outbound.Type == int(model.MaterialOutboundTypeReturn) {
				ms.ExpectedInbound = quantity
			}
			ms.OperationType = StatusConvert(outbound.Type)
		}
		if product.ContractId > 0 {
			contract := model.NewPmsPurchaseContract()
			err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Scan(&contract)
			if err != nil {
				return err
			}
			if contract.ID > 0 {
				ms.ContractId = product.ContractId
				orderList := make([]*model.PmsPurchaseOrder, 0)
				err = yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).Where("id", contract.PurchaseId).Scan(&orderList)
				if err != nil {
					return err
				}
				if len(orderList) > 0 {
					ms.PurchaseOrderSn = orderList[0].OrderNo
					ms.PurchaseOrderId = orderList[0].ID
				}
			}
		}
		ms.OutboundId = outbound.ID
		ms.OutboundSn = outbound.No
		ms.WorkOrderNo = outbound.WorkOrderNo
		ms.WorkOrderId = outbound.WorkOrderId

		if err2 != nil {
			return gerror.New(fmt.Sprintf("%s失败, 原因：%s", actionName, err2.Error()))
		}

		if ms == nil {
			return gerror.New("操作失败")
		}
		po := ""
		if contractMap[contractId] != nil {
			po = contractMap[contractId].Po
		}
		ms.Remark = fmt.Sprintf("订单%s, 合同PO: %s, 数量: %f", actionName, po, quantity)
		materialStocks = append(materialStocks, ms)

		materialIdSet.Add(product.MaterialId)
	}

	// 遍历materialIds，将缓存中的数据删除
	for _, materialId := range materialIdSet.Slice() {
		cacheKey := fmt.Sprintf("%s%d", MaterialInboundCacheKeyPrefix, materialId)
		_, err2 := yc.CacheManager.Remove(ctx, cacheKey)
		if err2 != nil {
			return err2
		}
	}

	// 更新库存
	err = NewPmsMaterialService().UpdateMaterialStockBatchV2(ctx, materialStocks)
	if err != nil {
		g.Log().Error(ctx, "更新库存失败", err)
		return err
	}

	// 检查并处理订单是否已完成
	err = NewPmsPurchaseOrderService().CheckAndSetOrderStatus(ctx, purchaseOrderId)
	if err != nil {
		return err
	}

	return nil
}

// UpdateReturnOrRevert 更新退货或者撤销
func (s *PmsPurchaseContractService) UpdateReturnOrRevert(ctx context.Context, outbound *model.PmsMaterialOutbound, products []*model.PmsMaterialOutboundProduct, isRevert bool) (err error) {
	purchaseOrderId := outbound.OrderId
	materialStocks := make([]*model.MaterialStock, 0)

	purchaseOrder := &model.PmsPurchaseOrder{}
	err = yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).Where("id", purchaseOrderId).Scan(&purchaseOrder)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}

	if purchaseOrder.ID == 0 {
		return errors.New("采购订单不存在")
	}

	actionName := "退货"
	if isRevert {
		actionName = "撤销退货"
	}

	// 开启事务
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 遍历产品
		materialIdSet := gset.New()
		for _, product := range products {
			quantity := product.Quantity
			contractId := product.ContractId
			if !isRevert {
				cr := &model.PmsPurchaseContractReturn{}
				cr.ContractId = contractId
				cr.Quantity = quantity

				_, err = yc.DBM(model.NewPmsPurchaseContractReturn()).Ctx(ctx).Data(cr).Insert()

				if err != nil {
					return err
				}
			}

			var ms *model.MaterialStock
			var err2 error

			if isRevert {
				ms, err2 = s.InboundOrOutboundByContractId(ctx, contractId, product.MaterialId, quantity, true, true)
			} else {
				ms, err2 = s.InboundOrOutboundByContractId(ctx, contractId, product.MaterialId, quantity, false, true)
			}

			if err2 != nil {
				return gerror.New(fmt.Sprintf("%s失败, 原因：%s", actionName, err2.Error()))
			}

			if ms == nil {
				return gerror.New("操作失败")
			}

			ms.Remark = fmt.Sprintf("订单%s, 合同ID: %d, 数量: %f", actionName, contractId, quantity)
			materialStocks = append(materialStocks, ms)

			materialIdSet.Add(product.MaterialId)
		}

		// 遍历materialIds，将缓存中的数据删除
		for _, materialId := range materialIdSet.Slice() {
			cacheKey := fmt.Sprintf("%s%d", MaterialInboundCacheKeyPrefix, materialId)
			_, err2 := yc.CacheManager.Remove(ctx, cacheKey)
			if err2 != nil {
				return err2
			}
		}

		// 更新库存
		err = NewPmsMaterialService().UpdateMaterialStocks(ctx, materialStocks)
		if err != nil {
			g.Log().Error(ctx, "更新库存失败", err)
			return err
		}

		// 检查并处理订单是否已完成
		err = NewPmsPurchaseOrderService().CheckAndSetOrderStatus(ctx, purchaseOrderId)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return err
	}

	return nil
}

// getOrSetMaterialWitchCache 从Redis中查找物料信息并缓存
func (s *PmsPurchaseContractService) getOrSetMaterialWitchCache(ctx context.Context, materialId int64) (material *model.PmsMaterial, err error) {
	// 将物料存入Redis缓存，解决当同一个物料操作多次时，查询到的数据不是最新的问题
	// 查询是否有之前使用过的在途库存
	material = model.NewPmsMaterial()
	cacheKey := fmt.Sprintf("%s%d", MaterialInboundCacheKeyPrefix, materialId)
	// 先从缓存中取数据
	materialCache, err3 := yc.CacheManager.Get(ctx, cacheKey)
	if err3 != nil {
		return
	}

	// 从缓存中取出数据，如果没有则从数据库中取
	if !materialCache.IsEmpty() {
		err1 := materialCache.Scan(&material)
		if err1 != nil {
			return
		}
	} else {
		err4 := yc.DBM(model.NewPmsMaterial()).Ctx(ctx).Where("id", materialId).Scan(&material)
		if err4 != nil && !errors.Is(err4, sql.ErrNoRows) {
			return
		}
		// 将数据存入缓存
		err4 = yc.CacheManager.Set(ctx, cacheKey, material, 0)
		//err4 = yc.CacheManager.SetIfNotExistFuncLock(ctx, cacheKey, material, 0) // todo 应该用这个方法
		if err4 != nil {
			return
		}
	}

	if material == nil || material.ID == 0 {
		return nil, gerror.New("物料不存在")
	}
	return material, nil
}

// setMaterialCache 将物料信息更新到Redis中
func (s *PmsPurchaseContractService) setMaterialCache(ctx context.Context, material *model.PmsMaterial) error {
	cacheKey := fmt.Sprintf("%s%d", MaterialInboundCacheKeyPrefix, material.ID)
	err := yc.CacheManager.Set(ctx, cacheKey, material, 0)
	if err != nil {
		return err
	}
	return nil
}

// 垃圾代码记得删掉 物料编号差异导出
func (s *PmsPurchaseContractService) ExportMaterialCodeDiff(_ context.Context, file *excelize.File, list []*model.PmsMaterialOutput, _ *model.QueryVo, _ map[string]*model.PmsMaterial) {
	sheetName := "物料编号差异明细"
	_, _ = file.NewSheet(sheetName)
	_ = file.SetCellValue(sheetName, "A1", "序号")
	_ = file.SetCellValue(sheetName, "B1", "物料编码")
	_ = file.SetCellValue(sheetName, "C1", "物料名称")
	_ = file.SetCellValue(sheetName, "D1", "数量")
	_ = file.SetCellValue(sheetName, "E1", "PO")
	_ = file.SetCellValue(sheetName, "F1", "备注")
	row := 2
	for i, item := range list {
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), i+1)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.Code)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), "")
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.Quantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.Po)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", row), item.Remark)

		row++
	}
}

type PoDiffVo struct {
	// 系统有上传数据没有的PO列表
	SysNotExistList []*model.PmsPurchaseOrderContractOutput `json:"sysNotExistList"`
	// 上传数据有系统没有的PO列表
	UploadNotExistList []*model.PmsPurchaseOrderContractOutput `json:"uploadNotExistList"`
}

func (s *PmsPurchaseContractService) QueryPage(ctx context.Context, reqVo *model.QueryVo) (result model.PageResult[*model.PmsPurchaseOrderContractOutput], err error) {
	result.List = make([]*model.PmsPurchaseOrderContractOutput, 0)
	result.Pagination = &model.Pagination{Page: reqVo.Page, Size: reqVo.Size}
	queryWrap := `		
		SELECT #{Fields} FROM pms_purchase_contract AS main 
		    LEFT JOIN pms_purchase_order AS t1 ON main.purchase_id = t1.id
		    LEFT JOIN pms_material AS t2 ON main.material_id = t2.id
		    LEFT JOIN pms_supplier AS t3 ON main.supplier_id = t3.id
		    LEFT JOIN
		    (
				SELECT main.purchase_id, t1.sn  FROM
				pms_production_purchase_order AS main
				LEFT JOIN pms_production_schedule AS t1 ON main.order_id = t1.id
				WHERE main.deleteTime IS NULL AND t1.sn IS NOT NULL
			) AS pro ON main.purchase_id = pro.purchase_id
		 WHERE t1.deleteTime IS NULL
	`
	// 1 是虚拟订单
	if reqVo.VirtualOrder == "0" {
		queryWrap += " AND main.virtual_order = 0"
	}
	if reqVo.Date != "" {
		split := gstr.Split(reqVo.Date, ",")
		queryWrap += fmt.Sprintf(` AND DATE_FORMAT(t1.createTime,'%%Y-%%m-%%d') BETWEEN '%s' AND '%s'`, split[0], split[1])
	}
	if reqVo.FilterV1 {
		queryWrap += fmt.Sprintf(` AND DATE_FORMAT(t1.createTime,'%%Y-%%m-%%d') > %s`, utils.V1_DISABLE_DATE)
	}
	reqVo.KeyWord = gstr.Trim(reqVo.KeyWord)
	if reqVo.KeyWord != "" {
		// 物料编码、物料名称、PO、订单号、供应商名称、SN
		queryWrap += fmt.Sprintf(" AND (t2.code LIKE '%%%s%%' OR t2.name LIKE '%%%s%%' OR main.po LIKE '%%%s%%' OR t1.order_no LIKE '%%%s%%' OR t3.name LIKE '%%%s%%' OR pro.sn LIKE '%%%s%%')",
			reqVo.KeyWord, reqVo.KeyWord, reqVo.KeyWord, reqVo.KeyWord, reqVo.KeyWord, reqVo.KeyWord)
	}
	if reqVo.Status != 0 {
		if reqVo.Status == -2 { //  -2 查询在途
			queryWrap += fmt.Sprintf(`AND t1.status IN(%d,%d)`, PurchaseOrderStatusConfirmed, PurchaseOrderStatusInbound)
			queryWrap += fmt.Sprintf(`AND main.expected_quantity > 0`)

		} else {
			status := reqVo.Status
			if reqVo.Status == -1 { // -1是待处理,
				status = 0
			}
			queryWrap += fmt.Sprintf(" AND t1.status = %d", status)
		}
	}
	countQuery := gstr.Replace(queryWrap, "#{Fields}", "COUNT(1)")
	count, err := g.DB().Query(ctx, countQuery)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return result, err
	}
	result.Pagination.Total = gconv.Int64(count[0]["COUNT(1)"])
	// 查询列表
	reqVo.CalcOffset()
	queryWrap += fmt.Sprintf(" ORDER BY main.purchase_id DESC LIMIT %d,%d", reqVo.Page, reqVo.Size)
	queryWrap = gstr.Replace(queryWrap, "#{Fields}",
		fmt.Sprintf(`main.*,t1.order_no AS no,t1.createTime,t1.status AS OrderStatus,t1.parent_order_id,%s,t3.name AS SupName,pro.sn AS ProductionSN`, utils.MATERIAL_FIEIDS))
	queryResult, err := g.DB().Query(ctx, queryWrap)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return result, err
	}
	err = queryResult.Structs(&result.List)
	if len(result.List) > 0 {
		result.MapData = make(map[int64][]*model.PmsPurchaseOrderContractOutput)
		for i, item := range result.List {
			if item.OrderNo == "" {
				if item.ParentOrderId > 0 {
					parentOrder := model.NewPmsPurchaseOrder()
					err = yc.DBM(parentOrder).Ctx(ctx).Where("id", item.ParentOrderId).Scan(&parentOrder)
					if err != nil && !errors.Is(err, sql.ErrNoRows) {
						return result, err
					}
					purchaseOrder := model.NewPmsProductionPurchaseOrder()
					err = yc.DBM(purchaseOrder).Ctx(ctx).Where("purchase_id", parentOrder.ID).Scan(&purchaseOrder)
					if err != nil && !errors.Is(err, sql.ErrNoRows) {
						return result, err
					}
					schedule := model.NewPmsProductionSchedule()
					err = yc.DBM(schedule).Ctx(ctx).Where("id", purchaseOrder.OrderId).Scan(&schedule)
					if err != nil && !errors.Is(err, sql.ErrNoRows) {
						return result, err
					}
					result.List[i].OrderNo = parentOrder.OrderNo
					result.List[i].ProductionSN = schedule.Sn
				}
			}
			result.List[i].OrderStatusLabel = s.OrderStatusToLabel(item.OrderStatus)
			result.List[i].ExpectedQuantity = item.Quantity - item.ReceivedQuantity
			if !reqVo.ShowPrice {
				result.List[i].UnitPrice = 0
			}
			tmp := result.MapData[item.MaterialId]
			if tmp == nil || len(tmp) == 0 {
				tmp = make([]*model.PmsPurchaseOrderContractOutput, 0)
			}
			tmp = append(tmp, item)
			result.MapData[item.MaterialId] = tmp
		}
	}
	return result, nil
}

func (s *PmsPurchaseContractService) PurchaseOrderExportExcel(ctx context.Context, reqVo *model.QueryVo) error {
	reqVo.Page = 1
	reqVo.Size = utils.MAX_PAGE_SIZE
	result, err := s.QueryPage(ctx, reqVo)
	if err != nil {
		return err
	}
	if result.List == nil || len(result.List) == 0 {
		return errors.New("没有数据")
	}
	sheetName := "采购明细"
	file := excelize.NewFile()
	file.SetSheetName("Sheet1", sheetName)
	// 创建一个工作表
	_, _ = file.NewSheet(sheetName)
	columnHead := []string{"生产单号", "采购单号", "Po", "物料编码", "物料名称", "规格/型号", "单位", "供应商", "数量", "已收数量", "在途数量", "状态", "创建时间"}
	if reqVo.ShowPrice {
		// 倒数第三列，插入单价
		columnHead = append(columnHead[:len(columnHead)-2], append([]string{"单价"}, columnHead[len(columnHead)-2:]...)...)
	}
	err = utils.CreateExcelHead(file, sheetName, columnHead)
	if err != nil {
		return err
	}
	// 循环写入数据
	row := 2
	for _, item := range result.List {
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), item.ProductionSN)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.No)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), item.Po)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.Code)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.MaterialName)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", row), item.Model)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", row), item.Unit)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("H%d", row), item.SupName)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("I%d", row), item.Quantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", row), item.ReceivedQuantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", row), item.ExpectedQuantity)
		if reqVo.ShowPrice {
			_ = file.SetCellValue(sheetName, fmt.Sprintf("L%d", row), item.UnitPrice)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", row), item.OrderStatusLabel)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("N%d", row), item.CreateTime)
		} else {
			_ = file.SetCellValue(sheetName, fmt.Sprintf("L%d", row), item.OrderStatusLabel)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", row), item.CreateTime)
		}
		row++
	}
	sheetName = "物料汇总"
	file.NewSheet(sheetName)
	columnHead = []string{"物料编码", "物料名称", "规格/型号", "单位", "数量", "已收数量", "在途数量"}
	err = utils.CreateExcelHead(file, sheetName, columnHead)
	if err != nil {
		return err
	}
	row = 2
	for _, v := range result.MapData {
		tmp := &model.PmsPurchaseOrderContractOutput{}
		Quantity := 0.0
		ReceivedQuantity := 0.0
		ExpectedQuantity := 0.0
		if len(v) > 0 {
			tmp = v[0]
		}
		for _, item := range v {
			Quantity += item.Quantity
			ReceivedQuantity += item.ReceivedQuantity
			ExpectedQuantity += item.ExpectedQuantity
		}
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), tmp.Code)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), tmp.MaterialName)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), tmp.Model)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), tmp.Unit)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), Quantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", row), ReceivedQuantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", row), ExpectedQuantity)
		row++
	}
	// 写出到文件
	fileName := fmt.Sprintf("采购订单报表-%s.xlsx", gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)

	// 返回文件流
	buffer, err := file.WriteToBuffer()
	if err != nil {
		return err
	}

	utils.SetResponseToStream(ctx, fileName, buffer.Bytes())
	return nil
}

func (s *PmsPurchaseContractService) OrderStatusToLabel(status int) string {
	switch status {
	case int(PurchaseOrderStatusPending):
		return "待处理"
	case int(PurchaseOrderStatusAudit):
		return "待审核"
	case int(PurchaseOrderStatusConfirmed):
		return "已确认"
	case int(PurchaseOrderStatusInbound):
		return "入库中"
	case int(PurchaseOrderStatusCompleted):
		return "已完成"
	default:
		return ""
	}
}

func (s *PmsPurchaseContractService) ExportInboundProduct(_ context.Context, file *excelize.File, list []*model.PmsMaterialInboundProductVo, materialMap map[int64]*model.PmsMaterial, _ *model.QueryVo, orderMap map[int64]*model.PmsPurchaseOrderContractOutput) {
	TypeLabelMap := map[int]string{0: "自定义入库", 1: "采购单入库", 2: "库存调整", 3: "生产退料"}
	sheetName := "系统入库单明细"
	_, _ = file.NewSheet(sheetName)
	_ = file.SetCellValue(sheetName, "A1", "序号")
	_ = file.SetCellValue(sheetName, "B1", "物料编码")
	_ = file.SetCellValue(sheetName, "C1", "物料名称")
	_ = file.SetCellValue(sheetName, "D1", "PO")
	_ = file.SetCellValue(sheetName, "E1", "入库单号")
	_ = file.SetCellValue(sheetName, "F1", "入库类型")
	_ = file.SetCellValue(sheetName, "G1", "入库数量")
	_ = file.SetCellValue(sheetName, "H1", "创建日期")

	row := 2
	for i, item := range list {
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), i+1)
		if materialMap[item.MaterialId] == nil {
			_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), "")
			_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), "")
		} else {
			_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), materialMap[item.MaterialId].Code)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), materialMap[item.MaterialId].Name)
		}
		if orderMap[item.ContractId] == nil {
			_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), "")
		} else {
			_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), orderMap[item.ContractId].Po)
		}
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.No)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", row), TypeLabelMap[item.Type])
		_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", row), item.Quantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("H%d", row), item.CreateTime)
		row++
	}
}

func (s *PmsPurchaseContractService) ExportExpectedContrac(_ context.Context, file *excelize.File, list []*model.PmsPurchaseOrderContractOutput, materialMap map[int64]*model.PmsMaterial, _ *model.QueryVo) {
	sheetName3 := "系统采购单明细"
	_, _ = file.NewSheet(sheetName3)
	_ = file.SetCellValue(sheetName3, "A1", "序号")
	_ = file.SetCellValue(sheetName3, "B1", "物料编码")
	_ = file.SetCellValue(sheetName3, "C1", "物料名称")
	_ = file.SetCellValue(sheetName3, "D1", "PO")
	_ = file.SetCellValue(sheetName3, "E1", "订单号")
	_ = file.SetCellValue(sheetName3, "F1", "创建日期")
	_ = file.SetCellValue(sheetName3, "G1", "数量")
	_ = file.SetCellValue(sheetName3, "H1", "已收数量")
	_ = file.SetCellValue(sheetName3, "I1", "在途数量")
	_ = file.SetCellValue(sheetName3, "J1", "备注")
	//_ = file.SetCellValue(sheetName3, "I1", "状态")
	row := 2
	for i, item := range list {
		_ = file.SetCellValue(sheetName3, fmt.Sprintf("A%d", row), i+1)
		if materialMap[item.MaterialId] == nil {
			_ = file.SetCellValue(sheetName3, fmt.Sprintf("B%d", row), "")
			_ = file.SetCellValue(sheetName3, fmt.Sprintf("C%d", row), "")

		} else {
			_ = file.SetCellValue(sheetName3, fmt.Sprintf("B%d", row), materialMap[item.MaterialId].Code)
			_ = file.SetCellValue(sheetName3, fmt.Sprintf("C%d", row), materialMap[item.MaterialId].Name)
		}
		_ = file.SetCellValue(sheetName3, fmt.Sprintf("D%d", row), item.Po)
		_ = file.SetCellValue(sheetName3, fmt.Sprintf("E%d", row), item.No)
		_ = file.SetCellValue(sheetName3, fmt.Sprintf("F%d", row), item.CreateTime)
		_ = file.SetCellValue(sheetName3, fmt.Sprintf("G%d", row), item.Quantity)
		_ = file.SetCellValue(sheetName3, fmt.Sprintf("H%d", row), item.ReceivedQuantity)
		_ = file.SetCellValue(sheetName3, fmt.Sprintf("I%d", row), item.ExpectedQuantity)
		_ = file.SetCellValue(sheetName3, fmt.Sprintf("J%d", row), item.Remark)
		row++
	}
}

// todo 垃圾代码记得删掉 查询系统有差异的PO号
func (s *PmsPurchaseContractService) ImportOutboundExcel(ctx context.Context) error {
	r := g.RequestFromCtx(ctx)
	// 读取文件
	file := r.GetUploadFile("file")
	operationType := r.Get("operationType").String()
	if file == nil {
		return errors.New("上传文件不能为空")
	}
	/* 注意哦：上传文件大小受到ghttp.Server的ClientMaxBodySize配置影响：https://pkg.go.dev/github.com/gogf/gf/v2/net/ghttp#ServerConfig 默认支持的上传文件大小为8MB。 */
	/*if file.Size > maxSize {
		fmt.Println("上传文件不能超过1G")
	}*/
	//验证文件类型
	filesuffix := path.Ext(file.Filename) // 文件类型
	if filesuffix != ".xlsx" && filesuffix != ".xls" {
		return errors.New("文件类型错误")
	}
	open, err := file.Open()
	if err != nil {
		return errors.New("打开文件失败")
	}
	rows, _, err := utils.ReadExcelFile(open, "")
	if err != nil {
		return errors.New("打开文件第一个表格失败")
	}
	if len(rows) == 0 {
		return errors.New("文件内容为空")
	}
	if operationType == "1" {
		err = NewUploadDataDiffService().HandlePurchaseData(ctx, rows, file.Filename)
		if err != nil {
			return err
		}
		return nil
	}
	err = NewUploadDataDiffService().HandleOutboundExcel(ctx, rows, file.Filename)
	if err != nil {
		return err
	}
	return nil
}

// todo 垃圾代码记得删掉 查询系统有差异的PO号
func (s *PmsPurchaseContractService) QueryDiffPo(ctx context.Context, list []*model.PmsPurchaseContract, queryVo *model.QueryVo) (data *PoDiffVo, err error) {
	if len(list) == 0 {
		return nil, gerror.New("导入数据不能为空")
	}
	data = &PoDiffVo{}
	queryVo.VirtualOrder = "0"
	result, err := s.QueryPage(ctx, queryVo)
	if err != nil {
		return data, err
	}
	uploadPurchaseContractMap := make(map[string]*model.PmsPurchaseContract)
	for _, item := range list {
		uploadPurchaseContractMap[item.Po] = item
	}
	// 系统有上传数据没有的PO列表
	sysNotExistList := make([]*model.PmsPurchaseOrderContractOutput, 0)
	for i, item := range result.List {
		if uploadPurchaseContractMap[item.Po] == nil {
			for _, el := range list {
				if el.Code == item.Code && el.Quantity == item.Quantity {
					result.List[i].MatchedPo = el.Po
				}
			}
			sysNotExistList = append(sysNotExistList, item)
		}
	}
	data.SysNotExistList = sysNotExistList
	// 上传数据有系统没有的PO列表
	uploadNotExistList := make([]*model.PmsPurchaseOrderContractOutput, 0)
	resultMap := make(map[string]*model.PmsPurchaseOrderContractOutput)
	for _, item := range result.List {
		resultMap[item.Po] = item
	}
	for _, item := range list {
		if resultMap[item.Po] == nil {
			pmsPurchaseContract := &model.PmsPurchaseOrderContractOutput{}
			gconv.Struct(item, pmsPurchaseContract)
			for _, el := range result.List {
				if el.Code == item.Code && el.Quantity == item.Quantity {
					pmsPurchaseContract.MatchedPo = el.Po
				}
			}
			uploadNotExistList = append(uploadNotExistList, pmsPurchaseContract)
		}
	}
	data.UploadNotExistList = uploadNotExistList
	return data, nil
}

// todo 垃圾代码记得删掉 查询系统有差异的PO号
func (s *PmsPurchaseContractService) ExportVirtualOrder(_ context.Context, file *excelize.File, list []*model.PmsPurchaseContract, materialMap map[int64]*model.PmsMaterial, _ *model.QueryVo) {
	sheetName2 := "系统假采购单明细"
	_, _ = file.NewSheet(sheetName2)
	_ = file.SetCellValue(sheetName2, "A1", "物料编码")
	_ = file.SetCellValue(sheetName2, "B1", "物料名称")
	_ = file.SetCellValue(sheetName2, "C1", "PO")
	_ = file.SetCellValue(sheetName2, "D1", "数量")
	_ = file.SetCellValue(sheetName2, "E1", "已收数量")
	_ = file.SetCellValue(sheetName2, "F1", "在途数量")

	for i, el := range list {
		if materialMap[el.MaterialId] == nil {
			_ = file.SetCellValue(sheetName2, fmt.Sprintf("A%d", i+2), "")
			_ = file.SetCellValue(sheetName2, fmt.Sprintf("B%d", i+2), "")
		} else {
			_ = file.SetCellValue(sheetName2, fmt.Sprintf("A%d", i+2), materialMap[el.MaterialId].Code)
			_ = file.SetCellValue(sheetName2, fmt.Sprintf("B%d", i+2), materialMap[el.MaterialId].Name)
		}
		_ = file.SetCellValue(sheetName2, fmt.Sprintf("C%d", i+2), el.Po)
		_ = file.SetCellValue(sheetName2, fmt.Sprintf("D%d", i+2), el.Quantity)
		_ = file.SetCellValue(sheetName2, fmt.Sprintf("E%d", i+2), el.ReceivedQuantity)
		_ = file.SetCellValue(sheetName2, fmt.Sprintf("F%d", i+2), el.ExpectedQuantity)
	}
}

// todo 垃圾代码记得删掉
func (s *PmsPurchaseContractService) ImportExcel(ctx context.Context, queryVo *model.QueryVo) error {
	list := queryVo.PurchaseData
	if list == nil {
		return gerror.New("导入数据不能为空")
	}
	if queryVo.OperationType == 2 {
		err := s.UpdateVirtualOrder(ctx, queryVo)
		if err != nil {
			return err
		}
		return nil
	}
	if queryVo.OperationType == 3 {
		err := s.ExportMaterialData(ctx, queryVo)
		if err != nil {
			return err
		}
		return nil
	}
	pmsMaterialList := make([]*model.PmsMaterial, 0)
	pmsMaterialMap := make(map[int64]*model.PmsMaterial)
	err := yc.DBM(model.NewPmsMaterial()).Ctx(ctx).Scan(&pmsMaterialList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	for _, item := range pmsMaterialList {
		pmsMaterialMap[item.ID] = item
	}
	pmsPurchaseContractMap := make(map[string]*model.PmsPurchaseContract)
	for _, item := range list {
		pmsPurchaseContractMap[item.Po] = item
	}
	poList := make([]string, 0)
	for po := range pmsPurchaseContractMap {
		poList = append(poList, po)
	}

	pmsMaterialInboundProductList := make([]*model.PmsMaterialInboundProduct, 0)
	err = yc.DBM(model.NewPmsMaterialInboundProduct()).Ctx(ctx).Scan(&pmsMaterialInboundProductList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}

	errPoList := make([]*model.ErrPOTypeVo, 0)
	purchaseContractList := make([]*model.PmsPurchaseContract, 0)
	err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Scan(&purchaseContractList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	if len(purchaseContractList) > 0 {
		purchaseContractMap := make(map[string]*model.PmsPurchaseContract)
		for i, item := range purchaseContractList {
			purchaseContractMap[item.Po] = item
			if pmsMaterialMap[item.MaterialId] != nil {
				purchaseContractList[i].Code = pmsMaterialMap[item.MaterialId].Code
			}
		}
		for _, el := range list {
			if purchaseContractMap[el.Po] == nil {
				errVo := &model.ErrPOTypeVo{PO: el.Po, Quantity: el.Quantity, Code: el.Code}
				if len(errPoList) == 0 {
					errPoList = append(errPoList, errVo)
				} else {
					isExist := false
					for j, errPo := range errPoList {
						for _, contract := range purchaseContractList {
							if errPo.Quantity == contract.Quantity && errPo.Code == contract.Code {
								errPoList[j].MatchPO = contract.Po
								errPoList[j].MatchMaterialId = contract.MaterialId
								errPoList[j].MatchContractId = contract.ID
							}
						}

						if errPo.PO == el.Po {
							isExist = true
							break
						}
					}
					if !isExist {
						errPoList = append(errPoList, errVo)
					}
				}
			}
		}
		//if len(errPoList) > 0 {
		//	return gerror.New("不存在的PO号" + gconv.String(errPoList))
		//}
	}
	notPurchaseContractList := make([]*model.PmsPurchaseContract, 0)
	err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).WhereNotIn("po", poList).Where("virtual_order", 0).Scan(&notPurchaseContractList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	if len(notPurchaseContractList) > 0 {
		pmsPurchaseOrderIdsList := make([]int64, 0)
		updateContractIds := make([]int64, 0)
		for _, item := range notPurchaseContractList {
			if len(pmsPurchaseOrderIdsList) == 0 {
				pmsPurchaseOrderIdsList = append(pmsPurchaseOrderIdsList, item.PurchaseId)
			} else {
				isExist := false
				for _, id := range pmsPurchaseOrderIdsList {
					if id == item.PurchaseId {
						isExist = true
						break
					}
				}
				if !isExist {
					pmsPurchaseOrderIdsList = append(pmsPurchaseOrderIdsList, item.PurchaseId)
				}
			}
			if len(updateContractIds) == 0 {
				updateContractIds = append(updateContractIds, item.ID)
			} else {
				isExist := false
				for _, id := range updateContractIds {
					if id == item.ID {
						isExist = true
						break
					}
				}
				if !isExist {
					updateContractIds = append(updateContractIds, item.ID)
				}
			}
		}
	}

	result, err := s.QueryPage(ctx, queryVo)
	if err != nil {
		return err
	}
	sheetName := "Sheet1"
	file := excelize.NewFile()
	// 创建一个工作表
	_, _ = file.NewSheet(sheetName)
	_ = file.SetCellValue(sheetName, "A1", "序号")
	_ = file.SetCellValue(sheetName, "B1", "物料编码")
	_ = file.SetCellValue(sheetName, "C1", "PO")
	_ = file.SetCellValue(sheetName, "D1", "系统采购单号")
	_ = file.SetCellValue(sheetName, "E1", "创建时间")
	_ = file.SetCellValue(sheetName, "F1", "系统采购数量")
	_ = file.SetCellValue(sheetName, "G1", "系统已收数量")
	_ = file.SetCellValue(sheetName, "H1", "系统在途数量")
	_ = file.SetCellValue(sheetName, "I1", "导入采购数量")
	_ = file.SetCellValue(sheetName, "J1", "导入已收数量")
	_ = file.SetCellValue(sheetName, "K1", "导入在途数量")
	_ = file.SetCellValue(sheetName, "L1", "差异采购数量")
	_ = file.SetCellValue(sheetName, "M1", "差异已收数量")
	_ = file.SetCellValue(sheetName, "N1", "差异在途数量")
	// 循环写入数据
	row := 2
	for i, item := range result.List {
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), i+1)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.Code)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), item.Po)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.No)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.CreateTime)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", row), item.Quantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("I%d", row), item.ReceivedQuantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("H%d", row), item.ExpectedQuantity)
		for _, el := range list {
			if el.Po == item.Po && el.Code == item.Code {
				_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", row), el.Quantity)
				_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", row), el.ReceivedQuantity)
				_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", row), el.ExpectedQuantity)
				_ = file.SetCellValue(sheetName, fmt.Sprintf("L%d", row), el.Quantity-item.Quantity)
				_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", row), el.ReceivedQuantity-item.ReceivedQuantity)
				_ = file.SetCellValue(sheetName, fmt.Sprintf("N%d", row), el.ExpectedQuantity-item.ExpectedQuantity)
				_ = file.SetCellValue(sheetName, fmt.Sprintf("O%d", row), el.Remark)
			}
		}
		value, err := file.GetCellValue(sheetName, fmt.Sprintf("F%d", row))
		if value == "" || err != nil {
			_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", row), 0)
		}
		row++
	}

	if len(errPoList) > 0 {
		sheetName2 := "Sheet2"
		_, _ = file.NewSheet(sheetName2)
		_ = file.SetCellValue(sheetName2, "A1", "系统不存在的PO")
		_ = file.SetCellValue(sheetName2, "B1", "物料编码")
		_ = file.SetCellValue(sheetName2, "C1", "数量")
		_ = file.SetCellValue(sheetName2, "D1", "根据数量与PO匹配的结果")
		_ = file.SetCellValue(sheetName2, "E1", "根据数量与PO匹配的结果的入库单号")
		for i, el := range errPoList {
			for _, item := range pmsMaterialInboundProductList {
				if item.ContractId == el.MatchContractId && item.MaterialId == el.MatchMaterialId {
					inbound := model.NewPmsMaterialInbound()
					err := yc.DBM(model.NewPmsMaterialInbound()).Where("id", item.InboundId).Scan(&inbound)
					if err != nil && !errors.Is(err, sql.ErrNoRows) {
						return err
					}
					if errPoList[i].InboundNo == "" {
						errPoList[i].InboundNo = inbound.No
					} else {
						errPoList[i].InboundNo = errPoList[i].InboundNo + "，" + inbound.No
					}

				}
			}
			_ = file.SetCellValue(sheetName2, fmt.Sprintf("A%d", i+2), el.PO)
			_ = file.SetCellValue(sheetName2, fmt.Sprintf("B%d", i+2), el.Code)
			_ = file.SetCellValue(sheetName2, fmt.Sprintf("C%d", i+2), el.Quantity)
			_ = file.SetCellValue(sheetName2, fmt.Sprintf("D%d", i+2), el.MatchPO)
			_ = file.SetCellValue(sheetName2, fmt.Sprintf("E%d", i+2), el.InboundNo)
		}
	}

	// 写出到文件
	fileName := fmt.Sprintf("采购数据对比-%s.xlsx", gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)

	// 返回文件流
	buffer, err := file.WriteToBuffer()
	if err != nil {
		return err
	}

	utils.SetResponseToStream(ctx, fileName, buffer.Bytes())
	return nil
}

// todo 垃圾代码记得删掉 更新虚拟采购单
func (s *PmsPurchaseContractService) UpdateVirtualOrder(ctx context.Context, queryVo *model.QueryVo) error {
	// 物料列表
	materialList := make([]*model.PmsMaterial, 0)
	materialMap := make(map[string]*model.PmsMaterial)
	err := yc.DBM(model.NewPmsMaterial()).Ctx(ctx).Scan(&materialList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	for _, item := range materialList {
		materialMap[item.Code] = item
	}
	// 虚拟采购单数据
	list := queryVo.PurchaseData
	// 不存在的PO
	for i, item := range list {
		list[i].MaterialId = materialMap[item.Code].ID
	}
	// 采购Excel数据有，系统没有的PO
	errPOList := queryVo.ProductionData
	for i, item := range errPOList {
		errPOList[i].MaterialId = materialMap[item.Code].ID
	}
	// 系统有,采购Excel数据没有的PO
	errPOList2 := queryVo.FinanceData
	for i, item := range errPOList2 {
		errPOList2[i].MaterialId = materialMap[item.Code].ID
	}

	// 开启事务 更新虚拟订单数据
	err = yc.DB(s.Model.GroupName()).Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		if len(list) > 0 {
			for _, item := range list {
				_, err := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Data(g.Map{"virtual_order": 1}).
					Where("material_id", item.MaterialId).
					Where("quantity", item.Quantity).
					Where("po", item.Po).UpdateAndGetAffected()
				if err != nil {
					return err
				}
			}
		}
		if len(errPOList) > 0 {
			for _, item := range errPOList {
				_, err := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Data(g.Map{"matched_po": item.MatchedPo, "matched_po_type": 1}).
					Where("material_id", item.MaterialId).
					Where("quantity", item.Quantity).
					Where("po", item.Po).UpdateAndGetAffected()
				if err != nil {
					return err
				}
			}
		}
		//if len(errPOList2) > 0 {
		//	for _, item := range errPOList2 {
		//		_, err := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Data(g.Map{"matched_po": item.MatchedPo, "matched_po_type": 2}).
		//			Where("material_id", item.MaterialId).Where("po", item.Po).UpdateAndGetAffected()
		//		if err != nil {
		//			return err
		//		}
		//	}
		//}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
	// 查询所有真实的采购数据
	result, err := s.QueryPage(ctx, &model.QueryVo{Page: 1, Size: 9999999, Date: "2024-01-01,2024-06-30"})
	if err != nil {
		return err
	}
	purchaseContractMap := make(map[string]*model.PmsPurchaseContract)
	for _, item := range list {
		purchaseContractMap[item.Po] = item
	}
	notPurchaseContractList := make([]*model.PmsPurchaseOrderContractOutput, 0)
	for _, item := range result.List {
		if purchaseContractMap[item.Po] == nil {
			notPurchaseContractList = append(notPurchaseContractList, item)
		}
	}
	sheetName := "Sheet1"
	file := excelize.NewFile()
	_, _ = file.NewSheet(sheetName)
	_ = file.SetCellValue(sheetName, "A1", "序号")
	_ = file.SetCellValue(sheetName, "B1", "物料编码")
	_ = file.SetCellValue(sheetName, "C1", "PO")
	_ = file.SetCellValue(sheetName, "D1", "系统采购单号")
	_ = file.SetCellValue(sheetName, "E1", "创建时间")
	_ = file.SetCellValue(sheetName, "F1", "数量")
	_ = file.SetCellValue(sheetName, "G1", "根据物料编号和数量与采购上传数据匹配的PO")

	row := 2
	for i, item := range notPurchaseContractList {
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), i+1)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.Code)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), item.Po)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.No)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.CreateTime)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", row), item.Quantity)
		for _, el := range list {
			if el.Code == item.Code && el.Quantity == item.Quantity {
				_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", row), el.Po)
			}
		}
		row++
	}
	// 写出到文件
	fileName := fmt.Sprintf("有差异的PO-%s.xlsx", gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)
	// 返回文件流
	buffer, err := file.WriteToBuffer()
	if err != nil {
		return err
	}
	utils.SetResponseToStream(ctx, fileName, buffer.Bytes())
	return nil
}

// todo 垃圾代码记得删掉  物料采购入库数据汇总导出
func (s *PmsPurchaseContractService) ExportMaterialData(ctx context.Context, queryVo *model.QueryVo) error {
	// 查询所有物料列表
	materialList := make([]*model.PmsMaterial, 0)
	err := yc.DBM(model.NewPmsMaterial()).Ctx(ctx).Scan(&materialList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	// 查询所有订单
	//purchaseOrderList := make([]*model.PmsPurchaseOrder, 0)
	//err = yc.DBM(model.NewPmsPurchaseOrder()).Ctx(ctx).Scan(&purchaseOrderList)
	//if err != nil && !errors.Is(err, sql.ErrNoRows) {
	//	return err
	//}
	//purchaseOrderMap := make(map[int64]*model.PmsPurchaseOrder)
	//for _, item := range purchaseOrderList {
	//	purchaseOrderMap[item.ID] = item
	//}
	// 查询所有合同
	purchaseOrdeContractList := make([]*model.PmsPurchaseOrderContractOutput, 0)
	err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Scan(&purchaseOrdeContractList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	purchaseOrdeContractMap := make(map[int64]*model.PmsPurchaseOrderContractOutput)
	for _, item := range purchaseOrdeContractList {
		purchaseOrdeContractMap[item.ID] = item
	}

	// 物料列表转map， 物料编码为key，
	materialMap := make(map[int64]*model.PmsMaterial)
	materialCodeMap := make(map[string]*model.PmsMaterial)
	for _, item := range materialList {
		materialMap[item.ID] = item
		materialCodeMap[item.Code] = item
	}

	// 系统不存在的物料编号
	errMaterialCodeList := make([]*model.PmsMaterialOutput, 0)

	// 采购导入的数据
	uploadMaterialMap := make(map[string]*model.PmsPurchaseContract)
	list := queryVo.PurchaseData
	purchaseDataSum := 0.0
	count := 0
	if len(list) > 0 {
		for _, item := range list {
			count++
			purchaseDataSum += item.Quantity
			if item.Code == "" {
				errMaterialCodeList = append(errMaterialCodeList, &model.PmsMaterialOutput{Code: item.Code, Po: item.Po, Quantity: item.Quantity, Remark: "采购导入数据，物料编号为空"})
				continue
			}
			if materialCodeMap[item.Code] == nil {
				errRes := &model.PmsMaterialOutput{}
				gconv.Struct(item, &errRes)
				errRes.Remark = "采购导入数据，系统不存在的物料编号"
				errMaterialCodeList = append(errMaterialCodeList, errRes)
				continue
			}
			if uploadMaterialMap[item.Code] == nil {
				uploadMaterialMap[item.Code] = item
			} else {
				uploadMaterialMap[item.Code].Quantity += item.Quantity
				uploadMaterialMap[item.Code].ReceivedQuantity += item.ReceivedQuantity
				uploadMaterialMap[item.Code].ExpectedQuantity += item.ExpectedQuantity
			}
		}
	}

	// 生产导入的数据
	productionDataMap := make(map[string]*model.PmsMaterialOutput)
	if len(queryVo.ProductionData) > 0 {
		for _, item := range queryVo.ProductionData {
			if productionDataMap[item.Code] == nil {
				productionDataMap[item.Code] = item
			} else {
				productionDataMap[item.Code].Quantity += item.Quantity
			}
		}
		for _, item := range queryVo.ProductionData {
			if item.Code == "" {
				errMaterialCodeList = append(errMaterialCodeList, &model.PmsMaterialOutput{Code: item.Code, Po: item.Po, Quantity: item.Quantity, Remark: "生产导入数据，物料编号为空"})
			}
			if materialCodeMap[item.Code] == nil {
				errRes := &model.PmsMaterialOutput{}
				errRes.Code = item.Code
				errRes.Quantity = item.Quantity
				errRes.Remark = "生产导入数据，系统不存在的物料编号"
				errMaterialCodeList = append(errMaterialCodeList, errRes)
			}
		}
	}

	//虚拟订单
	queryWrap := `
		SELECT main.material_id,SUM(main.quantity) AS quantity FROM pms_purchase_contract AS main
		LEFT JOIN pms_purchase_order AS t1 ON main.purchase_id = t1.id
		WHERE t1.deleteTime IS NULL AND main.virtual_order=#{virtual_order}  #{createTime}  GROUP BY main.material_id
	`
	queryVirtualOrder := gstr.Replace(queryWrap, "#{virtual_order}", "1")
	queryVirtualOrder = gstr.Replace(queryVirtualOrder, "#{createTime}", "")
	virtualOrderList := make([]*model.PmsPurchaseOrderContractOutput, 0)
	queryVirtualOrderResult, err := g.DB().Query(ctx, queryVirtualOrder)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	err = queryVirtualOrderResult.Structs(&virtualOrderList)
	if err != nil {
		return err
	}
	// 真实订单
	queryRealOrder := gstr.Replace(queryWrap, "#{virtual_order}", "0")
	if queryVo.Date != "" {
		//date := strings.Split(queryVo.Date, ",")
		//queryRealOrder = gstr.Replace(queryRealOrder, "#{createTime}", fmt.Sprintf("AND t1.createTime BETWEEN '%s' AND '%s'", date[0], date[1]))
		queryRealOrder = gstr.Replace(queryRealOrder, "#{createTime}", fmt.Sprintf("AND t1.createTime BETWEEN '%s' AND '%s'", "2024-01-01", "2024-06-30"))
	} else {
		queryRealOrder = gstr.Replace(queryRealOrder, "#{createTime}", "")
	}
	realOrderList := make([]*model.PmsPurchaseOrderContractOutput, 0)
	queryRealOrderResult, err := g.DB().Query(ctx, queryRealOrder)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	err = queryRealOrderResult.Structs(&realOrderList)

	// virtualOrderList 转map， 物料ID为key，
	virtualOrderMap := make(map[int64]*model.PmsPurchaseOrderContractOutput) // queryWrap 是用物料id分组查询不会有重复的物料ID
	for _, item := range virtualOrderList {
		virtualOrderMap[item.MaterialId] = item
	}
	// realOrderList 转map， 物料ID为key，
	realOrderMap := make(map[int64]*model.PmsPurchaseOrderContractOutput) // queryWrap 是用物料id分组查询不会有重复的物料ID
	for _, item := range realOrderList {
		realOrderMap[item.MaterialId] = item
	}

	otherInboundList := make([]*model.PmsMaterialInboundProduct, 0)
	queryWrap = `
		WITH query_data AS 
		(
			SELECT main.* FROM pms_material_inbound_product AS main 
			LEFT JOIN pms_material_inbound AS t1 ON main.inbound_id = t1.id 
			WHERE t1.deleteTime IS NULL  AND t1.type #{type}  #{createTime}
		)
		#{sql}
	`
	// 查询其他入库SQL
	queryOtherInbound := gstr.Replace(queryWrap, "#{type}", "!=1")
	if queryVo.Date != "" {
		date := strings.Split(queryVo.Date, ",")
		queryOtherInbound = gstr.Replace(queryOtherInbound, "#{createTime}", fmt.Sprintf("AND t1.createTime BETWEEN '%s' AND '%s'", date[0], date[1]))
	} else {
		queryOtherInbound = gstr.Replace(queryOtherInbound, "#{createTime}", "")
	}
	queryOtherInbound = gstr.Replace(queryOtherInbound, "#{sql}", "SELECT main.material_id, SUM(main.quantity) AS quantity FROM query_data AS main GROUP BY main.material_id")
	queryOtherInboundResult, err := g.DB().Query(ctx, queryOtherInbound)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	err = queryOtherInboundResult.Structs(&otherInboundList)
	if err != nil {
		return err
	}
	// otherInboundList 转map， 物料ID为key，
	otherInboundMap := make(map[int64]*model.PmsMaterialInboundProduct)
	for _, item := range otherInboundList {
		otherInboundMap[item.MaterialId] = item
	}

	queryOrderInboundDataSql := `
		SELECT main.material_id,SUM(main.quantity) AS quantity FROM query_data AS main 
		LEFT JOIN pms_purchase_contract AS t1 ON main.contract_id = t1.id WHERE t1.virtual_order=0 GROUP BY main.material_id
	`
	//查询采购单数据SQL
	queryOrderInbound := gstr.Replace(queryWrap, "#{type}", "=1") // todo 可以要改 IN (1,2)
	if queryVo.Date != "" {
		date := strings.Split(queryVo.Date, ",")
		queryOrderInbound = gstr.Replace(queryOrderInbound, "#{createTime}", fmt.Sprintf("AND t1.createTime BETWEEN '%s' AND '%s'", date[0], date[1]))
	} else {
		queryOrderInbound = gstr.Replace(queryOrderInbound, "#{createTime}", "")
	}
	queryOrderInbound = gstr.Replace(queryOrderInbound, "#{sql}", queryOrderInboundDataSql)
	orderInboundList := make([]*model.PmsMaterialInboundProduct, 0)
	queryOrderInboundResult, err := g.DB().Query(ctx, queryOrderInbound)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	err = queryOrderInboundResult.Structs(&orderInboundList)
	if err != nil {
		return err
	}
	// orderInboundList 转map， 物料ID为key，
	orderInboundMap := make(map[int64]*model.PmsMaterialInboundProduct)
	for _, item := range orderInboundList {
		orderInboundMap[item.MaterialId] = item
	}

	// 财务导入数据
	financeData := queryVo.FinanceData
	financeDataMap := make(map[string]*model.PmsPurchaseContract)
	// 财务退货
	financeReturnDataMap := make(map[string]*model.PmsPurchaseContract)
	if len(financeData) > 0 {
		for _, item := range financeData {
			if item.Quantity < 0 {
				if financeReturnDataMap[item.Code] == nil {
					financeReturnDataMap[item.Code] = item
				} else {
					financeReturnDataMap[item.Code].Quantity += item.Quantity
				}
			} else {
				if financeDataMap[item.Code] == nil {
					financeDataMap[item.Code] = item
				} else {
					financeDataMap[item.Code].Quantity += item.Quantity
				}
			}
			if item.Code == "" {
				errMaterialCodeList = append(errMaterialCodeList, &model.PmsMaterialOutput{Code: item.Code, Po: item.Po, Quantity: item.Quantity, Remark: "财务导入数据，物料编号为空"})
			}
			if materialCodeMap[item.Code] == nil {
				errRes := &model.PmsMaterialOutput{}
				errRes.Code = item.Code
				errRes.Quantity = item.Quantity
				errRes.Remark = "财务导入，系统不存在的物料编号"
				errMaterialCodeList = append(errMaterialCodeList, errRes)
			}
		}
	}

	sheetName := "物料入库汇总数据"
	file := excelize.NewFile()
	file.SetSheetName("Sheet1", "物料入库汇总数据")
	_, _ = file.NewSheet(sheetName)
	_ = file.SetCellValue(sheetName, "A1", "序号")
	_ = file.SetCellValue(sheetName, "B1", "物料编码")
	_ = file.SetCellValue(sheetName, "C1", "系统《假》采购单数量")
	_ = file.SetCellValue(sheetName, "D1", "系统采购单数量")
	_ = file.SetCellValue(sheetName, "E1", "截止6月30号系统入库数量")
	_ = file.SetCellValue(sheetName, "F1", "截止6月30号系统其他入库数量(包含生产退料)") // todo ？1 需要包含生产退料吗 2：是否包含 2024-03-18之前的数据
	_ = file.SetCellValue(sheetName, "G1", "系统采购单在途数量(系统采购单数量 - 截止6月30号系统入库数量)")
	_ = file.SetCellValue(sheetName, "H1", "系统直接导出在途数量")
	_ = file.SetCellValue(sheetName, "I1", "在途差异(系统直接导出在途数量 - 系统采购单在途数量)")
	_ = file.SetCellValue(sheetName, "J1", "截止6月30号系统结存")
	_ = file.SetCellValue(sheetName, "K1", "24年初期系统库存") // todo ？
	_ = file.SetCellValue(sheetName, "L1", "电脑采购的采购数量")
	_ = file.SetCellValue(sheetName, "M1", "电脑采购的已收数量")
	_ = file.SetCellValue(sheetName, "N1", "电脑采购的在途数量")
	_ = file.SetCellValue(sheetName, "O1", "电脑采购数量差异(系统采购单数量 - 电脑采购的采购数量)")
	_ = file.SetCellValue(sheetName, "P1", "系统与采购入库差异(截止6月30号系统入库数量-采购电脑账入库数)")
	_ = file.SetCellValue(sheetName, "Q1", "系统与采购在途差异(截止6月30号系统导出在途-采购电脑在途)")
	_ = file.SetCellValue(sheetName, "R1", "仓库1-6月30号入库")
	_ = file.SetCellValue(sheetName, "S1", "入库数量差异(系统入库数量 -仓库电脑入库数量)")
	_ = file.SetCellValue(sheetName, "T1", "财务1-5月30号入库")
	_ = file.SetCellValue(sheetName, "U1", "财务1-5月30号退货")
	_ = file.SetCellValue(sheetName, "V1", "入库数量差异(截止6月30号系统入库数量 -财务电脑入库数量")
	_ = file.SetCellValue(sheetName, "W1", "物料名称")

	row := 2
	for i, item := range materialList {
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), i+1)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.Code)
		if virtualOrderMap[item.ID] != nil {
			_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), virtualOrderMap[item.ID].Quantity)
		}
		realOrderQuantity := 0.0
		if realOrderMap[item.ID] != nil {
			realOrderQuantity = realOrderMap[item.ID].Quantity
			_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), realOrderQuantity)
		}
		orderInboundQuantity := 0.0
		if orderInboundMap[item.ID] != nil {
			orderInboundQuantity = orderInboundMap[item.ID].Quantity
			_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), orderInboundQuantity)
		}
		if otherInboundMap[item.ID] != nil {
			_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", row), otherInboundMap[item.ID].Quantity)
		}
		expectedInbound := 0.0
		if realOrderMap[item.ID] != nil && orderInboundMap[item.ID] != nil {
			expectedInbound = realOrderMap[item.ID].Quantity - orderInboundMap[item.ID].Quantity
			_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", row), expectedInbound)
		}
		_ = file.SetCellValue(sheetName, fmt.Sprintf("H%d", row), item.ExpectedInbound)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("I%d", row), item.ExpectedInbound-expectedInbound)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", row), item.Inventory)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", row), 0)
		if uploadMaterialMap[item.Code] != nil {
			_ = file.SetCellValue(sheetName, fmt.Sprintf("L%d", row), uploadMaterialMap[item.Code].Quantity)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", row), uploadMaterialMap[item.Code].ReceivedQuantity)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("N%d", row), uploadMaterialMap[item.Code].ExpectedQuantity)
			if realOrderMap[item.ID] != nil && uploadMaterialMap[item.Code] != nil {
				_ = file.SetCellValue(sheetName, fmt.Sprintf("O%d", row), realOrderMap[item.ID].Quantity-uploadMaterialMap[item.Code].Quantity)
				_ = file.SetCellValue(sheetName, fmt.Sprintf("P%d", row), expectedInbound-uploadMaterialMap[item.Code].ReceivedQuantity)
				_ = file.SetCellValue(sheetName, fmt.Sprintf("Q%d", row), expectedInbound-uploadMaterialMap[item.Code].ExpectedQuantity)
			}
		}
		if productionDataMap[item.Code] != nil {
			quantity := productionDataMap[item.Code].Quantity
			_ = file.SetCellValue(sheetName, fmt.Sprintf("R%d", row), quantity)
			if realOrderMap[item.ID] != nil && productionDataMap[item.Code] != nil {
				//diffQut := realOrderQuantity + orderInboundQuantity - quantity
				diffQut := realOrderQuantity - quantity
				_ = file.SetCellValue(sheetName, fmt.Sprintf("S%d", row), diffQut)
			}
		}
		if financeReturnDataMap[item.Code] != nil {
			_ = file.SetCellValue(sheetName, fmt.Sprintf("U%d", row), financeReturnDataMap[item.Code].Quantity)
		}
		if financeDataMap[item.Code] != nil {
			quantity := financeDataMap[item.Code].Quantity
			_ = file.SetCellValue(sheetName, fmt.Sprintf("T%d", row), quantity)
			if realOrderMap[item.ID] != nil && financeDataMap[item.Code] != nil {
				diffQut := orderInboundQuantity - quantity
				_ = file.SetCellValue(sheetName, fmt.Sprintf("V%d", row), diffQut)
			}
		}
		_ = file.SetCellValue(sheetName, fmt.Sprintf("W%d", row), item.Name)
		row++
	}

	// 不存在的po
	virtualContractList := make([]*model.PmsPurchaseContract, 0)
	err = yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).Where("virtual_order", 1).Scan(&virtualContractList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}

	if len(virtualContractList) > 0 {
		// 虚拟采购订单
		s.ExportVirtualOrder(ctx, file, virtualContractList, materialMap, queryVo)
	}

	// 查询采购订单明细
	expectedQueryWrap := `
	SELECT main.*,t1.order_no,t1.createTime,t1.status FROM pms_purchase_contract AS main 
	LEFT JOIN pms_purchase_order AS t1 ON main.purchase_id = t1.id 
	WHERE t1.deleteTime IS NULL AND main.virtual_order=0  #{createTime}
	`
	if queryVo.Date != "" {
		date := strings.Split(queryVo.Date, ",")
		expectedQueryWrap = gstr.Replace(expectedQueryWrap, "#{createTime}", fmt.Sprintf("AND t1.createTime BETWEEN '%s' AND '%s'", date[0], date[1]))
	} else {
		expectedQueryWrap = gstr.Replace(expectedQueryWrap, "#{createTime}", "")
	}
	expectedQueryResult, err := g.DB().Query(ctx, expectedQueryWrap)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	expectedContractList := make([]*model.PmsPurchaseOrderContractOutput, 0)
	err = expectedQueryResult.Structs(&expectedContractList)
	if err != nil {
		return err
	}
	if len(expectedContractList) > 0 {
		// 采购订单明细
		s.ExportExpectedContrac(ctx, file, expectedContractList, materialMap, queryVo)
	}

	if len(errMaterialCodeList) > 0 {
		// 物料编号差异
		s.ExportMaterialCodeDiff(ctx, file, errMaterialCodeList, queryVo, materialCodeMap)
	}

	// 系统入库明细
	inboundProductList := make([]*model.PmsMaterialInboundProductVo, 0)
	joinWrap := yc.DBM(model.NewPmsMaterialInboundProduct()).Ctx(ctx).As("main").LeftJoin("pms_material_inbound AS t1", "main.inbound_id = t1.id")
	if queryVo.Date != "" {
		date := strings.Split(queryVo.Date, ",")
		joinWrap = joinWrap.Where("t1.createTime BETWEEN ? AND ?", date[0], date[1])
	}
	err = joinWrap.Fields("main.*,t1.no,t1.createTime,t1.type,t1.order_id,t1.status").Where("t1.deleteTime IS NULL").Scan(&inboundProductList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}

	if len(inboundProductList) > 0 {
		// 系统入库明细
		s.ExportInboundProduct(ctx, file, inboundProductList, materialMap, queryVo, purchaseOrdeContractMap)
	}

	fileName := fmt.Sprintf("物料入库数据汇总(不包含267条假PO)-%s.xlsx", gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)
	buffer, err := file.WriteToBuffer()
	if err != nil {
		return err
	}
	utils.SetResponseToStream(ctx, fileName, buffer.Bytes())
	return nil
}

func NewPmsPurchaseContractService() *PmsPurchaseContractService {
	return &PmsPurchaseContractService{
		&yc.Service{
			Model: model.NewPmsPurchaseContract(),
		},
	}
}
