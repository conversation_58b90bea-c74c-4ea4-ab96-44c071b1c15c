package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	"github.com/imhuso/lookah-erp/admin/yc"
)

const TableNamePmsMaterial = "pms_material"

// ImportMaterialAddressData 用于导入物料地址
type ImportMaterialAddressData struct {
	Code    string `json:"code"`
	Address string `json:"address"`
}

type MaterialMap struct {
	MaterialCodeMap map[string]*PmsMaterial `json:"materialCodeMap"`
	MaterialIdMap   map[int64]*PmsMaterial  `json:"materialIdMap"`
	MaterialList    []*PmsMaterial          `json:"materialList"`
}

type MaterialDataVo struct {
	List      []*PmsMaterial          `json:"list"`
	IdMap     map[int64]*PmsMaterial  `json:"map"`
	CodeMap   map[string]*PmsMaterial `json:"codeMap"`
	SheetName string                  `json:"sheetName"`
}

// MaterialBundleDetail 用于物料集合详情的物料信息
type MaterialBundleDetail struct {
	gmeta.Meta `orm:"table:pms_material"`

	ID         int64  `json:"id"`
	Code       string `json:"code"`
	Name       string `json:"name"`
	Model      string `json:"model"`
	Unit       string `json:"unit"`
	CoverColor string `json:"coverColor"`
	Process    string `json:"process"`
	Material   string `json:"material"`
	Size       string `json:"size"`
}

// MaterialStock 用户记录物料的库存更新
type MaterialStock struct {
	// 合同id
	ContractId int64 `json:"contractId"`
	// 物料id
	MaterialId int64 `json:"materialId"`
	// 在库库存
	Stock float64 `json:"stock"`
	// 锁定库存
	LockedStock float64 `json:"lockedStock"`
	// 在途库存
	ExpectedInbound float64 `json:"expectedInbound"`
	// 订单占用
	OccupiedInventory float64 `json:"occupiedInventory"`
	// 可使用在途
	DeductibleExpectedInbound float64 `json:"deductibleExpectedInbound"`
	// 已使用在途
	UsedExpectedInbound float64 `json:"usedExpectedInbound"`
	Remark              string  `json:"remark"`
	UsedQuantity        float64 `json:"usedQuantity"` // 领料出库的数量
	// 生产单 pms_production_schedule （生产表）
	ProductionScheduleId int64 `json:"productionScheduleId"` // 生产单
	// 生产单SN
	ProductionScheduleSn string `json:"productionScheduleSn"` // 生产单SN
	// 采购单
	PurchaseOrderId int64 `json:"purchaseOrderId"` // 采购单
	// 采购单SN
	PurchaseOrderSn string `json:"purchaseOrderSn"` // 采购单SN
	// 入库单
	InboundId int64 `json:"warehouseInboundId"` // 入库单
	// 入库单SN
	InboundSn string `json:"warehouseInboundSn"` // 入库单SN
	// 出库单
	OutboundId int64 `json:"warehouseOutboundId"` // 出库单
	// 出库单SN
	OutboundSn string `json:"warehouseOutboundSn"` // 出库单SN
	// 工单id
	WorkOrderId int64 `json:"workOrderId"` // 工单id
	// 工单号
	WorkOrderNo string `json:"workOrderNo"` // 工单号
	// 操作类型 1采购下单，2入库-库存调整，3入库-生成退料，4自定义入库，5采购单入库，6领料出库，7退货出库，8出库-生成退料，9自定义出库，10报废
	OperationType int `json:"operationType"` // 操作类型
	// 其他备注
	OtherRemark string `json:"otherRemark"` // 其他备注
}

type MaterialChangeEntity struct {
	Name       string `json:"name"`
	Model      string `json:"model"`
	CoverColor string `json:"coverColor"`
	Process    string `json:"process"`
	Material   string `json:"material"`
	Size       string `json:"size"`
	Unit       string `json:"unit"`
}

// PmsPurchaseOrderContractMaterialOutput 采购订单合同物料输出
type PmsPurchaseOrderContractMaterialOutput struct {
	gmeta.Meta `orm:"table:pms_material"`

	ID                 int64   `json:"-"`
	Code               string  `json:"code"`
	Name               string  `json:"name"`
	Model              string  `json:"model"`
	Unit               string  `json:"unit"`
	CoverColor         string  `json:"coverColor"`
	Process            string  `json:"process"`
	Material           string  `json:"material"`
	Size               string  `json:"size"`
	ExpectedInbound    float64 `json:"expectedInbound"`
	LockedInventory    float64 `json:"lockedInventory"`
	Inventory          float64 `json:"inventory"`
	ContractId         int64   `json:"contractId"`
	MaterialId         int64   `json:"materialId"`
	PurchaseId         int64   `json:"purchaseId"`
	SupplierId         int64   `json:"supplierId"`
	InboundOutboundKey int     `json:"inbound_outbound_key"`
	AddressName        string  `json:"address_name"`
}

// PmsMaterialOutput 物料输出
type PmsMaterialOutput struct {
	gmeta.Meta `orm:"table:pms_material"`

	ID                 int64   `json:"id" gorm:"-"`
	Code               string  `json:"code" gorm:"-"`
	Name               string  `json:"name" gorm:"-"`
	Model              string  `json:"model" gorm:"-"`
	CoverColor         string  `json:"coverColor" gorm:"-"`
	Process            string  `json:"process" gorm:"-"`
	Material           string  `json:"material" gorm:"-"`
	Size               string  `json:"size" gorm:"-"`
	ExpectedInbound    float64 `json:"expectedInbound" gorm:"-"` //  在途库存
	Unit               string  `json:"unit" gorm:"-"`
	LockedInventory    float64 `json:"lockedInventory" gorm:"-"`
	BindUserId         int64   `json:"bindUserId" gorm:"-"`
	Po                 string  `json:"po" gorm:"-"`
	Inventory          float64 `json:"inventory" gorm:"-"`        // 库存
	ExpectedQuantity   float64 `json:"expectedQuantity" gorm:"-"` // 在途数量
	ReceivedQty        float64 `json:"receivedQty" gorm:"-"`      // 已收数量
	OutboundQuantity   float64 `json:"outboundQuantity" gorm:"-"` // 已出库数量
	OutQty             float64 `json:"outQty" gorm:"-"`           // 出库数量
	BomQuantity        float64 `json:"bomQuantity" gorm:"-"`      // bom用量
	CalcBomQuantity    float64 `json:"calcBomQuantity" gorm:"-"`  // 计算bom用量 工单数量 * bom用量
	Quantity           float64 `json:"quantity" gorm:"-"`         // 数量
	Remark             string  `json:"remark" gorm:"-"`
	MatchedPo          string  `json:"matchedPo" gorm:"-"`
	MaterialId         int64   `json:"materialId" gorm:"-"`
	WorkOrderID        int64   `json:"workOrderId" gorm:"-"`
	AddressName        string  `json:"address_name" gorm:"-"`
	InboundOutboundKey int     `json:"inbound_outbound_key"`
	WorkOrderDetailID  int64   `json:"workOrderDetailId" gorm:"-"`
}

// PmsMaterial mapped from table <pms_material>
type PmsMaterial struct {
	ID int64 `json:"id"       gorm:"column:id;type:bigint(20);not null;primary_key;auto_increment;comment:ID;"` // ID
	// 代码
	Code string `json:"code" gorm:"column:code;type:varchar(100);not null;default:'';comment:代码;uniqueIndex"`
	// 名称
	Name string `json:"name" gorm:"column:name;type:varchar(100);not null;default:'';comment:名称;"`
	// 型号
	Model string `json:"model" gorm:"column:model;type:varchar(500);not null;default:'';comment:型号;"`
	// 尺寸
	Size string `json:"size" gorm:"column:size;type:varchar(100);not null;default:'';comment:尺寸;"`
	// 材质
	Material string `json:"material" gorm:"column:material;type:varchar(100);not null;default:'';comment:材质;"`
	// 工艺
	Process string `json:"process" gorm:"column:process;type:varchar(100);not null;default:'';comment:工艺;"`
	// 封面图片颜色
	CoverColor string `json:"coverColor" gorm:"column:cover_color;type:varchar(100);not null;default:'';comment:封面图片颜色;"`
	// 单位
	Unit string `json:"unit" gorm:"column:unit;type:varchar(100);not null;default:'';comment:单位;"`
	// 库存 （入库更新）
	Inventory float64 `json:"inventory" gorm:"column:inventory;type:decimal(14,4);not null;default:0.0000;comment:库存;"`
	// 在途库存，采购下单入库的数量
	ExpectedInbound float64 `json:"expectedInbound" gorm:"column:expected_inbound;type:decimal(14,4);not null;default:0.0000;comment:在途库存;"`
	// 已使用数量（出库更新）
	UsedQuantity float64 `json:"usedQuantity" gorm:"column:used_quantity;type:decimal(14,4);not null;default:0.0000;comment:已使用数量;"`
	// 锁定库存 废弃
	LockedInventory float64 `json:"lockedInventory" gorm:"column:locked_inventory;type:decimal(14,4);not null;default:0.0000;comment:锁定库存;"`
	// 占用库存 废弃
	OccupiedInventory float64 `json:"occupiedInventory" gorm:"column:occupied_inventory;type:decimal(14,4);not null;default:0.0000;comment:占用库存;"`
	// 可使用预计入库量 废弃
	DeductibleExpectedInbound float64 `json:"deductibleExpectedInbound" gorm:"column:deductible_expected_inbound;type:decimal(14,4);not null;default:0.0000;comment:可抵扣预计入库量;"`
	// 使用预计入库量 废弃
	UsedExpectedInbound float64 `json:"usedExpectedInbound" gorm:"column:used_expected_inbound;type:decimal(14,4);not null;default:0.0000;comment:使用预计入库量;"`
	// 绑定的用户
	BindUserId int64 `json:"bindUserId" gorm:"column:bind_user_id;type:bigint(20);not null;default:0;comment:绑定的用户;"`
	// 物料位置
	AddressId          int64  `json:"address_id" gorm:"column:address_id;type:bigint(20);not null;default:0;comment:物料位置ID;"`
	InboundOutboundKey int    `json:"inbound_outbound_key" gorm:"column:inbound_outbound_key;type:int(10);not null;default:0;comment:关键字ID;"`
	Level              string `json:"level" gorm:"column:level;type:varchar(100);not null;default:'';comment:物料等级;"`
	AddressName        string `json:"address_name" gorm:"column:address_name;type:varchar(100);not null;default:'';comment:物料位置;"`
	// 创建时间
	CreateTime *gtime.Time `json:"createTime" gorm:"column:createTime;not null;index,priority:1;autoCreateTime;comment:创建时间"` // 创建时间
	DeleteTime *gtime.Time `gorm:"column:deleteTime;index;comment:删除时间" json:"-"`                                             // 删除时间
}

// GroupName 返回分组名
func (m *PmsMaterial) GroupName() string {
	return ""
}

// TableName PmsMaterial's table name
func (*PmsMaterial) TableName() string {
	return TableNamePmsMaterial
}

// NewPmsMaterial 创建实例
func NewPmsMaterial() *PmsMaterial {
	return &PmsMaterial{}
}

func init() {
	_ = yc.CreateTable(NewPmsMaterial())
}
