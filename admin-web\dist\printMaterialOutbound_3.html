<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <style>
      @media print {
        @page {
          size: 24.13cm 13.97cm landscape; /* 9.5 x 5.5 英寸转换为厘米，设置为横向 */
          margin: 0.5cm; /* 设置较小的边距 */
        }
      }
    </style>
  </head>

  <body>
    <div id="print_main"></div>
  </body>
  <script>
    const Print = function (dom, options) {
      if (!(this instanceof Print)) return new Print(dom, options)

      this.options = this.extend(
        {
          noPrint: '.no-print',
        },
        options,
      )

      if (typeof dom === 'string') {
        this.dom = document.querySelector(dom)
      } else {
        this.isDOM(dom)
        this.dom = this.isDOM(dom) ? dom : dom.$el
      }

      this.init()
    }
    Print.prototype = {
      init: function () {
        var content = this.getStyle() + this.getHtml()
        this.writeIframe(content)
      },
      extend: function (obj, obj2) {
        for (var k in obj2) {
          obj[k] = obj2[k]
        }
        return obj
      },

      getStyle: function () {
        var str = '',
          styles = document.querySelectorAll('style,link')
        for (var i = 0; i < styles.length; i++) {
          str += styles[i].outerHTML
        }
        str +=
          '<style>' +
          (this.options.noPrint ? this.options.noPrint : '.no-print') +
          '{display:none;}</style>'
        return str
      },

      getHtml: function () {
        var inputs = document.querySelectorAll('input')
        var textareas = document.querySelectorAll('textarea')
        var selects = document.querySelectorAll('select')

        for (var k = 0; k < inputs.length; k++) {
          if (inputs[k].type == 'checkbox' || inputs[k].type == 'radio') {
            if (inputs[k].checked == true) {
              inputs[k].setAttribute('checked', 'checked')
            } else {
              inputs[k].removeAttribute('checked')
            }
          } else if (inputs[k].type == 'text') {
            inputs[k].setAttribute('value', inputs[k].value)
          } else {
            inputs[k].setAttribute('value', inputs[k].value)
          }
        }

        for (var k2 = 0; k2 < textareas.length; k2++) {
          if (textareas[k2].type == 'textarea') {
            textareas[k2].innerHTML = textareas[k2].value
          }
        }

        for (var k3 = 0; k3 < selects.length; k3++) {
          if (selects[k3].type == 'select-one') {
            var child = selects[k3].children
            for (var i in child) {
              if (child[i].tagName == 'OPTION') {
                if (child[i].selected == true) {
                  child[i].setAttribute('selected', 'selected')
                } else {
                  child[i].removeAttribute('selected')
                }
              }
            }
          }
        }
        // 包裹要打印的元素
        let outerHTML = this.wrapperRefDom(this.dom).outerHTML
        return outerHTML
      },
      // 向父级元素循环，包裹当前需要打印的元素
      // 防止根级别开头的 css 选择器不生效
      wrapperRefDom: function (refDom) {
        let prevDom = null
        let currDom = refDom
        // 判断当前元素是否在 body 中，不在文档中则直接返回该节点
        if (!this.isInBody(currDom)) return currDom

        while (currDom) {
          if (prevDom) {
            let element = currDom.cloneNode(false)
            element.appendChild(prevDom)
            prevDom = element
          } else {
            prevDom = currDom.cloneNode(true)
          }
          currDom = currDom.parentElement
        }
        return prevDom
      },

      writeIframe: function (content) {
        var w,
          doc,
          iframe = document.createElement('iframe'),
          f = document.body.appendChild(iframe)
        iframe.id = 'myIframe'
        //iframe.style = "position:absolute;width:0;height:0;top:-10px;left:-10px;";
        iframe.setAttribute(
          'style',
          'position:absolute;width:0;height:0;top:-10px;left:-10px;',
        )
        w = f.contentWindow || f.contentDocument
        doc = f.contentDocument || f.contentWindow.document
        doc.open()
        doc.write(content)
        doc.close()
        var _this = this
        iframe.onload = function () {
          if (iframe.contentWindow.matchMedia) {
            let mediaQueryList = iframe.contentWindow.matchMedia('print')
            mediaQueryList.addListener((mql) => {
              console.log(mql)
              if (mql.matches) {
                onbeforePrint()
              } else {
                onafterprint()
              }
            })
          }
          _this.toPrint(w)
          setTimeout(function () {
            document.body.removeChild(iframe)
          }, 100)
        }
      },

      toPrint: function (frameWindow) {
        try {
          setTimeout(function () {
            frameWindow.focus()
            try {
              if (!frameWindow.document.execCommand('print', false, null)) {
                frameWindow.print()
              }
            } catch (e) {
              console.error('打印出错', e)
              frameWindow.print()
            }
            printFinish()
            frameWindow.close()
          }, 10)
        } catch (err) {
          console.log('err', err)
        }
      },
      // 检查一个元素是否是 body 元素的后代元素且非 body 元素本身
      isInBody: function (node) {
        return node === document.body ? false : document.body.contains(node)
      },
      isDOM:
        typeof HTMLElement === 'object'
          ? function (obj) {
              return obj instanceof HTMLElement
            }
          : function (obj) {
              return (
                obj &&
                typeof obj === 'object' &&
                obj.nodeType === 1 &&
                typeof obj.nodeName === 'string'
              )
            },
    }

    const print_main = document.getElementById('print_main')
    const printHeight = 445
    const opacity = '0'
    let htmlContentDiv = document.createElement('div')
    function render() {
      // 获取SessionStorage中的数据
      let data = sessionStorage.getItem('printData')
      if (!data) {
        alert('打印数据不能为空')
        window.close()
        return
      }
      data = JSON.parse(data)
      let htmlContent = ''
      // data = [
      //   {
      //     date: "",
      //     no: "",
      //     user: "",
      //     supplierName: "",
      //     po: "",
      //     userName: "",
      //     list: [
      //       { code: 'LYW028A005X-XXJ', name: '硅胶垫1', model: '食品级气相胶 米白色 尺寸：∅12.3*2.3mm 硬度60度 耐温350°', warehouse: '五金仓', unit: 'PCS', quantity: 1, remark: '' },
      //       { code: 'LYW028A005X-XXJ', name: '硅胶垫2', model: '食品级气相胶 米白色 尺寸：∅12.3*2.3mm 硬度60度 耐温350°', warehouse: '五金仓', unit: 'PCS', quantity: 1, remark: '' },
      //       { code: 'LYW028A005X-XXJ', name: '硅胶垫3', model: '食品级气相胶 米白色 尺寸：∅12.3*2.3mm 硬度60度 耐温350°', warehouse: '五金仓', unit: 'PCS', quantity: 1, remark: '' },
      //     ]
      //   },
      // ]
      data.forEach((item, index) => {
        const htmlContentStr = createHtmlTag(item, index)
        if (htmlContentStr) {
          htmlContent += htmlContentStr
        }
      })
      htmlContentDiv.innerHTML = htmlContent
    }

    function createHtmlTag(data) {
      let margonBottom = 0
      const hideWarehouseColumn = data.type === 1 // 判断 type 是否为 1
      // page-break-before:always;page-break-inside: avoid; 解决分页没有margin-top的问题
      let html = `
    <div id="print_content" style="width: 33.13cm;margin-top: 0.5cm;page-break-before:always;page-break-inside: avoid;">
    <div style="text-align: center;font-weight: bold;font-family: 微软雅黑, sans-serif;font-size: 12pt;">
      深圳市绿烟科技有限公司
    </div>
    <div style="text-align: center;font-family: 微软雅黑, sans-serif;font-size: 12pt;">
      报废单
    </div>
    <div style="display: flex;justify-content: space-between;margin-top: 5px;width: 33.13cm;">
      <div style="font-family: 微软雅黑, sans-serif;font-size: 9pt;width: 8cm;text-align: left;">
        <span>日期：${data.outboundTime || data.createTime}</span>
      </div>
      <div style="font-family: 微软雅黑, sans-serif;font-size: 9pt;width: 8cm;text-align: left;">
        <span>报废单号：${data.no || ''}</span>
      </div>
    </div>
    <table style="margin-top: 5px;margin-bottom: {margonBottom};border-collapse: collapse;width: 33.13cm;">
      <colgroup>
        <col span="1" style="width: 1.5cm">     <!-- 序号 -->
        <col span="1" style="width: 4.5cm">     <!-- 物料编码 -->
        <col span="1" style="width: 4.5cm">     <!-- 品名 -->
        <col span="1" style="width: 7cm">       <!-- 规格/型号 -->
        ${!hideWarehouseColumn ? `<col span="1" style="width: 2.5cm">` : ''}  <!-- 仓别 -->
        <col span="1" style="width: 2cm">       <!-- 单位 -->
        <col span="1" style="width: 2cm">       <!-- 数量 -->
        <col span="1" style="width: 2cm">       <!-- 报废率 -->
        <col span="1" style="width: 3cm">       <!-- 报废日期 -->
        <col span="1" style="width: 2cm">       <!-- 责任人 -->
        <col span="1" style="width: 4cm">       <!-- 处理方式 -->
        <col span="1" style="width: 4cm">       <!-- 机型 -->
        <col span="1" style="width: 3.5cm">     <!-- 备注 -->
      </colgroup>
      <thead align="center">
        <tr style="border-top: 1pt solid black;border-bottom: 1pt solid black;">
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 3px;">
            序号
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 3px;">
            物料编码
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 3px;">
            品名
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 3px;">
            规格/型号
          </th>
          ${!hideWarehouseColumn ? `<th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 3px;">仓别</th>` : ''}
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 3px;">
            单位
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 3px;">
            数量
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 3px;">
            报废率
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 3px;">
            报废日期
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 3px;">
            责任人
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 3px;">
            处理方式
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 3px;">
            机型
          </th>
          <th style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 3px;">
            备注
          </th>
        </tr>
      </thead>
      <tbody align="center" id="print_content_tbody">${createTr(data.list, hideWarehouseColumn)}</tbody>
    </table>
    <div
      style="margin-top: 5px;display: flex;justify-content: space-between;align-items: center;width: 31.13cm;font-family: 微软雅黑, sans-serif;font-size: 9pt;">
      <div>存根(白联) 采购(红联)财务(黄联)</div>
      <div>LYKJ-CK-001 A0</div>
    </div>
    <div style="margin-top: 5px; display: flex; justify-content: space-between; align-items: center; width: 31.13cm;">
      <div style="display: flex; align-items: center; font-family: 微软雅黑, sans-serif; font-size: 9pt; flex-grow: 1; flex-shrink: 1;">
        <span>生产：</span>
      </div>
      <div style="display: flex; align-items: center; font-family: 微软雅黑, sans-serif; font-size: 9pt; flex-grow: 1; flex-shrink: 1;">
        <span>品质：</span>
      </div>
      <div style="display: flex; align-items: center; font-family: 微软雅黑, sans-serif; font-size: 9pt; flex-grow: 1;">
        <span>工程：</span>
      </div>
      <div style="display: flex; align-items: center; font-family: 微软雅黑, sans-serif; font-size: 9pt; flex-grow: 1; flex-shrink: 1;">
        <span>仓库：</span>
      </div>
      <div style="display: flex; align-items: center; font-family: 微软雅黑, sans-serif; font-size: 9pt; flex-grow: 1; flex-shrink: 1;">
        <span>审核：</span>
      </div>
      <div style="display: flex; align-items: center; font-family: 微软雅黑, sans-serif; font-size: 9pt; flex-grow: 1; flex-shrink: 1;">
        <span>批准：</span>
      </div>
    </div>
  </div>
    `
      const itemDiv = document.createElement('div')
      itemDiv.innerHTML = html
      itemDiv.style.opacity = opacity
      print_main.appendChild(itemDiv)
      const table = itemDiv.querySelector('table')
      if (table) {
        if (itemDiv.offsetHeight < printHeight) {
          margonBottom = printHeight - itemDiv.offsetHeight
          html = html.replace('{margonBottom}', margonBottom + 80 + 'px;')
        }
      }
      html = html.replace('{margonBottom}', margonBottom + 'px;')
      print_main.removeChild(itemDiv)
      return html
    }

    function createTr(data, hideWarehouseColumn) {
      let tr = ''
      data.forEach((item, index) => {
        // 处理报废日期格式，只显示到日期
        const scrapDate = item.scrap_date ? item.scrap_date.split(' ')[0] : ''

        tr += `
        <tr style="text-align: center;height: 1.8cm;">
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 2px;white-space: nowrap;">${index + 1}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 2px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">${item.code || ''}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 2px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">${item.name || ''}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 2px;text-align: left;white-space: normal;word-break: break-all;">${item.model || ''}</td>
          ${!hideWarehouseColumn ? `<td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 2px;white-space: nowrap;">${item.warehouse || ''}</td>` : ''}
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 2px;white-space: nowrap;">${item.unit || ''}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 2px;white-space: nowrap;">${item.quantity || ''}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 2px;white-space: nowrap;">
             ${ item.total_quantity >= item.quantity ?  ((item.quantity / item.total_quantity).toFixed(4) + '%') : ''}
          </td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 2px;white-space: nowrap;">${scrapDate}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 2px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">${item.responsible || ''}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 2px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">${item.handling_method || ''}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 2px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">${item.product_group_name || ''}</td>
          <td style="font-family: 微软雅黑, sans-serif;font-size: 9pt;padding: 2px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">${item.remark || ''}</td>
        </tr>
      `
      })
      return tr
    }
    render()
    function handlePrint() {
      new Print(htmlContentDiv)
    }
    window.onload = function () {
      setTimeout(() => {
        handlePrint()
      }, 100)
    }
    // 监听打印开始事件
    function onbeforePrint() {
      console.log('打印开始')
    }

    function onafterprint() {
      closeWindow()
    }
    function printFinish() {
      closeWindow()
    }
    function closeWindow(t = 100) {
      setTimeout(() => {
        console.log('打印完成')
        window.close()
      }, t)
    }
    // 根据宽度,字体大小计算高度
    function getHeight(width, fontSize) {
      return (width / 21.5) * fontSize + 10
    }
  </script>
</html>
