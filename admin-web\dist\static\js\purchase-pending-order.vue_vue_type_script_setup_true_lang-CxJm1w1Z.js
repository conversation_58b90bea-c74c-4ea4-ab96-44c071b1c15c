import{i as N,e as oe}from"./index-BuqCFB-b.js";import{a as le}from"./index-BAHxID_w.js";import{n as Q}from"./index-CBanFtSc.js";import{_ as ae}from"./material-table-columns.vue_vue_type_script_setup_true_lang-hBwyRg0n.js";import{c as D,b as _,q as c,i as r,w as t,h as o,j as s,t as f,v as T,B as d,y as ne,E as V,o as i}from"./.pnpm-Kv7TmmH8.js";const re={flex:"~ items-center"},se=D({name:"undefined"}),fe=D({...se,props:{isSelector:{type:Boolean,default:!1},status:{type:Number,default:void 0},showCompleted:{type:Boolean,default:!0},pageSize:{type:Number,default:20}},emits:["selected"],setup(E,{emit:I}){const g=_(!1),m=E,R=I,{router:q,service:B}=le(),h=_(m.isSelector),W=_(m.pageSize),w=_(m.showCompleted),x=_(1),M=_(x.value),v=N.useCrud({service:B.pms.purchase.order.pending},l=>{l.refresh({size:W.value,showCompleted:w.value,status:m.status!==void 0?m.status:x.value})}),$=N.useTable({autoHeight:!h.value,columns:[{type:"expand",prop:"contracts"},{prop:"orderNo",label:"采购单号",align:"left"},{prop:"internalOrderNo",label:"生产单号",align:"left"},{prop:"total",label:"总采购数量",showOverflowTooltip:!0},{prop:"receivedQuantity",label:"已收货数量",showOverflowTooltip:!0},{prop:"createTime",label:"订单日期",width:180,showOverflowTooltip:!0},{type:"op",label:"操作",width:w.value?240:160,fixed:"right",buttons:["slot-btn-inbound","slot-btn-select","slot-btn-return","slot-btn-export"]}]});function z(l,n){var u;(n==null?void 0:n.type)==="expand"||(n==null?void 0:n.type)==="op"||(u=$.value)==null||u.toggleRowExpansion(l)}const Y=N.useSearch({items:[{label:"状态",props:{labelWidth:"150px"},hidden:!w.value,prop:"showCompleted",value:x.value,component:{name:"el-select",props:{style:"width: 120px",placeholder:"请选择状态",clearable:!0,trueValue:1,falseValue:0,onChange(l){var n;M.value=l,(n=v.value)==null||n.refresh({status:l,page:1})}},options:[{label:"未完成",value:1},{label:"已完成",value:2}]}},{label:"订单 / PO / 物料编码",prop:"keyword",props:{labelWidth:"150px"},component:{name:"el-input",props:{placeholder:"输入订单/PO/物料编码搜索",clearable:!1,onChange(l){var n;(n=v.value)==null||n.refresh({keyWord:l.trim(),page:1})}}}}]});function F({row:l}){return l.receivedQuantity>=l.quantity-l.transfer?"success-row":""}function j(l){q.push(`/pms/material/inbound/add?orderId=${l.id}`)}function A(l){h.value&&R("selected",l)}function H(l){q.push(`/pms/material/outbound/add?return=1&orderId=${l.id}`)}function L(l){console.log("导出数据",l),g.value=!0;const n={url:"/export",method:"POST",responseType:"blob",data:{contracts:l.contracts}};B.pms.purchase.order.pending.request(n).then(u=>{var b;oe(u)&&V.success("导出成功"),(b=v.value)==null||b.refresh()}).catch(u=>{V.error(u.message||"导出失败")}).finally(()=>{g.value=!1})}return(l,n)=>{const u=r("cl-refresh-btn"),b=r("cl-flex1"),G=r("cl-search"),k=r("el-row"),y=r("el-button"),J=r("el-badge"),P=r("el-tooltip"),K=r("cl-svg"),p=r("el-table-column"),C=r("el-text"),U=r("cl-date-text"),X=r("el-table"),Z=r("cl-table"),ee=r("cl-pagination"),te=r("cl-crud");return i(),c(te,{ref_key:"Crud",ref:v},{default:t(()=>[o(k,null,{default:t(()=>[o(u),o(b),o(G,{ref_key:"Search",ref:Y},null,512)]),_:1}),o(k,null,{default:t(()=>[o(Z,{ref_key:"Table",ref:$,"row-key":"orderNo",class:"cursor-pointer","max-height":"600","highlight-current-row":"",onRowClick:z},{"slot-btn-inbound":t(({scope:a})=>[h.value?d("",!0):(i(),c(y,{key:0,disabled:M.value===2,type:"primary",text:"",bg:"",onClick:e=>j(a.row)},{default:t(()=>[s(" 入库 ")]),_:2},1032,["disabled","onClick"]))]),"slot-btn-select":t(({scope:a})=>[h.value?(i(),c(y,{key:0,type:"success",text:"",bg:"",onClick:e=>A(a.row)},{default:t(()=>[s(" 选择 ")]),_:2},1032,["onClick"])):d("",!0)]),"slot-btn-return":t(({scope:a})=>[h.value?d("",!0):(i(),c(y,{key:0,disabled:!a.row.canReturn,type:"warning",text:"",bg:"",onClick:e=>H(a.row)},{default:t(()=>[s(" 退货 ")]),_:2},1032,["disabled","onClick"]))]),"slot-btn-export":t(({scope:a})=>[o(y,{type:"info",loading:g.value,text:"",bg:"",onClick:e=>L(a.row)},{default:t(()=>[s(" 导出 ")]),_:2},1032,["loading","onClick"])]),"column-orderNo":t(({scope:a})=>[ne("div",re,[s(f(a.row.orderNo||"-")+" ",1),a.row.parentOrderId>0?(i(),c(P,{key:0,effect:"dark",content:"这是一个子订单",placement:"top-start"},{default:t(()=>[o(J,{class:"ml5px mt7px",type:"danger",value:"子"})]),_:1})):d("",!0),a.row.hasWarning?(i(),c(P,{key:1,effect:"dark",content:"提交订单时BOM版本有更新，请仔细核对",placement:"top-start"},{default:t(()=>[o(K,{"m-x10px":"",name:"carbonWarningAltFilled"})]),_:1})):d("",!0)])]),"column-receivedQuantity":t(({scope:a})=>{var e;return[s(f(T(Q)(((e=a.row.contracts)==null?void 0:e.reduce((S,O)=>S+O.receivedQuantity,0))||0)),1)]}),"column-total":t(({scope:a})=>{var e;return[s(f(T(Q)(((e=a.row.contracts)==null?void 0:e.reduce((S,O)=>S+O.quantity,0))||0)),1)]}),"column-contracts":t(({scope:a})=>[o(X,{"row-class-name":F,data:a.row.contracts,stripe:"",border:"","max-height":"600"},{default:t(()=>[o(p,{prop:"po",label:"PO",align:"left",width:"190","show-overflow-tooltip":""}),o(p,{prop:"extra.totalCanInStock",label:"需入库数量",align:"center",width:"100","show-overflow-tooltip":""},{default:t(({row:e})=>[s(f(T(Q)(e.quantity-e.transfer)),1)]),_:1}),o(p,{prop:"receivedQuantity",label:"已入库数量",align:"center",width:"100","show-overflow-tooltip":""},{default:t(({row:e})=>[e.receivedQuantity>e.quantity?(i(),c(C,{key:0,type:"danger"},{default:t(()=>[s(f(Math.max(e.receivedQuantity-e.transfer,0)),1)]),_:2},1024)):d("",!0),e.receivedQuantity===e.quantity?(i(),c(C,{key:1,type:"success"},{default:t(()=>[s(f(Math.max(e.receivedQuantity-e.transfer,0)),1)]),_:2},1024)):d("",!0),e.receivedQuantity<e.quantity?(i(),c(C,{key:2},{default:t(()=>[s(f(Math.max(e.receivedQuantity-e.transfer,0)),1)]),_:2},1024)):d("",!0)]),_:1}),o(p,{prop:"quantity",label:"采购数量",align:"center",width:"100","show-overflow-tooltip":""}),o(p,{prop:"transfer",label:"转单数量",align:"center",width:"100","show-overflow-tooltip":""}),o(ae),o(p,{prop:"supplierName",label:"供应商",align:"left",width:"150","show-overflow-tooltip":""}),o(p,{prop:"unit",label:"单位",align:"center",width:"70","show-overflow-tooltip":""}),o(p,{prop:"deliveryDate",label:"交货日期",align:"center",width:"100","show-overflow-tooltip":""},{default:t(({row:e})=>[o(U,{"model-value":e.deliveryDate,format:"YYYY-MM-DD"},null,8,["model-value"])]),_:1})]),_:2},1032,["data"])]),_:1},512)]),_:1}),o(k,null,{default:t(()=>[o(b),o(ee)]),_:1})]),_:1},512)}}});export{fe as _};
