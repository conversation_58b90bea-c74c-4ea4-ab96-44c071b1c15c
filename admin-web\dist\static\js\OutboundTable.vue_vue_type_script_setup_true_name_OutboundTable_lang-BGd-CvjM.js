import{i as _,s as k}from"./index-BuqCFB-b.js";import{c as i,q as x,i as t,w as n,h as e,o as C}from"./.pnpm-Kv7TmmH8.js";const T=i({name:"OutboundTable"}),O=i({...T,emits:["selected"],setup(v,{emit:u}){const d=u,m=_.useTable({columns:[{label:"#",prop:"products",type:"expand"},{label:"出库单号",prop:"no",width:220},{label:"出库总数量",prop:"totalQuantity"},{label:"创建时间",prop:"createTime"},{label:"备注",prop:"remark",width:150,showOverflowTooltip:!0},{type:"op",label:"操作",width:80,buttons:[{label:"选择",type:"primary",onClick:o=>{d("selected",o.scope)}}]}]}),b=_.useCrud({service:k.pms.material.outbound,async onRefresh(o,{next:p,render:c}){const{_:r,list:s,pagination:a}=await p(o);c(s,a)}},o=>{o.refresh({status:3,type:4,finished:!0})});return(o,p)=>{const c=t("cl-refresh-btn"),r=t("cl-flex1"),s=t("cl-search-key"),a=t("el-row"),l=t("el-table-column"),f=t("el-table"),h=t("cl-table"),y=t("cl-pagination"),w=t("cl-crud");return C(),x(w,{ref_key:"Crud",ref:b},{default:n(()=>[e(a,null,{default:n(()=>[e(c),e(r),e(s)]),_:1}),e(a,null,{default:n(()=>[e(h,{ref_key:"Table",ref:m,"row-key":"id","auto-height":!1,style:"height:600px"},{"column-products":n(({scope:g})=>[e(f,{data:g.row.products,style:{width:"100%"},border:""},{default:n(()=>[e(l,{label:"物料代码",prop:"code",align:"center"}),e(l,{label:"物料名称",prop:"name",align:"center"}),e(l,{label:"型号",prop:"model",align:"center"}),e(l,{label:"退货数量",prop:"quantity",align:"center"}),e(l,{label:"已补数量",prop:"restockingQty",align:"center"}),e(l,{label:"单位",prop:"unit",align:"center"})]),_:2},1032,["data"])]),_:1},512)]),_:1}),e(a,null,{default:n(()=>[e(r),e(y)]),_:1})]),_:1},512)}}});export{O as _};
