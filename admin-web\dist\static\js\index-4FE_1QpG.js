import{c as ue,b as u,e as Oe,f as g,y as e,h as s,w as l,i as p,G as P,H as ie,v as r,q as b,j as x,B as $,t as _,L as We,a3 as Re,a4 as X,a5 as de,a6 as Ke,a7 as Fe,F as Me,s as Ne,a8 as Ae,a9 as Ge,a0 as je,E as S,o as d,V as He,T as Ze}from"./.pnpm-Kv7TmmH8.js";import{g as Je,i as K,j as re,e as ce}from"./index-BuqCFB-b.js";import{a as Qe}from"./index-BAHxID_w.js";const Xe={class:"drawing-management"},Ye={class:"left-panel"},et={class:"right-panel"},tt={class:"panel-header"},ot={class:"header-left"},st={key:0},lt={key:1},at={class:"header-right"},nt={class:"panel-content"},it={class:"product-selector"},dt={class:"search-bar"},rt={class:"pagination-wrapper"},ct={class:"dialog-footer"},ut={class:"selection-info"},pt={key:0,class:"selected-count"},_t={class:"footer-buttons"},vt={class:"premium-header"},ht=e("div",{class:"header-background"},[e("div",{class:"bg-pattern"}),e("div",{class:"bg-gradient"})],-1),mt={class:"header-content"},ft={class:"header-left"},gt={class:"download-icon"},wt={class:"icon-circle"},yt=e("div",{class:"icon-pulse"},null,-1),bt={class:"header-info"},kt=e("h2",{class:"dialog-title"}," 图纸下载中心 ",-1),Ct={key:0,class:"dialog-subtitle"},Pt={class:"header-right"},xt={class:"premium-content"},Dt={key:0,class:"download-workspace"},St={class:"featured-version"},Tt={class:"featured-header"},Vt={class:"version-status"},zt={class:"status-badge latest"},$t={class:"badge-icon"},Bt=e("span",null,"最新版本",-1),It={class:"version-meta"},Ut={class:"version-label"},qt={class:"update-date"},Et={class:"file-info"},Lt={class:"file-icon"},Ot=e("div",{class:"file-details"},[e("span",{class:"file-type"},"ZIP 压缩包"),e("span",{class:"file-size"},"约 2.5 MB")],-1),Wt={class:"featured-content"},Rt={class:"drawing-name"},Kt={class:"drawing-desc"},Ft={class:"download-actions"},Mt={class:"btn-content"},Nt=e("span",{class:"btn-text"},"立即下载",-1),At=e("div",{class:"btn-shine"},null,-1),Gt={class:"download-info"},jt={class:"info-item"},Ht={class:"info-item"},Zt=e("span",null,"包含完整技术图纸",-1),Jt={key:0,class:"history-workspace"},Qt={class:"workspace-header"},Xt={class:"header-left"},Yt={class:"section-icon"},eo={class:"section-info"},to=e("h4",{class:"section-title"}," 历史版本 ",-1),oo={class:"section-desc"},so={class:"header-right"},lo={class:"history-timeline"},ao={class:"timeline-marker"},no=e("div",{class:"marker-dot"},null,-1),io={key:0,class:"marker-line"},ro={class:"timeline-content"},co={class:"version-card"},uo={class:"card-header"},po={class:"version-info"},_o={class:"version-number"},vo={class:"version-date"},ho=e("span",null,"下载",-1),mo={class:"card-body"},fo={class:"version-title"},go={class:"version-description"},wo={key:1,class:"empty-workspace"},yo={class:"empty-illustration"},bo=e("h4",null,"暂无历史版本",-1),ko=e("p",null,"当前图纸还没有历史版本记录",-1),Co=ue({name:"pms-bill-payment"}),Vo=ue({...Co,setup(Po){const{service:v}=Qe(),{dict:Y}=Je(),F=Y.get("drawing_type"),ee=Y.get("color"),q=u([]),M=u(!1),y=u(null);u("");const E=u([]),te=u([]),N=u(!1),B=u(!1),L=u([]),m=u([]),h=u({page:1,size:20,total:0}),D=u(""),A=u(!1),T=u(!1),O=u();let G=!1;const W=u(!1),c=u(null),I=u(!1),oe=Oe(()=>{var t;if(!((t=c.value)!=null&&t.historyVersions))return[];const o=c.value.historyVersions;return I.value?o:o.slice(0,3)});async function pe(){try{const o=await v.pms.product.request({url:"/getAllProduct",method:"GET"});te.value=o==null?void 0:o.map(t=>({group_id:t.groupId,value:t.id,sku:t.sku,label:`${t.sku} ${t.name}`}))}catch(o){console.error(o)}}pe();function j(o){return o?new Date(o).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):""}const H=K.useUpsert({props:{class:"drawing-form",labelWidth:"120px"},items:[{prop:"_edit_notice",label:"",hidden:()=>!T.value,component:{name:"el-alert",props:{title:"编辑提醒",description:"您正在编辑图纸信息，修改后将生成新版本。请确认所有信息无误后再提交。",type:"warning",showIcon:!0,closable:!1}},props:{class:"full-line"}},{prop:"title",label:"图纸标题",required:!0,hidden:()=>T.value,component:{name:"el-input"}},{prop:"type",label:"图纸类型",required:!0,hidden:()=>T.value,component:{options:F,name:"el-select"}},{prop:"description",label:"描述",hidden:()=>T.value,component:{name:"el-input",props:{type:"textarea",rows:3}}},{prop:"drawing_url",label:"图纸文件",required:!0,hidden:()=>A.value,component:{name:"cl-upload",props:{accept:".zip,.rar",text:"上传图纸文件",limitSize:500,type:"file",disabled:!1,isPrivate:!1}}}],async onClose(o,t){A.value=!1,T.value=!1,t()}}),_e=K.useTable({columns:[{label:"创建时间",prop:"createTime",width:160,sortable:"desc"},{label:"标题",prop:"title",minWidth:200,showOverflowTooltip:!0},{label:"类型",prop:"type",minWidth:80,formatter:o=>{var t,n;return((n=(t=F.value)==null?void 0:t.find(i=>i.value===o.type))==null?void 0:n.label)||"-"}},{label:"图纸版本",prop:"version",minWidth:80,align:"center",formatter:o=>`V${o.version}`},{label:"描述",prop:"description",minWidth:160,showOverflowTooltip:!0,sortable:"desc"},{type:"op",label:"操作",width:360,buttons:["slot-btn-update","slot-btn-upload","slot-btn-download","delete"]}]}),R=K.useCrud({service:v.pms.drawing,async onRefresh(o,{next:t,render:n}){const{list:i,pagination:k}=await t(o);n(i,k)}},o=>{o.refresh()}),ve=K.useSearch({items:[{label:"产品图纸",prop:"product_id",props:{labelWidth:"100px"},component:{name:"el-select",props:{style:"width: 160px",clearable:!0,filterable:!0,onChange(o){var t;(t=R.value)==null||t.refresh({product_id:o,page:1})}},options:te}},{label:"图纸类型",prop:"type",props:{labelWidth:"80px"},component:{name:"el-select",props:{style:"width: 160px",clearable:!0,filterable:!0,onChange(o){var t;(t=R.value)==null||t.refresh({type:o,page:1})}},options:F}},{label:"图纸标题",prop:"keyword",props:{labelWidth:"80px"},component:{name:"el-input",props:{clearable:!1,onChange(o){var t;(t=R.value)==null||t.refresh({keyword:o.trim(),page:1})}}}}]});function he(o){y.value=o,Z(o.id)}async function Z(o){try{M.value=!0;const t=await v.pms.drawing.request({url:"/getProductsByDrawingId",method:"POST",data:{drawingId:o}});q.value=t||[]}catch(t){console.error("加载图纸产品失败:",t)}finally{M.value=!1}}async function me(o){var t,n;try{await Ze.confirm(`确定要移除产品 "${((t=o.product)==null?void 0:t.sku)||o.sku}" 的关联吗？`,"移除关联确认",{confirmButtonText:"确定移除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"}),await v.pms.drawing.request({url:"/removeDrawingProduct",method:"POST",data:{id:o.id}}),S.success("移除图纸产品成功")}catch(i){i!=="cancel"&&(console.error("移除图纸产品失败:",i),S.error("移除图纸产品失败"))}finally{Z((n=y.value)==null?void 0:n.id)}}async function U(o={}){var t;try{N.value=!0;const{page:n=h.value.page,keyword:i=D.value}=o,k=await v.pms.product.request({url:"/page",method:"POST",data:{page:n,size:h.value.size,keyword:i}});G=!0,E.value=k.list||[],h.value.total=((t=k.pagination)==null?void 0:t.total)||k.total||0,h.value.page=n,je(()=>{be()})}catch(n){console.error("加载产品列表失败:",n)}finally{N.value=!1}}U();async function fe(){var t;if(m.value.length===0)return;const o=m.value.map(n=>n.id);await v.pms.drawing.request({url:"/saveProducts",method:"POST",data:{drawingId:y.value.id,productIds:o}}),L.value=[],m.value=[],B.value=!1,S.success("设置成功"),Z((t=y.value)==null?void 0:t.id)}function ge(o){if(G)return;const t=E.value.map(n=>n.id);m.value=m.value.filter(n=>!t.includes(n.id)),o.forEach(n=>{m.value.push(n)}),L.value=m.value.map(n=>n.id)}function J(){h.value.page=1,U({page:1,keyword:D.value})}function we(o){U({page:o,keyword:D.value})}function ye(o){h.value.size=o,h.value.page=1,U({page:1,keyword:D.value})}function be(){O.value&&(O.value.clearSelection(),E.value.forEach(o=>{L.value.includes(o.id)&&O.value.toggleRowSelection(o,!0)}),G=!1)}function ke(){D.value="",h.value.page=1,m.value=q.value.map(o=>{var t,n,i;return{id:o.productId,sku:((t=o.product)==null?void 0:t.sku)||"",name:((n=o.product)==null?void 0:n.name)||"",color:((i=o.product)==null?void 0:i.color)||"",...o.product}}),L.value=q.value.map(o=>o.productId),U({page:1,keyword:""})}function Ce(o){var t;A.value=!0,(t=H.value)==null||t.edit(o)}function Pe(o){c.value=o,W.value=!0}async function xe(o,t,n){try{const i=await v.pms.drawing.request({url:"/download",method:"GET",params:{id:o},responseType:"blob"});ce(i)&&S.success("下载成功")}catch(i){console.error("下载失败:",i),S.error("下载失败")}}async function De(o,t,n){try{const i=await v.pms.drawing.request({url:"/downloadHistory",method:"GET",params:{id:o},responseType:"blob"});ce(i)&&S.success("下载成功")}catch(i){console.error("下载失败:",i),S.error("下载失败")}}function Se(){W.value=!1,c.value=null}function Te(o){var t;T.value=!0,(t=H.value)==null||t.edit(o)}function Ve(o){Pe(o)}return(o,t)=>{const n=p("cl-refresh-btn"),i=p("cl-add-btn"),k=p("cl-flex1"),ze=p("cl-search"),Q=p("el-row"),f=p("el-button"),$e=p("cl-table"),Be=p("cl-pagination"),Ie=p("cl-upsert"),Ue=p("cl-crud"),qe=p("el-tag"),C=p("el-table-column"),se=p("el-table"),Ee=p("el-input"),Le=p("el-pagination"),le=p("el-dialog"),w=p("el-icon"),ae=p("Document"),V=ie("permission"),ne=ie("loading");return d(),g("div",Xe,[e("div",Ye,[s(Ue,{ref_key:"Crud",ref:R},{default:l(()=>[s(Q,null,{default:l(()=>[s(n),P(s(i,null,null,512),[[V,r(v).pms.drawing.permission.add]]),s(k),s(ze,{ref_key:"Search",ref:ve},null,512)]),_:1}),s(Q,{style:{"margin-top":"10px"}},{default:l(()=>[s($e,{ref_key:"Table",ref:_e,onRowClick:he},{"slot-btn-update":l(({scope:a})=>[P((d(),b(f,{type:"success",onClick:z=>Ce(a.row)},{default:l(()=>[x(" 编辑 ")]),_:2},1032,["onClick"])),[[V,r(v).pms.drawing.permission.update]])]),"slot-btn-upload":l(({scope:a})=>[P((d(),b(f,{type:"primary",onClick:z=>Te(a.row)},{default:l(()=>[x(" 更新图纸 ")]),_:2},1032,["onClick"])),[[V,r(v).pms.drawing.permission.update]])]),"slot-btn-download":l(({scope:a})=>[P((d(),b(f,{type:"warning",onClick:z=>Ve(a.row)},{default:l(()=>[x(" 下载图纸 ")]),_:2},1032,["onClick"])),[[V,r(v).pms.drawing.permission.download]])]),_:1},512)]),_:1}),s(Q,null,{default:l(()=>[s(k),s(Be)]),_:1}),s(Ie,{ref_key:"Upsert",ref:H},null,512)]),_:1},512)]),e("div",et,[e("div",tt,[e("div",ot,[y.value?(d(),g("h3",st,_(y.value.title)+" - 关联产品 ",1)):(d(),g("h3",lt," 请选择图纸查看关联产品 ")),y.value?(d(),b(qe,{key:2,type:"warning"},{default:l(()=>[x(" 版本: V"+_(y.value.version),1)]),_:1})):$("",!0)]),e("div",at,[y.value?P((d(),b(f,{key:0,type:"success",size:"small",onClick:t[0]||(t[0]=a=>B.value=!0)},{default:l(()=>[x(" 设置产品关联 ")]),_:1})),[[V,r(v).pms.drawing.permission.saveProducts]]):$("",!0)])]),P((d(),g("div",nt,[s(se,{data:q.value,border:"",height:"100%","empty-text":"暂无关联产品"},{default:l(()=>[s(C,{prop:"product.sku",label:"SKU","min-width":"150"}),s(C,{prop:"product.name",label:"产品名称","min-width":"200","show-overflow-tooltip":""}),s(C,{prop:"product.color",label:"颜色","min-width":"120",align:"center"},{default:l(a=>[e("span",null,_(r(re)(r(ee),parseInt(a.row.product.color))),1)]),_:1}),s(C,{label:"操作",width:"120",fixed:"right"},{default:l(({row:a})=>[P((d(),b(f,{type:"danger",size:"small",onClick:z=>me(a)},{default:l(()=>[x(" 移除关联 ")]),_:2},1032,["onClick"])),[[V,r(v).pms.drawing.permission.removeDrawingProduct]])]),_:1})]),_:1},8,["data"])])),[[ne,M.value]])]),s(le,{modelValue:B.value,"onUpdate:modelValue":t[5]||(t[5]=a=>B.value=a),title:"选择关联产品",width:"1200px","close-on-click-modal":!1,onOpen:ke},{footer:l(()=>[e("div",ct,[e("div",ut,[m.value.length>0?(d(),g("span",pt," 已选择 "+_(m.value.length)+" 个产品 ",1)):$("",!0)]),e("div",_t,[s(f,{onClick:t[4]||(t[4]=a=>B.value=!1)},{default:l(()=>[x(" 取消 ")]),_:1}),s(f,{type:"primary",disabled:m.value.length===0,onClick:fe},{default:l(()=>[x(" 确认设置 ("+_(m.value.length)+") ",1)]),_:1},8,["disabled"])])])]),default:l(()=>[e("div",it,[e("div",dt,[s(Ee,{modelValue:D.value,"onUpdate:modelValue":t[1]||(t[1]=a=>D.value=a),placeholder:"请输入产品SKU或名称进行搜索",clearable:"",style:{width:"300px"},onKeyup:We(J,["enter"]),onClear:J},{append:l(()=>[s(f,{icon:"Search",onClick:J})]),_:1},8,["modelValue"])]),P((d(),b(se,{ref_key:"productTableRef",ref:O,data:E.value,border:"",height:"500px",onSelectionChange:ge},{default:l(()=>[s(C,{type:"selection",width:"55"}),s(C,{prop:"sku",label:"SKU","min-width":"150"}),s(C,{prop:"name",label:"产品名称","min-width":"200","show-overflow-tooltip":""}),s(C,{prop:"color",label:"颜色","min-width":"220",align:"center"},{default:l(a=>[e("span",null,_(r(re)(r(ee),parseInt(a.row.color))),1)]),_:1})]),_:1},8,["data"])),[[ne,N.value]]),e("div",rt,[s(Le,{"current-page":h.value.page,"onUpdate:currentPage":t[2]||(t[2]=a=>h.value.page=a),"page-size":h.value.size,"onUpdate:pageSize":t[3]||(t[3]=a=>h.value.size=a),"page-sizes":[20,50,100,200],total:h.value.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ye,onCurrentChange:we},null,8,["current-page","page-size","total"])])])]),_:1},8,["modelValue"]),s(le,{modelValue:W.value,"onUpdate:modelValue":t[8]||(t[8]=a=>W.value=a),width:"800px","close-on-click-modal":!1,"show-close":!1,class:"premium-download-dialog","destroy-on-close":""},{header:l(()=>[e("div",vt,[ht,e("div",mt,[e("div",ft,[e("div",gt,[e("div",wt,[s(w,null,{default:l(()=>[s(r(X))]),_:1})]),yt]),e("div",bt,[kt,c.value?(d(),g("p",Ct,_(c.value.title),1)):$("",!0)])]),e("div",Pt,[s(f,{type:"text",class:"premium-close-btn",onClick:Se},{default:l(()=>[s(w,null,{default:l(()=>[s(r(Ge))]),_:1})]),_:1})])])])]),default:l(()=>[e("div",xt,[c.value?(d(),g("div",Dt,[e("div",St,[e("div",Tt,[e("div",Vt,[e("div",zt,[e("div",$t,[s(w,null,{default:l(()=>[s(r(Re))]),_:1})]),Bt]),e("div",It,[e("span",Ut,"v"+_(c.value.version),1),e("span",qt,_(j(c.value.updateTime)),1)])]),e("div",Et,[e("div",Lt,[s(w,null,{default:l(()=>[s(ae)]),_:1})]),Ot])]),e("div",Wt,[e("h3",Rt,_(c.value.title),1),e("p",Kt,_(c.value.description),1),e("div",Ft,[s(f,{type:"primary",size:"large",class:"primary-download-btn",onClick:t[6]||(t[6]=a=>xe(c.value.id,c.value.title,c.value.version))},{default:l(()=>[e("div",Mt,[s(w,{class:"download-icon"},{default:l(()=>[s(r(X))]),_:1}),Nt]),At]),_:1}),e("div",Gt,[e("div",jt,[s(w,null,{default:l(()=>[s(r(de))]),_:1}),e("span",null,"更新于 "+_(j(c.value.updateTime)),1)]),e("div",Ht,[s(w,null,{default:l(()=>[s(ae)]),_:1}),Zt])])])])]),c.value.historyVersions&&c.value.historyVersions.length>0?(d(),g("div",Jt,[e("div",Qt,[e("div",Xt,[e("div",Yt,[s(w,null,{default:l(()=>[s(r(de))]),_:1})]),e("div",eo,[to,e("p",oo," 共 "+_(c.value.historyVersions.length)+" 个历史版本可供下载 ",1)])]),e("div",so,[s(f,{type:"text",class:"toggle-btn",onClick:t[7]||(t[7]=a=>I.value=!I.value)},{default:l(()=>[e("span",null,_(I.value?"收起":"展开全部"),1),s(w,null,{default:l(()=>[I.value?(d(),b(r(Ke),{key:0})):(d(),b(r(Fe),{key:1}))]),_:1})]),_:1})])]),e("div",lo,[(d(!0),g(Me,null,Ne(oe.value,(a,z)=>(d(),g("div",{key:a.id,class:"timeline-item animate-in",style:He({animationDelay:`${z*.1}s`})},[e("div",ao,[no,z<oe.value.length-1?(d(),g("div",io)):$("",!0)]),e("div",ro,[e("div",co,[e("div",uo,[e("div",po,[e("span",_o,"v"+_(a.version),1),e("span",vo,_(j(a.createTime)),1)]),s(f,{size:"small",class:"version-download-btn",onClick:xo=>De(a.id,a.title,a.version)},{default:l(()=>[s(w,null,{default:l(()=>[s(r(X))]),_:1}),ho]),_:2},1032,["onClick"])]),e("div",mo,[e("h5",fo,_(a.title),1),e("p",go,_(a.description),1)])])])],4))),128))])])):(d(),g("div",wo,[e("div",yo,[s(w,null,{default:l(()=>[s(r(Ae))]),_:1})]),bo,ko]))])):$("",!0)])]),_:1},8,["modelValue"])])}}});export{Vo as default};
