import{b as V}from"./index-BuqCFB-b.js";import{c as _,k as m,l as k,n as w,e as y,q as a,i as p,m as x,v as f,w as B,f as C,s as F,F as M,o as n}from"./.pnpm-Kv7TmmH8.js";const S=_({name:"select-dict"}),A=_({...S,props:m({width:{default:"150px"},code:{},productFloor:{type:Boolean,default:!1}},{modelValue:{},modelModifiers:{}}),emits:m(["change"],["update:modelValue"]),setup(r,{emit:g}){const t=r,c=g,h=k(),u=w(r,"modelValue"),d=V().getDictMap(""),i=y(()=>{if(t.productFloor){const e=d.product_floor.find(o=>o.id===Number(t.code));return(e==null?void 0:e.children)||[]}return t.code?d[t.code]:[]});function b(e){if(e===void 0){c("change",e);return}const o=i.value.find(s=>s.id===e);c("change",o)}return(e,o)=>{const s=p("el-option"),v=p("el-select");return n(),a(v,x({modelValue:u.value,"onUpdate:modelValue":o[0]||(o[0]=l=>u.value=l)},f(h),{clearable:"",style:`width:${t.width}`,onChange:b}),{default:B(()=>[(n(),a(s,{key:0,label:"请选择",value:0})),(n(!0),C(M,null,F(f(i),l=>(n(),a(s,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},16,["modelValue","style"])}}});export{A as _};
