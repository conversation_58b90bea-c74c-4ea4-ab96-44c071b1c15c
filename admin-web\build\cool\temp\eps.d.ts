declare namespace Eps {
	interface BaseCommInfoEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseOpenInfoEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysDepartmentInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * name
		 */
		name?: string;
		/**
		 * parentId
		 */
		parentId?: number;
		/**
		 * orderNum
		 */
		orderNum?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysLogInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * userId
		 */
		userId?: bigint;
		/**
		 * action
		 */
		action?: string;
		/**
		 * ip
		 */
		ip?: string;
		/**
		 * ipAddr
		 */
		ipAddr?: string;
		/**
		 * params
		 */
		params?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysMenuInfoEntity {
		/**
		 * id
		 */
		id?: bigint;
		/**
		 * parentId
		 */
		parentId?: number;
		/**
		 * name
		 */
		name?: string;
		/**
		 * router
		 */
		router?: string;
		/**
		 * perms
		 */
		perms?: string;
		/**
		 * type
		 */
		type?: number;
		/**
		 * icon
		 */
		icon?: string;
		/**
		 * orderNum
		 */
		orderNum?: number;
		/**
		 * viewPath
		 */
		viewPath?: string;
		/**
		 * keepAlive
		 */
		keepAlive?: number;
		/**
		 * isShow
		 */
		isShow?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysParamInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * keyName
		 */
		keyName?: string;
		/**
		 * name
		 */
		name?: string;
		/**
		 * data
		 */
		data?: string;
		/**
		 * dataType
		 */
		dataType?: number;
		/**
		 * remark
		 */
		remark?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysRoleInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * userId
		 */
		userId?: string;
		/**
		 * name
		 */
		name?: string;
		/**
		 * label
		 */
		label?: string;
		/**
		 * remark
		 */
		remark?: string;
		/**
		 * relevance
		 */
		relevance?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysUserInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * departmentId
		 */
		departmentId?: number;
		/**
		 * name
		 */
		name?: string;
		/**
		 * username
		 */
		username?: string;
		/**
		 * password
		 */
		password?: string;
		/**
		 * passwordV
		 */
		passwordV?: number;
		/**
		 * nickName
		 */
		nickName?: string;
		/**
		 * headImg
		 */
		headImg?: string;
		/**
		 * phone
		 */
		phone?: string;
		/**
		 * email
		 */
		email?: string;
		/**
		 * status
		 */
		status?: number;
		/**
		 * remark
		 */
		remark?: string;
		/**
		 * socketId
		 */
		socketId?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface DictInfoEntity {
		/**
		 * id
		 */
		id?: bigint;
		/**
		 * typeId
		 */
		typeId?: number;
		/**
		 * name
		 */
		name?: string;
		/**
		 * orderNum
		 */
		orderNum?: number;
		/**
		 * remark
		 */
		remark?: string;
		/**
		 * parentId
		 */
		parentId?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface DictTypeInfoEntity {
		/**
		 * id
		 */
		id?: bigint;
		/**
		 * name
		 */
		name?: string;
		/**
		 * key
		 */
		key?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PimsAutidInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 标题
		 */
		title?: string;
		/**
		 * 工作项id
		 */
		workitemId?: number;
		/**
		 * 根ID
		 */
		rootId?: number;
		/**
		 * 业务id
		 */
		businessId?: number;
		/**
		 * 类型 1: 任务 2: 日报
		 */
		type?: number;
		/**
		 * 审批人
		 */
		approver?: string;
		/**
		 * 是否同意
		 */
		agree?: char;
		/**
		 * 审批意见
		 */
		opinion?: string;
		/**
		 * 抄送人ID
		 */
		ccUserIds?: string;
		/**
		 * 完成时间
		 */
		finishTime?: Date;
		/**
		 * 是否作废 1 是 0否
		 */
		isRepeal?: number;
		/**
		 * 创建人ID
		 */
		creatorId?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PimsConfigInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 创建人ID
		 */
		creatorId?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 更新人ID
		 */
		updaterId?: number;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 配置模块
		 */
		configModule?: string;
		/**
		 * 配置名称
		 */
		configName?: string;
		/**
		 * 配置类型
		 */
		configType?: number;
		/**
		 * 配置值
		 */
		configValue?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PimsDaliyReportInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 工作事项
		 */
		jobDescription?: string;
		/**
		 * job_detail
		 */
		jobDetail?: string;
		/**
		 * 工作时间范围
		 */
		workTimeRange?: string;
		/**
		 * 工时
		 */
		estimatedWorkNum?: number;
		/**
		 * 根ID
		 */
		rootId?: number;
		/**
		 * 工作项id
		 */
		workitemId?: number;
		/**
		 * 文件路径
		 */
		filePath?: string;
		/**
		 * 创建人ID
		 */
		creatorId?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删��时间
		 */
		deleteTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 工作类型
		 */
		workType?: number;
		/**
		 * 抄送人ID
		 */
		ccUserIds?: string;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 是否暂存
		 */
		isTemp?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PimsProgressInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 工作项id
		 */
		workitemId?: number;
		/**
		 * 根节点id
		 */
		rootId?: number;
		/**
		 * 节点名称
		 */
		nodeName?: string;
		/**
		 * 描述
		 */
		desc?: string;
		/**
		 * 进度
		 */
		progress?: number;
		/**
		 * 创建人ID
		 */
		creatorId?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PimsWorkitemInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 标题
		 */
		title?: string;
		/**
		 * 内容
		 */
		content?: string;
		/**
		 * 根ID
		 */
		rootId?: number;
		/**
		 * 层级
		 */
		level?: number;
		/**
		 * 父级ID
		 */
		parentId?: number;
		/**
		 * type
		 */
		type?: number;
		/**
		 * 负责人
		 */
		responsiblePersonId?: number;
		/**
		 * 优先级
		 */
		priority?: number;
		/**
		 * 计划开始时间
		 */
		planStartTime?: Date;
		/**
		 * 计划结束时间
		 */
		planEndTime?: Date;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 审批状态
		 */
		auditStatus?: number;
		/**
		 * 创建者
		 */
		creatorId?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 归档 0 未归档 1 已归档
		 */
		archive?: number;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 工期
		 */
		workDay?: number;
		/**
		 * 进度
		 */
		progress?: number;
		/**
		 * 实际完成时间
		 */
		actualEndTime?: Date;
		/**
		 * 日报ID
		 */
		daliyReportId?: string;
		/**
		 * 审批人
		 */
		approver?: string;
		/**
		 * 是否按顺序审批
		 */
		isOrderApprove?: number;
		/**
		 * 排序
		 */
		sortIndex?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PimsWorkitemAttachmentInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 工作项ID
		 */
		workitemId?: number;
		/**
		 * 根ID
		 */
		rootId?: number;
		/**
		 * 父级ID
		 */
		pid?: number;
		/**
		 * 原始的文件名
		 */
		fileName?: string;
		/**
		 * 创建人ID
		 */
		creatorId?: number;
		/**
		 * 文件名
		 */
		name?: string;
		/**
		 * 文件路径
		 */
		path?: string;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PimsWorkitemLogInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 工作项ID
		 */
		workitemId?: number;
		/**
		 * 根ID
		 */
		rootId?: number;
		/**
		 * 父级ID
		 */
		parentId?: number;
		/**
		 * 日报ID
		 */
		daliyReportId?: number;
		/**
		 * 类型 动态 1: 评论 2 任务审批记录 3 日报审批记录
		 */
		type?: number;
		/**
		 * 是否置顶
		 */
		isTop?: number;
		/**
		 * 内容
		 */
		content?: string;
		/**
		 * 是否已读，注意这里是反的）是否已读 1: 未读 0: 已读
		 */
		isRead?: number;
		/**
		 * 父级ID
		 */
		pid?: number;
		/**
		 * 创建人ID
		 */
		creatorId?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsAbnormalWorkingHoursInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 生产订单id
		 */
		orderId?: number;
		/**
		 * 订单数量
		 */
		orderQuantity?: number;
		/**
		 * 生产数量
		 */
		quantity?: number;
		/**
		 * 异常日期
		 */
		abnormalDate?: Date;
		/**
		 * ProductId
		 */
		productId?: number;
		/**
		 * 工段
		 */
		workshopSection?: number;
		/**
		 * 生产阶段
		 */
		productionStages?: number;
		/**
		 * SKU
		 */
		sku?: string;
		/**
		 * Description
		 */
		description?: string;
		/**
		 * accountability_unit
		 */
		accountabilityUnit?: string;
		/**
		 * 人数
		 */
		numberOfPeople?: number;
		/**
		 * 工时
		 */
		manHour?: number;
		/**
		 * 人均工时
		 */
		averageWorkingHours?: number;
		/**
		 * 人工费用
		 */
		laborCost?: number;
		/**
		 * 累计费用
		 */
		totalCost?: number;
		/**
		 * 备注
		 */
		reMark?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsDailyProductionReportInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * ProductId
		 */
		productId?: number;
		/**
		 * SKU
		 */
		sku?: string;
		/**
		 * 生产订单id
		 */
		orderId?: number;
		/**
		 * 订单数量
		 */
		quantity?: number;
		/**
		 * 人数
		 */
		numberOfPeople?: number;
		/**
		 * 工时
		 */
		manHour?: number;
		/**
		 * 日产量
		 */
		dailyOutput?: number;
		/**
		 * 生产阶段
		 */
		productionStages?: number;
		/**
		 * 工段
		 */
		workshopSection?: number;
		/**
		 * 备注
		 */
		reMark?: string;
		/**
		 * 生产日期
		 */
		producedDate?: Date;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 工段
		 */
		workshopId?: number;
		/**
		 * 审核状态
		 */
		isAudit?: number;
		/**
		 * 作业人员
		 */
		busyworkGroup?: string;
		/**
		 * 产线
		 */
		productionLine?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsPartsCapacityInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * sku
		 */
		sku?: string;
		/**
		 * product_name
		 */
		productName?: string;
		/**
		 * Description
		 */
		description?: string;
		/**
		 * 工段
		 */
		workshopSection?: number;
		/**
		 * 试产产能
		 */
		pilotProductionCapacity?: number;
		/**
		 * 首次量产产能
		 */
		firstMassProductionCapacity?: number;
		/**
		 * 量产产能
		 */
		massProductionCapacity?: number;
		/**
		 * 备注
		 */
		reMark?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsStandardCapacityInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * ProductId
		 */
		productId?: number;
		/**
		 * SKU
		 */
		sku?: string;
		/**
		 * Description
		 */
		description?: string;
		/**
		 * 工段
		 */
		workshopSection?: number;
		/**
		 * 试产产能
		 */
		pilotProductionCapacity?: number;
		/**
		 * 首次量产产能
		 */
		firstMassProductionCapacity?: number;
		/**
		 * 量产产能
		 */
		massProductionCapacity?: number;
		/**
		 * 备注
		 */
		reMark?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * ProductGroupId
		 */
		productGroupId?: number;
		/**
		 * ProductIds
		 */
		productIds?: string;
		/**
		 * SKUS
		 */
		skus?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsAllDataCompareInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 列名称
		 */
		name?: string;
		/**
		 * 列类型
		 */
		type?: number;
		/**
		 * 处理器
		 */
		handler?: string;
		/**
		 * 行号
		 */
		row?: number;
		/**
		 * 列号
		 */
		col?: number;
		/**
		 * 模板ID
		 */
		templateId?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 区块标题
		 */
		blockTitle?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsAuditProcessInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 流程名称
		 */
		name?: string;
		/**
		 * 流程图标
		 */
		icon?: string;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 流程key
		 */
		key?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsAuditProcessorInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 流程ID
		 */
		processId?: number;
		/**
		 * 审核ID
		 */
		auditId?: number;
		/**
		 * 完成时间
		 */
		completeTime?: Date;
		/**
		 * 状态 0: 等待处理 1: 处理中 2: 已完成 3: 已驳回
		 */
		status?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsAuditProcessorNodeInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 审核ID
		 */
		auditId?: number;
		/**
		 * 流程ID
		 */
		processId?: number;
		/**
		 * 处理ID
		 */
		processorId?: number;
		/**
		 * 步骤
		 */
		step?: number;
		/**
		 * 节点值
		 */
		nodeValue?: number;
		/**
		 * 节点类型
		 */
		nodeType?: number;
		/**
		 * 处理人ID
		 */
		userId?: number;
		/**
		 * 处理人名称
		 */
		userName?: string;
		/**
		 * 节点ID
		 */
		nodeId?: number;
		/**
		 * 节点名称
		 */
		nodeName?: string;
		/**
		 * 展示名称
		 */
		displayName?: string;
		/**
		 * 处理状态 0: 不可用 1: 等待处理 2: 处理中 3: 已处理
		 */
		status?: number;
		/**
		 * 处理时间
		 */
		dealTime?: Date;
		/**
		 * 处理结果
		 */
		result?: number;
		/**
		 * 处理备注
		 */
		remark?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsBomInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * BOM单编号
		 */
		sn?: string;
		/**
		 * 产品ID
		 */
		productId?: number;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 版本
		 */
		version?: number;
		/**
		 * 最后更新时间
		 */
		lastUpdateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsClearanceOrderInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 订单ID
		 */
		orderId?: number;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 货运单ID
		 */
		forwarderOrderId?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsDailyReportDataInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 数据组
		 */
		groupId?: number;
		/**
		 * 生产日期
		 */
		producedDate?: Date;
		/**
		 * 生产订单id
		 */
		orderId?: number;
		/**
		 * 订单数量
		 */
		quantity?: number;
		/**
		 * ProductId
		 */
		productId?: number;
		/**
		 * SKU
		 */
		sku?: string;
		/**
		 * 生产车间
		 */
		workshopId?: number;
		/**
		 * 产线
		 */
		productionLine?: string;
		/**
		 * 作业人员
		 */
		busyworkGroup?: string;
		/**
		 * 生产阶段
		 */
		productionStages?: number;
		/**
		 * 工序
		 */
		workshopSection?: number;
		/**
		 * 工时
		 */
		manHour?: number;
		/**
		 * 合计工时
		 */
		totalManHour?: number;
		/**
		 * 当日产能
		 */
		dailyOutput?: number;
		/**
		 * 当日产能总计
		 */
		totalDailyOutput?: number;
		/**
		 * 人均产能
		 */
		averageCapacity?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 人数
		 */
		numberOfPeople?: number;
		/**
		 * 合计人数
		 */
		totalNumberOfPeople?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsDataDiffInfoEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsDeliveryNoteInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 入库单号
		 */
		no?: string;
		/**
		 * po号
		 */
		po?: string;
		/**
		 * 供应商单号
		 */
		supplierOrderNo?: string;
		/**
		 * 入库完成时间
		 */
		completeTime?: Date;
		/**
		 * 入库时间
		 */
		inboundTime?: Date;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 凭证
		 */
		voucher?: string;
		/**
		 * 供应商id
		 */
		supplierId?: number;
		/**
		 * 采购单订单ID
		 */
		orderId?: number;
		/**
		 * 总数量
		 */
		totalQuantity?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsDrawingInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 名称
		 */
		title?: string;
		/**
		 * 类别
		 */
		type?: number;
		/**
		 * 图纸
		 */
		drawingUrl?: string;
		/**
		 * 版本
		 */
		version?: number;
		/**
		 * Description
		 */
		description?: string;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsFinanceInfoEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsFinanceBillPaymentInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 付款日期
		 */
		paymentDate?: Date;
		/**
		 * 货款结算开始日期
		 */
		startDate?: Date;
		/**
		 * 货款结算结束日期
		 */
		endDate?: Date;
		/**
		 * 付款银行
		 */
		payingBank?: string;
		/**
		 * 收款银行
		 */
		recipientBank?: string;
		/**
		 * 收款单位ID
		 */
		supplierId?: number;
		/**
		 * 供应商名称
		 */
		supplierName?: string;
		/**
		 * 摘要
		 */
		description?: string;
		/**
		 * 凭证
		 */
		voucher?: string;
		/**
		 * 银行流水号
		 */
		serialNumber?: string;
		/**
		 * 付款账号
		 */
		paymentAccount?: string;
		/**
		 * 收款账号
		 */
		shroffAccount?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 付款金额
		 */
		amount?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsFreightForwarderInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 公司名
		 */
		name?: string;
		/**
		 * 地址
		 */
		address?: string;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsFreightForwarderOrderInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 订单ID
		 */
		orderId?: number;
		/**
		 * 货代ID
		 */
		forwarderId?: number;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * deleteTime
		 */
		deleteTime?: Date;
		/**
		 * 父订单ID
		 */
		parentId?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsJobInstructionInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 文件标题
		 */
		title?: string;
		/**
		 * 文件地址
		 */
		url?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsMaterialInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 代码
		 */
		code?: string;
		/**
		 * 名称
		 */
		name?: string;
		/**
		 * 型号
		 */
		model?: string;
		/**
		 * 尺寸
		 */
		size?: string;
		/**
		 * 材质
		 */
		material?: string;
		/**
		 * 工艺
		 */
		process?: string;
		/**
		 * 封面图片颜色
		 */
		coverColor?: string;
		/**
		 * 单位
		 */
		unit?: string;
		/**
		 * 库存
		 */
		inventory?: number;
		/**
		 * 在途库存
		 */
		expectedInbound?: number;
		/**
		 * 最低起订量
		 */
		minimumOrderQuantity?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 绑定的用户
		 */
		bindUserId?: number;
		/**
		 * 锁定库存
		 */
		lockedInventory?: number;
		/**
		 * 占用库存
		 */
		occupiedInventory?: number;
		/**
		 * 可抵扣预计入库量
		 */
		deductibleExpectedInbound?: number;
		/**
		 * 使用预计入库量
		 */
		usedExpectedInbound?: number;
		/**
		 * 已使用数量
		 */
		usedQuantity?: number;
		/**
		 * 物料位置ID
		 */
		addressId?: number;
		/**
		 * 物料位置
		 */
		addressName?: string;
		/**
		 * 关键字ID
		 */
		inboundOutboundKey?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsMaterialBomChangeLogInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 物料ID
		 */
		materialId?: number;
		/**
		 * BOM单ID
		 */
		bomId?: number;
		/**
		 * BOM 版本
		 */
		bomVersion?: number;
		/**
		 * 操作类型
		 */
		operationType?: number;
		/**
		 * 操作人ID
		 */
		operatorId?: number;
		/**
		 * 操作人名称
		 */
		operatorName?: string;
		/**
		 * 操作时间
		 */
		operationTime?: Date;
		/**
		 * 操作内容
		 */
		operationContent?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsMaterialBundleInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 名称
		 */
		name?: string;
		/**
		 * 代码
		 */
		code?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsMaterialDrawingInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 物料ID
		 */
		materialId?: number;
		/**
		 * 图纸名称
		 */
		name?: string;
		/**
		 * 图纸路径
		 */
		path?: string;
		/**
		 * 图纸类型
		 */
		type?: number;
		/**
		 * 图纸版本
		 */
		version?: number;
		/**
		 * 图纸状态
		 */
		status?: number;
		/**
		 * 图纸备注
		 */
		remark?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 是否默认
		 */
		isDefault?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsMaterialInboundInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 入库单号
		 */
		no?: string;
		/**
		 * 入库完成时间
		 */
		completeTime?: Date;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 凭证
		 */
		voucher?: string;
		/**
		 * 是否为订单
		 */
		isOrder?: number;
		/**
		 * 采购单订单ID
		 */
		orderId?: number;
		/**
		 * 总数量
		 */
		totalQuantity?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 入库类型
		 */
		type?: number;
		/**
		 * 入库时间
		 */
		inboundTime?: Date;
		/**
		 * 是否提交审批
		 */
		isSubmit?: number;
		/**
		 * 工单ID
		 */
		workOrderId?: number;
		/**
		 * 供货单号
		 */
		deliveryNoteId?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsMaterialOutboundInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 出库完成时间
		 */
		completeTime?: Date;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 总数量
		 */
		totalQuantity?: number;
		/**
		 * 是否为库存调整
		 */
		isAdjust?: number;
		/**
		 * 出库单凭证
		 */
		voucher?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 出库单号
		 */
		no?: string;
		/**
		 * 类型
		 */
		type?: number;
		/**
		 * 订单ID
		 */
		orderId?: number;
		/**
		 * 出库时间
		 */
		outboundTime?: Date;
		/**
		 * 是否提交审批
		 */
		isSubmit?: number;
		/**
		 * 工单号
		 */
		workOrderNo?: string;
		/**
		 * 工单ID
		 */
		workOrderId?: number;
		/**
		 * 产品ID
		 */
		productId?: number;
		/**
		 * 是否完成补货
		 */
		isReplenish?: number;
		/**
		 * 生产单号
		 */
		productionScheduleSn?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsMaterialPriceInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 物料ID
		 */
		materialId?: number;
		/**
		 * 供应商ID
		 */
		supplierId?: number;
		/**
		 * 价格
		 */
		price?: number;
		/**
		 * 价格类型 0->采购下单 1->人工录入
		 */
		type?: number;
		/**
		 * 关联的采购订单ID
		 */
		referenceId?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsMaterialStockLogInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 物料ID
		 */
		materialId?: number;
		/**
		 * 变更前数量
		 */
		before?: number;
		/**
		 * 变更后数量
		 */
		after?: number;
		/**
		 * 变更类型 0: 可用库存 1: 在途库存 2: 冻结库存 ,3在途，4库存，
		 */
		type?: number;
		/**
		 * 变更数量
		 */
		quantity?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 合同ID
		 */
		contractId?: number;
		/**
		 * 创建人ID
		 */
		creatorId?: number;
		/**
		 * 生产单
		 */
		productionScheduleId?: number;
		/**
		 * 生产单SN
		 */
		productionScheduleSn?: string;
		/**
		 * 采购单
		 */
		purchaseOrderId?: number;
		/**
		 * 采购单SN
		 */
		purchaseOrderSn?: string;
		/**
		 * 入库单
		 */
		warehouseInboundId?: number;
		/**
		 * 入库单SN
		 */
		warehouseInboundSn?: string;
		/**
		 * 出库单
		 */
		warehouseOutboundId?: number;
		/**
		 * 出库单SN
		 */
		warehouseOutboundSn?: string;
		/**
		 * 工单id
		 */
		workOrderId?: number;
		/**
		 * 工单号
		 */
		workOrderNo?: string;
		/**
		 * 操作类型 1采购下单，2入库-库存调整，3入库-生成退料，4自定义入库，5采购单入库，6领料出库，7退货出库，8自定义出库，9报废
		 */
		operationType?: number;
		/**
		 * 其他备注
		 */
		otherRemark?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsMaterialAnomalyInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 生产订单id
		 */
		orderId?: number;
		/**
		 * SKU
		 */
		sku?: string;
		/**
		 * 生产日期
		 */
		abnormalDate?: Date;
		/**
		 * 工序
		 */
		workshopSection?: number;
		/**
		 * Description
		 */
		description?: string;
		/**
		 * 责任单位
		 */
		accountabilityUnit?: string;
		/**
		 * 物料ID
		 */
		materialId?: number;
		/**
		 * 供应商ID
		 */
		supplierId?: number;
		/**
		 * 损耗数量
		 */
		quantity?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsMaterialTestInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 检测时间
		 */
		testDate?: Date;
		/**
		 * 不良数
		 */
		badQuantity?: number;
		/**
		 * 批量
		 */
		batchQuantity?: number;
		/**
		 * 抽检数
		 */
		checkQuantity?: number;
		/**
		 * 不良率
		 */
		rejectRatio?: number;
		/**
		 * 物料ID
		 */
		materialId?: number;
		/**
		 * 供应商ID
		 */
		supplierId?: number;
		/**
		 * 处理结果
		 */
		dispose?: string;
		/**
		 * 检验员
		 */
		inspector?: string;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 抽样方案
		 */
		samplingPlan?: string;
		/**
		 * 附图
		 */
		voucher?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsOutsourceInboundInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 入库单号
		 */
		no?: string;
		/**
		 * 入库完成时间
		 */
		completeTime?: Date;
		/**
		 * 入库时间
		 */
		inboundTime?: Date;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 凭证
		 */
		voucher?: string;
		/**
		 * 是否提交审批
		 */
		isSubmit?: number;
		/**
		 * 采购单订单ID
		 */
		orderId?: number;
		/**
		 * 总数量
		 */
		totalQuantity?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 工单ID
		 */
		workOrderId?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsPaymentInfoEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsProductInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 分组ID
		 */
		groupId?: number;
		/**
		 * SKU
		 */
		sku?: string;
		/**
		 * UPC
		 */
		upc?: string;
		/**
		 * 名称
		 */
		name?: string;
		/**
		 * 英文名称
		 */
		nameEn?: string;
		/**
		 * 长cm
		 */
		length?: number;
		/**
		 * 宽cm
		 */
		width?: number;
		/**
		 * 高cm
		 */
		height?: number;
		/**
		 * 重量kg
		 */
		weight?: number;
		/**
		 * 毛重kg
		 */
		grossWeight?: number;
		/**
		 * 颜色
		 */
		color?: number;
		/**
		 * 单位
		 */
		unit?: number;
		/**
		 * 包含的产品ID
		 */
		unitProductId?: number;
		/**
		 * 单位数量
		 */
		unitQuantity?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsProductGroupInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 分组名称
		 */
		name?: string;
		/**
		 * 分组英文名
		 */
		nameEn?: string;
		/**
		 * deleteTime
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsProductStockLogInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 产品ID
		 */
		productId?: number;
		/**
		 * 变化的数量
		 */
		amount?: number;
		/**
		 * 修改前的库存
		 */
		before?: number;
		/**
		 * 修改后的库存
		 */
		after?: number;
		/**
		 * 库存变化的类型
		 */
		stockType?: number;
		/**
		 * 类型
		 */
		type?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsProductionManHourDeductInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 扣款单编号
		 */
		no?: string;
		/**
		 * 事故物料供应商id
		 */
		supplierId?: number;
		/**
		 * 供应商名称
		 */
		supplierName?: string;
		/**
		 * 凭证
		 */
		voucher?: string;
		/**
		 * ProductId
		 */
		productId?: number;
		/**
		 * 不良物料ID
		 */
		badMaterialId?: number;
		/**
		 * 不良物料名称
		 */
		badMaterialName?: string;
		/**
		 * 事故时间
		 */
		accidentTime?: Date;
		/**
		 * 人数
		 */
		numberOfPeople?: number;
		/**
		 * 工时总计
		 */
		manHour?: number;
		/**
		 * 人工费率(元/h)
		 */
		laborRate?: number;
		/**
		 * 人均工时
		 */
		perCapitaWorkingHours?: number;
		/**
		 * 金额总计
		 */
		totalAmount?: number;
		/**
		 * 描述
		 */
		description?: string;
		/**
		 * 状态 0：待处理 1：已扣款
		 */
		status?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsProductionMaterialDeductInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 扣款单编号
		 */
		no?: string;
		/**
		 * 描述
		 */
		description?: string;
		/**
		 * 金额总计
		 */
		totalAmount?: number;
		/**
		 * 损耗数量
		 */
		quantity?: number;
		/**
		 * 事故物料供应商id
		 */
		supplierId?: number;
		/**
		 * 供应商名称
		 */
		supplierName?: string;
		/**
		 * 凭证
		 */
		voucher?: string;
		/**
		 * 物料ID
		 */
		materialId?: number;
		/**
		 * 物料名称
		 */
		materialName?: string;
		/**
		 * 单价
		 */
		unitPrice?: number;
		/**
		 * 事故时间
		 */
		accidentTime?: Date;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 不良物料ID
		 */
		badMaterialId?: number;
		/**
		 * 不良物料名称
		 */
		badMaterialName?: string;
		/**
		 * 状态 0：待处理 1：已扣款
		 */
		status?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsProductionPurchaseOrderInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 生产订单ID
		 */
		orderId?: number;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 采购订单ID
		 */
		purchaseId?: number;
		/**
		 * 内部订单号
		 */
		internalOrderNo?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsProductionSaleOrderInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 订单ID
		 */
		orderId?: number;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsProductionScheduleInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 生产单号
		 */
		sn?: string;
		/**
		 * 创建者ID
		 */
		creatorId?: number;
		/**
		 * 来源
		 */
		source?: number;
		/**
		 * 关联的订单ID
		 */
		referOrderId?: number;
		/**
		 * 合计数量
		 */
		totalQuantity?: number;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 预计交货时间
		 */
		expectedDeliveryTime?: Date;
		/**
		 * 实际交货时间
		 */
		actualDeliveryTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsProductionSchedulePlanInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 生产计划ID
		 */
		scheduleId?: number;
		/**
		 * 排产计划日期
		 */
		scheduleDate?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsProductionScheduleProductInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 生产单号
		 */
		sn?: string;
		/**
		 * 创建者ID
		 */
		creatorId?: number;
		/**
		 * 来源
		 */
		source?: number;
		/**
		 * 关联的订单ID
		 */
		referOrderId?: number;
		/**
		 * 合计数量
		 */
		totalQuantity?: number;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 预计交货时间
		 */
		expectedDeliveryTime?: Date;
		/**
		 * 实际交货时间
		 */
		actualDeliveryTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsProductionSchedulePurchaseOrderInfoEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsProductionDataIncomingInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 供应商ID
		 */
		supplierId?: number;
		/**
		 * 供应商名称
		 */
		supplierName?: string;
		/**
		 * 供应商简称
		 */
		supplierShortName?: string;
		/**
		 * 物料ID
		 */
		materialId?: number;
		/**
		 * 物料编号
		 */
		code?: string;
		/**
		 * 物料名称
		 */
		materialName?: string;
		/**
		 * 订单编号
		 */
		orderNo?: string;
		/**
		 * 订单ID
		 */
		orderId?: number;
		/**
		 * 交货日期
		 */
		deliveryDate?: Date;
		/**
		 * 交货数量
		 */
		deliveryQuantity?: number;
		/**
		 * 合格率
		 */
		qualifiedRate?: number;
		/**
		 * 检验员
		 */
		inspector?: string;
		/**
		 * 检验结果
		 */
		inspectionResult?: char;
		/**
		 * 异常描述
		 */
		abnormalDescription?: string;
		/**
		 * 异常图片
		 */
		abnormalPicture?: string;
		/**
		 * 临时措施
		 */
		temporaryMeasures?: string;
		/**
		 * 原因分析
		 */
		causeAnalysis?: string;
		/**
		 * 改善方案
		 */
		improvementPlan?: string;
		/**
		 * 完成状况
		 */
		completionStatus?: string;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 创建人ID
		 */
		creatorId?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 更新人ID
		 */
		updaterId?: number;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 产品ID
		 */
		modelId?: number;
		/**
		 * 产品名称
		 */
		modelChName?: string;
		/**
		 * 产品SKU
		 */
		modelSku?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsProductionDataProcessAbnormalityInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 发现日期
		 */
		discoveryDate?: Date;
		/**
		 * 线别
		 */
		line?: number;
		/**
		 * 供应商ID
		 */
		supplierId?: number;
		/**
		 * 供应商名称
		 */
		supplierName?: string;
		/**
		 * 供应商简称
		 */
		supplierShortName?: string;
		/**
		 * 机型
		 */
		model?: string;
		/**
		 * 机型ID
		 */
		modelId?: number;
		/**
		 * 物料名称
		 */
		materialName?: string;
		/**
		 * 物料ID
		 */
		materialId?: number;
		/**
		 * 物料编号
		 */
		code?: string;
		/**
		 * 异常描述
		 */
		abnormalDescription?: string;
		/**
		 * 不良率
		 */
		defectiveRate?: number;
		/**
		 * 异常图片
		 */
		abnormalPicture?: string;
		/**
		 * 临时措施
		 */
		temporaryMeasures?: string;
		/**
		 * 原因分析
		 */
		causeAnalysis?: string;
		/**
		 * 改善方案
		 */
		improvementPlan?: string;
		/**
		 * 责任人
		 */
		responsiblePerson?: number;
		/**
		 * 责任单位
		 */
		responsibleUnit?: number;
		/**
		 * 完成状况
		 */
		completionStatus?: string;
		/**
		 * 楼层
		 */
		floor?: number;
		/**
		 * 异常类型
		 */
		abnormalType?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 创建人ID
		 */
		creatorId?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 更新人ID
		 */
		updaterId?: number;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsPurchaseContractInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 采购单ID
		 */
		purchaseId?: number;
		/**
		 * 物料ID
		 */
		materialId?: number;
		/**
		 * 供应商ID
		 */
		supplierId?: number;
		/**
		 * 单价
		 */
		unitPrice?: number;
		/**
		 * 数量
		 */
		quantity?: number;
		/**
		 * 税率
		 */
		taxRate?: number;
		/**
		 * 交货日期
		 */
		deliveryDate?: Date;
		/**
		 * 已收数量
		 */
		receivedQuantity?: number;
		/**
		 * 小计
		 */
		subtotal?: number;
		/**
		 * 转单数量
		 */
		transfer?: number;
		/**
		 * 在途数量
		 */
		expectedQuantity?: number;
		/**
		 * PO号
		 */
		po?: string;
		/**
		 * 总备货数量
		 */
		preparesTotal?: number;
		/**
		 * 付款方式
		 */
		paymentTerm?: number;
		/**
		 * 虚拟订单
		 */
		virtualOrder?: number;
		/**
		 * 匹配的PO
		 */
		matchedPo?: string;
		/**
		 *  1:MatchedPo采购Excel数据有，系统没有,2：MatchedPo系统有,采购Excel数据没有，
		 */
		matchedPoType?: number;
		/**
		 * 总需求量
		 */
		totalDemand?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 创建人ID
		 */
		creatorId?: number;
		/**
		 * 付款状态
		 */
		paymentStatus?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsPurchaseOrderInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 订单号
		 */
		orderNo?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 步骤
		 */
		step?: number;
		/**
		 * 是否有警告
		 */
		hasWarning?: number;
		/**
		 * 父订单ID
		 */
		parentOrderId?: number;
		/**
		 * 订单类型
		 */
		orderType?: number;
		/**
		 * 虚拟订单
		 */
		virtualOrder?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsPurchaseOrderDetailInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 采购单ID
		 */
		purchaseId?: number;
		/**
		 * 采购生成数据的类型： 0: 通过产品生成 1: 物料生成
		 */
		generateType?: number;
		/**
		 * 采购生成数据的ID
		 */
		generateId?: number;
		/**
		 * 采购生成数据的数量
		 */
		generateQuantity?: number;
		/**
		 * 物料清单版本
		 */
		bomVersion?: number;
		/**
		 * 物料清单内容
		 */
		bomContent?: json;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsPurchaseOrderPendingInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 订单号
		 */
		orderNo?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 步骤
		 */
		step?: number;
		/**
		 * 是否有警告
		 */
		hasWarning?: number;
		/**
		 * 父订单ID
		 */
		parentOrderId?: number;
		/**
		 * 订单类型
		 */
		orderType?: number;
		/**
		 * 虚拟订单
		 */
		virtualOrder?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsSaleDeliveryOrderInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 创建者ID
		 */
		creatorId?: number;
		/**
		 * 目的地仓库ID
		 */
		destinationPointId?: number;
		/**
		 * 订单号
		 */
		orderSn?: string;
		/**
		 * 参考号
		 */
		ref?: string;
		/**
		 * 收货人
		 */
		consignee?: string;
		/**
		 * 箱数
		 */
		cases?: number;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 下单日期
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsSaleOrderInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 创建者ID
		 */
		creatorId?: number;
		/**
		 * 订单号
		 */
		orderSn?: string;
		/**
		 * 客户代码
		 */
		customerCode?: string;
		/**
		 * 合计数量
		 */
		total?: number;
		/**
		 * 要求出货日期
		 */
		requiredShipDate?: Date;
		/**
		 * 实际发货日期
		 */
		shipDate?: Date;
		/**
		 * 出货仓库ID
		 */
		sourcePointId?: number;
		/**
		 * 订单类型
		 */
		type?: number;
		/**
		 * 入仓号
		 */
		receiptNumber?: string;
		/**
		 * 入仓仓库ID
		 */
		destinationPointId?: number;
		/**
		 * 货代ID
		 */
		forwarderId?: number;
		/**
		 * 目的港
		 */
		port?: string;
		/**
		 * 汇率
		 */
		exchangeRate?: number;
		/**
		 * 已发INVOICE
		 */
		isInvoice?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 到库时间
		 */
		inboundDate?: Date;
		/**
		 * 送达时间
		 */
		arrivalDate?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsSupplierInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 名称
		 */
		name?: string;
		/**
		 * 联系人
		 */
		contact?: string;
		/**
		 * 联系电话
		 */
		phone?: string;
		/**
		 * 传真
		 */
		fax?: string;
		/**
		 * 地址
		 */
		address?: string;
		/**
		 * 简称
		 */
		shortName?: string;
		/**
		 * 主要产品
		 */
		mainProduct?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsSupplierAccountInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * departmentId
		 */
		departmentId?: number;
		/**
		 * name
		 */
		name?: string;
		/**
		 * username
		 */
		username?: string;
		/**
		 * password
		 */
		password?: string;
		/**
		 * passwordV
		 */
		passwordV?: number;
		/**
		 * nickName
		 */
		nickName?: string;
		/**
		 * headImg
		 */
		headImg?: string;
		/**
		 * phone
		 */
		phone?: string;
		/**
		 * email
		 */
		email?: string;
		/**
		 * status
		 */
		status?: number;
		/**
		 * remark
		 */
		remark?: string;
		/**
		 * socketId
		 */
		socketId?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsWarehouseDestinationInboundInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 关联订单ID
		 */
		orderId?: number;
		/**
		 * 入库仓库ID
		 */
		destinationPointId?: number;
		/**
		 * 总数量
		 */
		totalQuantity?: number;
		/**
		 * 状态 0：待处理 1：入库中 2：已入库
		 */
		status?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 处理完成时间
		 */
		completeTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsWarehouseDestinationOutboundInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 关联订单ID
		 */
		deliveryOrderId?: number;
		/**
		 * 入库仓库ID
		 */
		destinationPointId?: number;
		/**
		 * 状态 0：待处理 1：入库中 2：已入库
		 */
		status?: number;
		/**
		 * 是否已付款
		 */
		isPaid?: number;
		/**
		 * 运费
		 */
		trackingCost?: number;
		/**
		 * 承运商
		 */
		carrier?: string;
		/**
		 * 跟踪号码
		 */
		pro?: string;
		/**
		 * 追踪网址
		 */
		trackingUrl?: string;
		/**
		 * 发货日期
		 */
		dispatchDate?: Date;
		/**
		 * 预计到货时间
		 */
		eta?: Date;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 处理完成时间
		 */
		completeTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsWarehouseDestinationStockInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 入库仓库ID
		 */
		destinationPointId?: number;
		/**
		 * product_id
		 */
		productId?: number;
		/**
		 * 可用库存
		 */
		inStock?: number;
		/**
		 * 冻结库存
		 */
		freezeStock?: number;
		/**
		 * 库存最后更新的时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsWarehouseDestinationStockRecordInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 入库仓库ID
		 */
		destinationPointId?: number;
		/**
		 * 关联入库单ID
		 */
		inboundId?: number;
		/**
		 * 关联产品ID
		 */
		productId?: number;
		/**
		 * 关联的入库单行ID
		 */
		inboundRowId?: number;
		/**
		 * 入库数量
		 */
		quantity?: number;
		/**
		 * 原包装产品ID
		 */
		originalProductId?: number;
		/**
		 * 原包装产品单位
		 */
		originalProductUnit?: number;
		/**
		 * 原包装产品单位数量
		 */
		originalProductUnitQuantity?: number;
		/**
		 * 剩余可用数量
		 */
		available?: number;
		/**
		 * 外箱PO
		 */
		cartonPo?: string;
		/**
		 * 空运单号
		 */
		airway?: string;
		/**
		 * 箱号
		 */
		carton?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsWarehousePointInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 仓库名
		 */
		name?: string;
		/**
		 * 仓库地址
		 */
		address?: string;
		/**
		 * 仓库类型 0：出 1：入
		 */
		type?: number;
		/**
		 * 仓库位置 0: 国内 1:美国
		 */
		location?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsWarehouseSourceInboundInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 入库单号
		 */
		isn?: string;
		/**
		 * 关联订生产订单ID
		 */
		orderId?: number;
		/**
		 * 创建人ID
		 */
		createUserId?: number;
		/**
		 * 入库仓库ID
		 */
		sourcePointId?: number;
		/**
		 * 生产计划ID
		 */
		scheduleId?: number;
		/**
		 * 总数量
		 */
		totalQuantity?: number;
		/**
		 * 类型：1：生产入库 2：手动入库
		 */
		type?: number;
		/**
		 * 是否为库存调整：0：否 1：是
		 */
		isAdjust?: number;
		/**
		 * 状态 0：待处理 1：入库中 2：已入库
		 */
		status?: number;
		/**
		 * 凭证
		 */
		voucher?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 处理完成时间
		 */
		completeTime?: Date;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsWarehouseSourceOutboundInfoEntity {
		/**
		 * id
		 */
		id?: number;
		/**
		 * 出库单号
		 */
		osn?: string;
		/**
		 * 关联订单ID
		 */
		orderId?: number;
		/**
		 * 出库仓库ID
		 */
		sourcePointId?: number;
		/**
		 * 合计数量
		 */
		totalQuantity?: number;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 发货时间
		 */
		shipTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 出库单号
		 */
		sn?: string;
		/**
		 * 生产订单ID
		 */
		productionOrderId?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PmsWorkOrderInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 工单编号
		 */
		workOrderNo?: string;
		/**
		 * 生产单 id
		 */
		productionScheduleId?: number;
		/**
		 * 生产单号
		 */
		productionScheduleSn?: string;
		/**
		 * 产品ID
		 */
		productId?: number;
		/**
		 * 出库单ID
		 */
		materialOutboundIds?: string;
		/**
		 * 创建人
		 */
		creatorId?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 计划ID
		 */
		planId?: number;
		/**
		 * 计划生产日期
		 */
		planProductionDate?: Date;
		/**
		 * 计划生产数量
		 */
		quantity?: number;
		/**
		 * 成品入库数量
		 */
		productInboundQty?: number;
		/**
		 * SKU
		 */
		sku?: string;
		/**
		 * 工单状态, 1已完成
		 */
		status?: number;
		/**
		 * BOM id
		 */
		bomId?: number;
		/**
		 * 版本
		 */
		bomVersion?: number;
		/**
		 * 产品ID
		 */
		productIds?: string;
		/**
		 * 产品数量
		 */
		productQtyJson?: string;
		/**
		 * 产品编码JSON
		 */
		codeJson?: string;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface SpaceInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 地址
		 */
		url?: string;
		/**
		 * 类型
		 */
		type?: string;
		/**
		 * 分类ID
		 */
		classifyId?: number;
		/**
		 * 是否为私有文件
		 */
		private?: number;
		/**
		 * 是否私有
		 */
		isPrivate?: number;
		/**
		 * 创建者ID
		 */
		creatorId?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface SpaceTypeInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 类别名称
		 */
		name?: string;
		/**
		 * 父分类ID
		 */
		parentId?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface TaskInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 删除时间
		 */
		deleteTime?: Date;
		/**
		 * 任务ID
		 */
		jobId?: string;
		/**
		 * 重复配置
		 */
		repeatConf?: string;
		/**
		 * 任务名称
		 */
		name?: string;
		/**
		 * cron表达式
		 */
		cron?: string;
		/**
		 * 限制次数 不传为不限制
		 */
		limit?: number;
		/**
		 * 间隔时间 单位秒
		 */
		every?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 状态 0:关闭 1:开启
		 */
		status?: number;
		/**
		 * 开始时间
		 */
		startDate?: Date;
		/**
		 * 结束时间
		 */
		endDate?: Date;
		/**
		 * 数据
		 */
		data?: string;
		/**
		 * 执行的服务
		 */
		service?: string;
		/**
		 * 类型 0:系统 1:用户
		 */
		type?: number;
		/**
		 * 下次执行时间
		 */
		nextRunTime?: Date;
		/**
		 * 任务类型 0:cron 1:时间间隔
		 */
		taskType?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}
	interface BaseComm {
		/**
		 * appUpdate
		 */
		appUpdate(data?: any): Promise<any>;
		/**
		 * logout
		 */
		logout(data?: any): Promise<any>;
		/**
		 * permmenu
		 */
		permmenu(data?: any): Promise<any>;
		/**
		 * person
		 */
		person(data?: any): Promise<any>;
		/**
		 * personUpdate
		 */
		personUpdate(data?: any): Promise<any>;
		/**
		 * upload
		 */
		upload(data?: any): Promise<any>;
		/**
		 * uploadMode
		 */
		uploadMode(data?: any): Promise<any>;
		/**
		 * version
		 */
		version(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			appUpdate: string;
			logout: string;
			permmenu: string;
			person: string;
			personUpdate: string;
			upload: string;
			uploadMode: string;
			version: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			appUpdate: boolean;
			logout: boolean;
			permmenu: boolean;
			person: boolean;
			personUpdate: boolean;
			upload: boolean;
			uploadMode: boolean;
			version: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseOpen {
		/**
		 * captcha
		 */
		captcha(data?: any): Promise<any>;
		/**
		 * eps
		 */
		eps(data?: any): Promise<any>;
		/**
		 * login
		 */
		login(data?: any): Promise<any>;
		/**
		 * refreshToken
		 */
		refreshToken(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: { captcha: string; eps: string; login: string; refreshToken: string };
		/**
		 * 权限状态
		 */
		_permission: { captcha: boolean; eps: boolean; login: boolean; refreshToken: boolean };
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseSysDepartment {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<BaseSysDepartmentInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<BaseSysDepartmentInfoEntity[]>;
		/**
		 * order
		 */
		order(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: BaseSysDepartmentInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			order: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			order: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseSysLog {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * clear
		 */
		clear(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * getKeep
		 */
		getKeep(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<BaseSysLogInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<BaseSysLogInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: BaseSysLogInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * setKeep
		 */
		setKeep(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			clear: string;
			delete: string;
			getKeep: string;
			info: string;
			list: string;
			page: string;
			setKeep: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			clear: boolean;
			delete: boolean;
			getKeep: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			setKeep: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseSysMenu {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<BaseSysMenuInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<BaseSysMenuInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: BaseSysMenuInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseSysParam {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<BaseSysParamInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<BaseSysParamInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: BaseSysParamInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseSysRole {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<BaseSysRoleInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<BaseSysRoleInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: BaseSysRoleInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseSysUser {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<BaseSysUserInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<BaseSysUserInfoEntity[]>;
		/**
		 * move
		 */
		move(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: BaseSysUserInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			move: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			move: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface DictInfo {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * data
		 */
		data(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<DictInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<DictInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: DictInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			data: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			data: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface DictType {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<DictTypeInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<DictTypeInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: DictTypeInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PimsAutid {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * getAutidByWorkitemId
		 */
		getAutidByWorkitemId(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PimsAutidInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PimsAutidInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PimsAutidInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			getAutidByWorkitemId: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			getAutidByWorkitemId: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PimsConfig {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PimsConfigInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PimsConfigInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PimsConfigInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PimsDaliyReport {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PimsDaliyReportInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PimsDaliyReportInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PimsDaliyReportInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * readReport
		 */
		readReport(data?: any): Promise<any>;
		/**
		 * submitReport
		 */
		submitReport(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			readReport: string;
			submitReport: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			readReport: boolean;
			submitReport: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PimsProgress {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PimsProgressInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PimsProgressInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PimsProgressInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PimsWorkitem {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * audit
		 */
		audit(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PimsWorkitemInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PimsWorkitemInfoEntity[]>;
		/**
		 * listByIds
		 */
		listByIds(data?: any): Promise<any>;
		/**
		 * listByUser
		 */
		listByUser(data?: any): Promise<any>;
		/**
		 * logDetail
		 */
		logDetail(data?: any): Promise<any>;
		/**
		 * myList
		 */
		myList(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PimsWorkitemInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * queryInfo
		 */
		queryInfo(data?: any): Promise<any>;
		/**
		 * readLog
		 */
		readLog(data?: any): Promise<any>;
		/**
		 * readTask
		 */
		readTask(data?: any): Promise<any>;
		/**
		 * remove
		 */
		remove(data?: any): Promise<any>;
		/**
		 * submit
		 */
		submit(data?: any): Promise<any>;
		/**
		 * systemUserList
		 */
		systemUserList(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * updateSort
		 */
		updateSort(data?: any): Promise<any>;
		/**
		 * workHourByIds
		 */
		workHourByIds(data?: any): Promise<any>;
		/**
		 * attachment
		 */
		attachment: PimsWorkitemAttachment;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			audit: string;
			delete: string;
			info: string;
			list: string;
			listByIds: string;
			listByUser: string;
			logDetail: string;
			myList: string;
			page: string;
			queryInfo: string;
			readLog: string;
			readTask: string;
			remove: string;
			submit: string;
			systemUserList: string;
			update: string;
			updateSort: string;
			workHourByIds: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			audit: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			listByIds: boolean;
			listByUser: boolean;
			logDetail: boolean;
			myList: boolean;
			page: boolean;
			queryInfo: boolean;
			readLog: boolean;
			readTask: boolean;
			remove: boolean;
			submit: boolean;
			systemUserList: boolean;
			update: boolean;
			updateSort: boolean;
			workHourByIds: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PimsWorkitemAttachment {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * download
		 */
		download(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PimsWorkitemAttachmentInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PimsWorkitemAttachmentInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PimsWorkitemAttachmentInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * remove
		 */
		remove(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * upload
		 */
		upload(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			download: string;
			info: string;
			list: string;
			page: string;
			remove: string;
			update: string;
			upload: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			download: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			remove: boolean;
			update: boolean;
			upload: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsAbnormalWorkingHours {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * importAbnormalData
		 */
		importAbnormalData(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsAbnormalWorkingHoursInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsAbnormalWorkingHoursInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsAbnormalWorkingHoursInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			export: string;
			importAbnormalData: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			export: boolean;
			importAbnormalData: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsDailyProductionReport {
		/**
		 * GetCapacitySummary
		 */
		GetCapacitySummary(data?: any): Promise<any>;
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * audit
		 */
		audit(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * exportSummary
		 */
		exportSummary(data?: any): Promise<any>;
		/**
		 * importDailyProductionData
		 */
		importDailyProductionData(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsDailyProductionReportInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsDailyProductionReportInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsDailyProductionReportInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			GetCapacitySummary: string;
			add: string;
			audit: string;
			delete: string;
			export: string;
			exportSummary: string;
			importDailyProductionData: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			GetCapacitySummary: boolean;
			add: boolean;
			audit: boolean;
			delete: boolean;
			export: boolean;
			exportSummary: boolean;
			importDailyProductionData: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsPartsCapacity {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsPartsCapacityInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsPartsCapacityInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsPartsCapacityInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsStandardCapacity {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * addPartsCapacity
		 */
		addPartsCapacity(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * deletePartsCapacity
		 */
		deletePartsCapacity(data?: any): Promise<any>;
		/**
		 * importStandardData
		 */
		importStandardData(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsStandardCapacityInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsStandardCapacityInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsStandardCapacityInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * summaryExport
		 */
		summaryExport(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * updatePartsCapacity
		 */
		updatePartsCapacity(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			addPartsCapacity: string;
			delete: string;
			deletePartsCapacity: string;
			importStandardData: string;
			info: string;
			list: string;
			page: string;
			summaryExport: string;
			update: string;
			updatePartsCapacity: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			addPartsCapacity: boolean;
			delete: boolean;
			deletePartsCapacity: boolean;
			importStandardData: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			summaryExport: boolean;
			update: boolean;
			updatePartsCapacity: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsAllDataCompare {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * downloadLatestColumnData
		 */
		downloadLatestColumnData(data?: any): Promise<any>;
		/**
		 * exportSummaryData
		 */
		exportSummaryData(data?: any): Promise<any>;
		/**
		 * getHeaderColumns
		 */
		getHeaderColumns(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsAllDataCompareInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsAllDataCompareInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsAllDataCompareInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * uploadColumnData
		 */
		uploadColumnData(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			downloadLatestColumnData: string;
			exportSummaryData: string;
			getHeaderColumns: string;
			info: string;
			list: string;
			page: string;
			update: string;
			uploadColumnData: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			downloadLatestColumnData: boolean;
			exportSummaryData: boolean;
			getHeaderColumns: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
			uploadColumnData: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsAuditProcess {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * auditRecordList
		 */
		auditRecordList(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsAuditProcessInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsAuditProcessInfoEntity[]>;
		/**
		 * nodes
		 */
		nodes(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsAuditProcessInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * saveNodes
		 */
		saveNodes(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * updateNode
		 */
		updateNode(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			auditRecordList: string;
			delete: string;
			info: string;
			list: string;
			nodes: string;
			page: string;
			saveNodes: string;
			update: string;
			updateNode: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			auditRecordList: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			nodes: boolean;
			page: boolean;
			saveNodes: boolean;
			update: boolean;
			updateNode: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsAuditProcessor {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsAuditProcessorInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsAuditProcessorInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsAuditProcessorInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * node
		 */
		node: PmsAuditProcessorNode;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsAuditProcessorNode {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * deal
		 */
		deal(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsAuditProcessorNodeInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsAuditProcessorNodeInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsAuditProcessorNodeInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			deal: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			deal: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsBom {
		/**
		 * GetBomById
		 */
		GetBomById(data?: any): Promise<any>;
		/**
		 * GetBomMaterialByProductId
		 */
		GetBomMaterialByProductId(data?: any): Promise<any>;
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * importBom
		 */
		importBom(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsBomInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsBomInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsBomInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * saveBom
		 */
		saveBom(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			GetBomById: string;
			GetBomMaterialByProductId: string;
			add: string;
			delete: string;
			export: string;
			importBom: string;
			info: string;
			list: string;
			page: string;
			saveBom: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			GetBomById: boolean;
			GetBomMaterialByProductId: boolean;
			add: boolean;
			delete: boolean;
			export: boolean;
			importBom: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			saveBom: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsClearanceOrder {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * confirm
		 */
		confirm(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * download
		 */
		download(data?: any): Promise<any>;
		/**
		 * finish
		 */
		finish(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsClearanceOrderInfoEntity>;
		/**
		 * information
		 */
		information(data?: any): Promise<any>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsClearanceOrderInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsClearanceOrderInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			confirm: string;
			delete: string;
			download: string;
			finish: string;
			info: string;
			information: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			confirm: boolean;
			delete: boolean;
			download: boolean;
			finish: boolean;
			info: boolean;
			information: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsDaily_report_data {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * importDailyReportData
		 */
		importDailyReportData(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsDailyReportDataInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsDailyReportDataInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsDailyReportDataInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			export: string;
			importDailyReportData: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			export: boolean;
			importDailyReportData: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsDataDiff {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * exportTestDataToJson
		 */
		exportTestDataToJson(data?: any): Promise<any>;
		/**
		 * importTestData
		 */
		importTestData(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsDataDiffInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsDataDiffInfoEntity[]>;
		/**
		 * mergeMaterialData
		 */
		mergeMaterialData(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsDataDiffInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * uploadDataDiff
		 */
		uploadDataDiff(data?: any): Promise<any>;
		/**
		 * uploadProductDataDiff
		 */
		uploadProductDataDiff(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			exportTestDataToJson: string;
			importTestData: string;
			info: string;
			list: string;
			mergeMaterialData: string;
			page: string;
			update: string;
			uploadDataDiff: string;
			uploadProductDataDiff: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			exportTestDataToJson: boolean;
			importTestData: boolean;
			info: boolean;
			list: boolean;
			mergeMaterialData: boolean;
			page: boolean;
			update: boolean;
			uploadDataDiff: boolean;
			uploadProductDataDiff: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsDelivery_note {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * complete
		 */
		complete(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * getDeliveryInfo
		 */
		getDeliveryInfo(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsDeliveryNoteInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsDeliveryNoteInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsDeliveryNoteInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * revoke
		 */
		revoke(data?: any): Promise<any>;
		/**
		 * submit
		 */
		submit(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			complete: string;
			delete: string;
			getDeliveryInfo: string;
			info: string;
			list: string;
			page: string;
			revoke: string;
			submit: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			complete: boolean;
			delete: boolean;
			getDeliveryInfo: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			revoke: boolean;
			submit: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsDrawing {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * download
		 */
		download(data?: any): Promise<any>;
		/**
		 * downloadHistory
		 */
		downloadHistory(data?: any): Promise<any>;
		/**
		 * getProductsByDrawingId
		 */
		getProductsByDrawingId(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsDrawingInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsDrawingInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsDrawingInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * removeDrawingProduct
		 */
		removeDrawingProduct(data?: any): Promise<any>;
		/**
		 * saveProducts
		 */
		saveProducts(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			download: string;
			downloadHistory: string;
			getProductsByDrawingId: string;
			info: string;
			list: string;
			page: string;
			removeDrawingProduct: string;
			saveProducts: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			download: boolean;
			downloadHistory: boolean;
			getProductsByDrawingId: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			removeDrawingProduct: boolean;
			saveProducts: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsFinance {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsFinanceInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsFinanceInfoEntity[]>;
		/**
		 * materialInboundExportExcel
		 */
		materialInboundExportExcel(data?: any): Promise<any>;
		/**
		 * materialInboundPage
		 */
		materialInboundPage(data?: any): Promise<any>;
		/**
		 * materialOutboundExportExcel
		 */
		materialOutboundExportExcel(data?: any): Promise<any>;
		/**
		 * materialOutboundPage
		 */
		materialOutboundPage(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsFinanceInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * productInboundExportExcel
		 */
		productInboundExportExcel(data?: any): Promise<any>;
		/**
		 * productInboundPage
		 */
		productInboundPage(data?: any): Promise<any>;
		/**
		 * productOutboundExportExcel
		 */
		productOutboundExportExcel(data?: any): Promise<any>;
		/**
		 * productOutboundPage
		 */
		productOutboundPage(data?: any): Promise<any>;
		/**
		 * productionOrderExportExcel
		 */
		productionOrderExportExcel(data?: any): Promise<any>;
		/**
		 * productionOrderPage
		 */
		productionOrderPage(data?: any): Promise<any>;
		/**
		 * purchaseOrderExportExcel
		 */
		purchaseOrderExportExcel(data?: any): Promise<any>;
		/**
		 * purchaseOrderPage
		 */
		purchaseOrderPage(data?: any): Promise<any>;
		/**
		 * salesOrderExportExcel
		 */
		salesOrderExportExcel(data?: any): Promise<any>;
		/**
		 * salesOrderPage
		 */
		salesOrderPage(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * bill_payment
		 */
		bill_payment: PmsFinanceBill_payment;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			materialInboundExportExcel: string;
			materialInboundPage: string;
			materialOutboundExportExcel: string;
			materialOutboundPage: string;
			page: string;
			productInboundExportExcel: string;
			productInboundPage: string;
			productOutboundExportExcel: string;
			productOutboundPage: string;
			productionOrderExportExcel: string;
			productionOrderPage: string;
			purchaseOrderExportExcel: string;
			purchaseOrderPage: string;
			salesOrderExportExcel: string;
			salesOrderPage: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			materialInboundExportExcel: boolean;
			materialInboundPage: boolean;
			materialOutboundExportExcel: boolean;
			materialOutboundPage: boolean;
			page: boolean;
			productInboundExportExcel: boolean;
			productInboundPage: boolean;
			productOutboundExportExcel: boolean;
			productOutboundPage: boolean;
			productionOrderExportExcel: boolean;
			productionOrderPage: boolean;
			purchaseOrderExportExcel: boolean;
			purchaseOrderPage: boolean;
			salesOrderExportExcel: boolean;
			salesOrderPage: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsFreightForwarder {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsFreightForwarderInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsFreightForwarderInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsFreightForwarderInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * order
		 */
		order: PmsFreightForwarderOrder;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsFreightForwarderOrder {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * confirm
		 */
		confirm(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsFreightForwarderOrderInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsFreightForwarderOrderInfoEntity[]>;
		/**
		 * merge
		 */
		merge(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsFreightForwarderOrderInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * prepare
		 */
		prepare(data?: any): Promise<any>;
		/**
		 * revoke
		 */
		revoke(data?: any): Promise<any>;
		/**
		 * send
		 */
		send(data?: any): Promise<any>;
		/**
		 * supplement
		 */
		supplement(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * upload
		 */
		upload(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			confirm: string;
			delete: string;
			info: string;
			list: string;
			merge: string;
			page: string;
			prepare: string;
			revoke: string;
			send: string;
			supplement: string;
			update: string;
			upload: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			confirm: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			merge: boolean;
			page: boolean;
			prepare: boolean;
			revoke: boolean;
			send: boolean;
			supplement: boolean;
			update: boolean;
			upload: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsJobInstruction {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * download
		 */
		download(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsJobInstructionInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsJobInstructionInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsJobInstructionInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			download: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			download: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsMaterial {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * addMaterialAddress
		 */
		addMaterialAddress(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * deleteMaterialAddress
		 */
		deleteMaterialAddress(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * getMaterialAddress
		 */
		getMaterialAddress(data?: any): Promise<any>;
		/**
		 * getMaterialById
		 */
		getMaterialById(data?: any): Promise<any>;
		/**
		 * getMaterialByName
		 */
		getMaterialByName(data?: any): Promise<any>;
		/**
		 * importExcel
		 */
		importExcel(data?: any): Promise<any>;
		/**
		 * importMaterialAddressData
		 */
		importMaterialAddressData(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsMaterialInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsMaterialInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsMaterialInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * summaryExport
		 */
		summaryExport(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * bundle
		 */
		bundle: PmsMaterialBundle;
		/**
		 * drawing
		 */
		drawing: PmsMaterialDrawing;
		/**
		 * inbound
		 */
		inbound: PmsMaterialInbound;
		/**
		 * outbound
		 */
		outbound: PmsMaterialOutbound;
		/**
		 * price
		 */
		price: PmsMaterialPrice;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			addMaterialAddress: string;
			delete: string;
			deleteMaterialAddress: string;
			export: string;
			getMaterialAddress: string;
			getMaterialById: string;
			getMaterialByName: string;
			importExcel: string;
			importMaterialAddressData: string;
			info: string;
			list: string;
			page: string;
			summaryExport: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			addMaterialAddress: boolean;
			delete: boolean;
			deleteMaterialAddress: boolean;
			export: boolean;
			getMaterialAddress: boolean;
			getMaterialById: boolean;
			getMaterialByName: boolean;
			importExcel: boolean;
			importMaterialAddressData: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			summaryExport: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsMaterialStockLog {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsMaterialStockLogInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsMaterialStockLogInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsMaterialStockLogInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * queryMaterialStockLogPage
		 */
		queryMaterialStockLogPage(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			queryMaterialStockLogPage: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			queryMaterialStockLogPage: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsMaterial_anomaly {
		/**
		 * ImportMaterialAnomaly
		 */
		ImportMaterialAnomaly(data?: any): Promise<any>;
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsMaterialAnomalyInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsMaterialAnomalyInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsMaterialAnomalyInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			ImportMaterialAnomaly: string;
			add: string;
			delete: string;
			export: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			ImportMaterialAnomaly: boolean;
			add: boolean;
			delete: boolean;
			export: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsMaterial_test {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsMaterialTestInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsMaterialTestInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsMaterialTestInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			export: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			export: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsOutsourceInbound {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * getOutsourceByWorkOrder
		 */
		getOutsourceByWorkOrder(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsOutsourceInboundInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsOutsourceInboundInfoEntity[]>;
		/**
		 * outsourceInboundPage
		 */
		outsourceInboundPage(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsOutsourceInboundInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * revoke
		 */
		revoke(data?: any): Promise<any>;
		/**
		 * submit
		 */
		submit(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			getOutsourceByWorkOrder: string;
			info: string;
			list: string;
			outsourceInboundPage: string;
			page: string;
			revoke: string;
			submit: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			getOutsourceByWorkOrder: boolean;
			info: boolean;
			list: boolean;
			outsourceInboundPage: boolean;
			page: boolean;
			revoke: boolean;
			submit: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsPayment {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * batchExport
		 */
		batchExport(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * financePaymentPage
		 */
		financePaymentPage(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsPaymentInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsPaymentInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsPaymentInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * summaryExport
		 */
		summaryExport(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			batchExport: string;
			delete: string;
			export: string;
			financePaymentPage: string;
			info: string;
			list: string;
			page: string;
			summaryExport: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			batchExport: boolean;
			delete: boolean;
			export: boolean;
			financePaymentPage: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			summaryExport: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsProduct {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * exportInboundOutboundDetails
		 */
		exportInboundOutboundDetails(data?: any): Promise<any>;
		/**
		 * exportProduct
		 */
		exportProduct(data?: any): Promise<any>;
		/**
		 * getAllProduct
		 */
		getAllProduct(data?: any): Promise<any>;
		/**
		 * import_data
		 */
		import_data(data?: any): Promise<any>;
		/**
		 * inboundSummary
		 */
		inboundSummary(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsProductInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsProductInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsProductInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * summaryExport
		 */
		summaryExport(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * group
		 */
		group: PmsProductGroup;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			exportInboundOutboundDetails: string;
			exportProduct: string;
			getAllProduct: string;
			import_data: string;
			inboundSummary: string;
			info: string;
			list: string;
			page: string;
			summaryExport: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			exportInboundOutboundDetails: boolean;
			exportProduct: boolean;
			getAllProduct: boolean;
			import_data: boolean;
			inboundSummary: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			summaryExport: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsProductionManHourDeduct {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsProductionManHourDeductInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsProductionManHourDeductInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsProductionManHourDeductInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsProductionMaterialDeduct {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * getMaterialListBySupplierId
		 */
		getMaterialListBySupplierId(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsProductionMaterialDeductInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsProductionMaterialDeductInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsProductionMaterialDeductInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			getMaterialListBySupplierId: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			getMaterialListBySupplierId: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsProductionPurchaseOrder {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * auditLog
		 */
		auditLog(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * exportSummary
		 */
		exportSummary(data?: any): Promise<any>;
		/**
		 * getProductByProductionOrder
		 */
		getProductByProductionOrder(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsProductionPurchaseOrderInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsProductionPurchaseOrderInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsProductionPurchaseOrderInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * purchaseOrderExportExcel
		 */
		purchaseOrderExportExcel(data?: any): Promise<any>;
		/**
		 * purchaseOrderPage
		 */
		purchaseOrderPage(data?: any): Promise<any>;
		/**
		 * revoke
		 */
		revoke(data?: any): Promise<any>;
		/**
		 * submit
		 */
		submit(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * updateExtra
		 */
		updateExtra(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			auditLog: string;
			delete: string;
			exportSummary: string;
			getProductByProductionOrder: string;
			info: string;
			list: string;
			page: string;
			purchaseOrderExportExcel: string;
			purchaseOrderPage: string;
			revoke: string;
			submit: string;
			update: string;
			updateExtra: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			auditLog: boolean;
			delete: boolean;
			exportSummary: boolean;
			getProductByProductionOrder: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			purchaseOrderExportExcel: boolean;
			purchaseOrderPage: boolean;
			revoke: boolean;
			submit: boolean;
			update: boolean;
			updateExtra: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsProductionSaleOrder {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsProductionSaleOrderInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsProductionSaleOrderInfoEntity[]>;
		/**
		 * lockStock
		 */
		lockStock(data?: any): Promise<any>;
		/**
		 * outbound
		 */
		outbound(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsProductionSaleOrderInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * revoke
		 */
		revoke(data?: any): Promise<any>;
		/**
		 * schedule
		 */
		schedule(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			lockStock: string;
			outbound: string;
			page: string;
			revoke: string;
			schedule: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			lockStock: boolean;
			outbound: boolean;
			page: boolean;
			revoke: boolean;
			schedule: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsProductionSchedule {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * confirm
		 */
		confirm(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * exportOrder
		 */
		exportOrder(data?: any): Promise<any>;
		/**
		 * getOrderProductQuantity
		 */
		getOrderProductQuantity(data?: any): Promise<any>;
		/**
		 * getProductListByOrderId
		 */
		getProductListByOrderId(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsProductionScheduleInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsProductionScheduleInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsProductionScheduleInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * revoke
		 */
		revoke(data?: any): Promise<any>;
		/**
		 * summary
		 */
		summary(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * plan
		 */
		plan: PmsProductionSchedulePlan;
		/**
		 * product
		 */
		product: PmsProductionScheduleProduct;
		/**
		 * purchaseOrder
		 */
		purchaseOrder: PmsProductionSchedulePurchaseOrder;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			confirm: string;
			delete: string;
			export: string;
			exportOrder: string;
			getOrderProductQuantity: string;
			getProductListByOrderId: string;
			info: string;
			list: string;
			page: string;
			revoke: string;
			summary: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			confirm: boolean;
			delete: boolean;
			export: boolean;
			exportOrder: boolean;
			getOrderProductQuantity: boolean;
			getProductListByOrderId: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			revoke: boolean;
			summary: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsProductionSchedulePlan {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * create
		 */
		create(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsProductionSchedulePlanInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsProductionSchedulePlanInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsProductionSchedulePlanInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			create: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			create: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsProductionScheduleProduct {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsProductionScheduleProductInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsProductionScheduleProductInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsProductionScheduleProductInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsProductionSchedulePurchaseOrder {
		/**
		 * detail
		 */
		detail(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsProductionSchedulePurchaseOrderInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * 权限标识
		 */
		permission: { detail: string; page: string };
		/**
		 * 权限状态
		 */
		_permission: { detail: boolean; page: boolean };
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsProductionDataIncoming {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * import
		 */
		import(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsProductionDataIncomingInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsProductionDataIncomingInfoEntity[]>;
		/**
		 * listByMonth
		 */
		listByMonth(data?: any): Promise<any>;
		/**
		 * materialList
		 */
		materialList(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsProductionDataIncomingInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * supplierList
		 */
		supplierList(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			import: string;
			info: string;
			list: string;
			listByMonth: string;
			materialList: string;
			page: string;
			supplierList: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			import: boolean;
			info: boolean;
			list: boolean;
			listByMonth: boolean;
			materialList: boolean;
			page: boolean;
			supplierList: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsProductionDataProcessAbnormality {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * getProductList
		 */
		getProductList(data?: any): Promise<any>;
		/**
		 * import
		 */
		import(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsProductionDataProcessAbnormalityInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsProductionDataProcessAbnormalityInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsProductionDataProcessAbnormalityInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * queryDeptList
		 */
		queryDeptList(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			getProductList: string;
			import: string;
			info: string;
			list: string;
			page: string;
			queryDeptList: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			getProductList: boolean;
			import: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			queryDeptList: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsPurchaseContract {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * getContractListByPoAndSupplierId
		 */
		getContractListByPoAndSupplierId(data?: any): Promise<any>;
		/**
		 * getUnfinishedPo
		 */
		getUnfinishedPo(data?: any): Promise<any>;
		/**
		 * importContractExcelData
		 */
		importContractExcelData(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsPurchaseContractInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsPurchaseContractInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsPurchaseContractInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * purchaseContractImport
		 */
		purchaseContractImport(data?: any): Promise<any>;
		/**
		 * purchaseContractOutboundImport
		 */
		purchaseContractOutboundImport(data?: any): Promise<any>;
		/**
		 * queryPage
		 */
		queryPage(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * updateContractStatus
		 */
		updateContractStatus(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			getContractListByPoAndSupplierId: string;
			getUnfinishedPo: string;
			importContractExcelData: string;
			info: string;
			list: string;
			page: string;
			purchaseContractImport: string;
			purchaseContractOutboundImport: string;
			queryPage: string;
			update: string;
			updateContractStatus: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			getContractListByPoAndSupplierId: boolean;
			getUnfinishedPo: boolean;
			importContractExcelData: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			purchaseContractImport: boolean;
			purchaseContractOutboundImport: boolean;
			queryPage: boolean;
			update: boolean;
			updateContractStatus: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsPurchaseOrder {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * auditData
		 */
		auditData(data?: any): Promise<any>;
		/**
		 * auditLog
		 */
		auditLog(data?: any): Promise<any>;
		/**
		 * confirm
		 */
		confirm(data?: any): Promise<any>;
		/**
		 * contract
		 */
		contract(data?: any): Promise<any>;
		/**
		 * createContract
		 */
		createContract(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * deleteSuborder
		 */
		deleteSuborder(data?: any): Promise<any>;
		/**
		 * downloadContract
		 */
		downloadContract(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * exportDetail
		 */
		exportDetail(data?: any): Promise<any>;
		/**
		 * exportSummary
		 */
		exportSummary(data?: any): Promise<any>;
		/**
		 * getInboundReceivedQuantity
		 */
		getInboundReceivedQuantity(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsPurchaseOrderInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsPurchaseOrderInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsPurchaseOrderInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * rejectProduction
		 */
		rejectProduction(data?: any): Promise<any>;
		/**
		 * removeContract
		 */
		removeContract(data?: any): Promise<any>;
		/**
		 * revoke
		 */
		revoke(data?: any): Promise<any>;
		/**
		 * saveContract
		 */
		saveContract(data?: any): Promise<any>;
		/**
		 * summary
		 */
		summary(data?: any): Promise<any>;
		/**
		 * transfer
		 */
		transfer(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * detail
		 */
		detail: PmsPurchaseOrderDetail;
		/**
		 * pending
		 */
		pending: PmsPurchaseOrderPending;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			auditData: string;
			auditLog: string;
			confirm: string;
			contract: string;
			createContract: string;
			delete: string;
			deleteSuborder: string;
			downloadContract: string;
			export: string;
			exportDetail: string;
			exportSummary: string;
			getInboundReceivedQuantity: string;
			info: string;
			list: string;
			page: string;
			rejectProduction: string;
			removeContract: string;
			revoke: string;
			saveContract: string;
			summary: string;
			transfer: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			auditData: boolean;
			auditLog: boolean;
			confirm: boolean;
			contract: boolean;
			createContract: boolean;
			delete: boolean;
			deleteSuborder: boolean;
			downloadContract: boolean;
			export: boolean;
			exportDetail: boolean;
			exportSummary: boolean;
			getInboundReceivedQuantity: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			rejectProduction: boolean;
			removeContract: boolean;
			revoke: boolean;
			saveContract: boolean;
			summary: boolean;
			transfer: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsPurchaseOrderDetail {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsPurchaseOrderDetailInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsPurchaseOrderDetailInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsPurchaseOrderDetailInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsPurchaseOrderPending {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsPurchaseOrderPendingInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsPurchaseOrderPendingInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsPurchaseOrderPendingInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			export: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			export: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsSaleDeliveryOrder {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * confirm
		 */
		confirm(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsSaleDeliveryOrderInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsSaleDeliveryOrderInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsSaleDeliveryOrderInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			confirm: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			confirm: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsSaleOrder {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * arrived
		 */
		arrived(data?: any): Promise<any>;
		/**
		 * confirm
		 */
		confirm(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * download
		 */
		download(data?: any): Promise<any>;
		/**
		 * downloadSign
		 */
		downloadSign(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsSaleOrderInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsSaleOrderInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsSaleOrderInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			arrived: string;
			confirm: string;
			delete: string;
			download: string;
			downloadSign: string;
			export: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			arrived: boolean;
			confirm: boolean;
			delete: boolean;
			download: boolean;
			downloadSign: boolean;
			export: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsSupplier {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsSupplierInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsSupplierInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsSupplierInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsSupplier_account {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * bindSupplier
		 */
		bindSupplier(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * getSupplierAccountList
		 */
		getSupplierAccountList(data?: any): Promise<any>;
		/**
		 * getUserBindSupplier
		 */
		getUserBindSupplier(data?: any): Promise<any>;
		/**
		 * getUserRole
		 */
		getUserRole(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsSupplierAccountInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsSupplierAccountInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsSupplierAccountInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			bindSupplier: string;
			delete: string;
			getSupplierAccountList: string;
			getUserBindSupplier: string;
			getUserRole: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			bindSupplier: boolean;
			delete: boolean;
			getSupplierAccountList: boolean;
			getUserBindSupplier: boolean;
			getUserRole: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsWarehouseDestinationInbound {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * confirm
		 */
		confirm(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * finish
		 */
		finish(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsWarehouseDestinationInboundInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsWarehouseDestinationInboundInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsWarehouseDestinationInboundInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * start
		 */
		start(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			confirm: string;
			delete: string;
			export: string;
			finish: string;
			info: string;
			list: string;
			page: string;
			start: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			confirm: boolean;
			delete: boolean;
			export: boolean;
			finish: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			start: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsWarehouseDestinationOutbound {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * complete
		 */
		complete(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsWarehouseDestinationOutboundInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsWarehouseDestinationOutboundInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsWarehouseDestinationOutboundInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * ship
		 */
		ship(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			complete: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			ship: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			complete: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			ship: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsWarehouseDestinationStock {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsWarehouseDestinationStockInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsWarehouseDestinationStockInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsWarehouseDestinationStockInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * record
		 */
		record: PmsWarehouseDestinationStockRecord;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			export: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			export: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsWarehouseDestinationStockRecord {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsWarehouseDestinationStockRecordInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsWarehouseDestinationStockRecordInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsWarehouseDestinationStockRecordInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsWarehousePoint {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsWarehousePointInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsWarehousePointInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsWarehousePointInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsWarehouseSourceInbound {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * compareInboundAndOutbound
		 */
		compareInboundAndOutbound(data?: any): Promise<any>;
		/**
		 * complete
		 */
		complete(data?: any): Promise<any>;
		/**
		 * confirm
		 */
		confirm(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * download
		 */
		download(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsWarehouseSourceInboundInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsWarehouseSourceInboundInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsWarehouseSourceInboundInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * revoke
		 */
		revoke(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			compareInboundAndOutbound: string;
			complete: string;
			confirm: string;
			delete: string;
			download: string;
			info: string;
			list: string;
			page: string;
			revoke: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			compareInboundAndOutbound: boolean;
			complete: boolean;
			confirm: boolean;
			delete: boolean;
			download: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			revoke: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsWarehouseSourceOutbound {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * export
		 */
		export(data?: any): Promise<any>;
		/**
		 * finish
		 */
		finish(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsWarehouseSourceOutboundInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsWarehouseSourceOutboundInfoEntity[]>;
		/**
		 * pack
		 */
		pack(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsWarehouseSourceOutboundInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * revoke
		 */
		revoke(data?: any): Promise<any>;
		/**
		 * ship
		 */
		ship(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			export: string;
			finish: string;
			info: string;
			list: string;
			pack: string;
			page: string;
			revoke: string;
			ship: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			export: boolean;
			finish: boolean;
			info: boolean;
			list: boolean;
			pack: boolean;
			page: boolean;
			revoke: boolean;
			ship: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsWorkOrder {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * addWorkOrder
		 */
		addWorkOrder(data?: any): Promise<any>;
		/**
		 * complete
		 */
		complete(data?: any): Promise<any>;
		/**
		 * delWorkOrder
		 */
		delWorkOrder(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsWorkOrderInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsWorkOrderInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsWorkOrderInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * queryPage
		 */
		queryPage(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * updateWorkOrder
		 */
		updateWorkOrder(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			addWorkOrder: string;
			complete: string;
			delWorkOrder: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			queryPage: string;
			update: string;
			updateWorkOrder: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			addWorkOrder: boolean;
			complete: boolean;
			delWorkOrder: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			queryPage: boolean;
			update: boolean;
			updateWorkOrder: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsFinanceBillpayment {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsFinanceBillPaymentInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsFinanceBillPaymentInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsFinanceBillPaymentInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsMaterialBundle {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsMaterialBundleInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsMaterialBundleInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsMaterialBundleInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * saveBundle
		 */
		saveBundle(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			saveBundle: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			saveBundle: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsMaterialDrawing {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * download
		 */
		download(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsMaterialDrawingInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsMaterialDrawingInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsMaterialDrawingInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * setAsDefault
		 */
		setAsDefault(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			download: string;
			info: string;
			list: string;
			page: string;
			setAsDefault: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			download: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			setAsDefault: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsMaterialInbound {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * complete
		 */
		complete(data?: any): Promise<any>;
		/**
		 * confirm
		 */
		confirm(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * detail
		 */
		detail(data?: any): Promise<any>;
		/**
		 * importInboundRecord
		 */
		importInboundRecord(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsMaterialInboundInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsMaterialInboundInfoEntity[]>;
		/**
		 * materialInboundExportExcel
		 */
		materialInboundExportExcel(data?: any): Promise<any>;
		/**
		 * materialInboundPage
		 */
		materialInboundPage(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsMaterialInboundInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * revoke
		 */
		revoke(data?: any): Promise<any>;
		/**
		 * revokeAndSync
		 */
		revokeAndSync(data?: any): Promise<any>;
		/**
		 * start
		 */
		start(data?: any): Promise<any>;
		/**
		 * submit
		 */
		submit(data?: any): Promise<any>;
		/**
		 * summary
		 */
		summary(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			complete: string;
			confirm: string;
			delete: string;
			detail: string;
			importInboundRecord: string;
			info: string;
			list: string;
			materialInboundExportExcel: string;
			materialInboundPage: string;
			page: string;
			revoke: string;
			revokeAndSync: string;
			start: string;
			submit: string;
			summary: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			complete: boolean;
			confirm: boolean;
			delete: boolean;
			detail: boolean;
			importInboundRecord: boolean;
			info: boolean;
			list: boolean;
			materialInboundExportExcel: boolean;
			materialInboundPage: boolean;
			page: boolean;
			revoke: boolean;
			revokeAndSync: boolean;
			start: boolean;
			submit: boolean;
			summary: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsMaterialOutbound {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * complete
		 */
		complete(data?: any): Promise<any>;
		/**
		 * confirm
		 */
		confirm(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * detail
		 */
		detail(data?: any): Promise<any>;
		/**
		 * exportScrap
		 */
		exportScrap(data?: any): Promise<any>;
		/**
		 * getPrintData
		 */
		getPrintData(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsMaterialOutboundInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsMaterialOutboundInfoEntity[]>;
		/**
		 * materialOutboundExportExcel
		 */
		materialOutboundExportExcel(data?: any): Promise<any>;
		/**
		 * materialOutboundPage
		 */
		materialOutboundPage(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsMaterialOutboundInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * revoke
		 */
		revoke(data?: any): Promise<any>;
		/**
		 * start
		 */
		start(data?: any): Promise<any>;
		/**
		 * submit
		 */
		submit(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			complete: string;
			confirm: string;
			delete: string;
			detail: string;
			exportScrap: string;
			getPrintData: string;
			info: string;
			list: string;
			materialOutboundExportExcel: string;
			materialOutboundPage: string;
			page: string;
			revoke: string;
			start: string;
			submit: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			complete: boolean;
			confirm: boolean;
			delete: boolean;
			detail: boolean;
			exportScrap: boolean;
			getPrintData: boolean;
			info: boolean;
			list: boolean;
			materialOutboundExportExcel: boolean;
			materialOutboundPage: boolean;
			page: boolean;
			revoke: boolean;
			start: boolean;
			submit: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsMaterialPrice {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * getPrices
		 */
		getPrices(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsMaterialPriceInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsMaterialPriceInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsMaterialPriceInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			getPrices: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			getPrices: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PmsProductGroup {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<PmsProductGroupInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<PmsProductGroupInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: PmsProductGroupInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface SpaceInfo {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * getConfig
		 */
		getConfig(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<SpaceInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<SpaceInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: SpaceInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			getConfig: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			getConfig: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface SpaceType {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<SpaceTypeInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<SpaceTypeInfoEntity[]>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: SpaceTypeInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			page: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface TaskInfo {
		/**
		 * add
		 */
		add(data?: any): Promise<any>;
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;
		/**
		 * info
		 */
		info(data?: any): Promise<TaskInfoEntity>;
		/**
		 * list
		 */
		list(data?: any): Promise<TaskInfoEntity[]>;
		/**
		 * log
		 */
		log(data?: any): Promise<any>;
		/**
		 * once
		 */
		once(data?: any): Promise<any>;
		/**
		 * page
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number };
			list: TaskInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * start
		 */
		start(data?: any): Promise<any>;
		/**
		 * stop
		 */
		stop(data?: any): Promise<any>;
		/**
		 * update
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			add: string;
			delete: string;
			info: string;
			list: string;
			log: string;
			once: string;
			page: string;
			start: string;
			stop: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			add: boolean;
			delete: boolean;
			info: boolean;
			list: boolean;
			log: boolean;
			once: boolean;
			page: boolean;
			start: boolean;
			stop: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	type Service = {
		request(options?: {
			url: string;
			method?: "POST" | "GET" | string;
			data?: any;
			params?: any;
			proxy?: boolean;
			[key: string]: any;
		}): Promise<any>;
		base: {
			comm: BaseComm;
			open: BaseOpen;
			sys: {
				department: BaseSysDepartment;
				log: BaseSysLog;
				menu: BaseSysMenu;
				param: BaseSysParam;
				role: BaseSysRole;
				user: BaseSysUser;
			};
		};
		dict: { info: DictInfo; type: DictType };
		pims: {
			autid: PimsAutid;
			config: PimsConfig;
			daliyReport: PimsDaliyReport;
			progress: PimsProgress;
			workitem: PimsWorkitem;
		};
		pms: {
			AbnormalWorkingHours: PmsAbnormalWorkingHours;
			DailyProductionReport: PmsDailyProductionReport;
			PartsCapacity: PmsPartsCapacity;
			StandardCapacity: PmsStandardCapacity;
			allDataCompare: PmsAllDataCompare;
			audit: { process: PmsAuditProcess; processor: PmsAuditProcessor };
			bom: PmsBom;
			clearance: { order: PmsClearanceOrder };
			daily_report_data: PmsDaily_report_data;
			dataDiff: PmsDataDiff;
			delivery_note: PmsDelivery_note;
			drawing: PmsDrawing;
			finance: PmsFinance;
			freight: { forwarder: PmsFreightForwarder };
			jobInstruction: PmsJobInstruction;
			material: PmsMaterial;
			materialStockLog: PmsMaterialStockLog;
			material_anomaly: PmsMaterial_anomaly;
			material_test: PmsMaterial_test;
			outsource: { inbound: PmsOutsourceInbound };
			payment: PmsPayment;
			product: PmsProduct;
			production: {
				ManHourDeduct: PmsProductionManHourDeduct;
				MaterialDeduct: PmsProductionMaterialDeduct;
				purchase: { order: PmsProductionPurchaseOrder };
				sale: { order: PmsProductionSaleOrder };
				schedule: PmsProductionSchedule;
			};
			productionData: {
				incoming: PmsProductionDataIncoming;
				processAbnormality: PmsProductionDataProcessAbnormality;
			};
			purchase: { contract: PmsPurchaseContract; order: PmsPurchaseOrder };
			sale: { delivery: { order: PmsSaleDeliveryOrder }; order: PmsSaleOrder };
			supplier: PmsSupplier;
			supplier_account: PmsSupplier_account;
			warehouse: {
				destination: {
					inbound: PmsWarehouseDestinationInbound;
					outbound: PmsWarehouseDestinationOutbound;
					stock: PmsWarehouseDestinationStock;
				};
				point: PmsWarehousePoint;
				source: {
					inbound: PmsWarehouseSourceInbound;
					outbound: PmsWarehouseSourceOutbound;
				};
			};
			workOrder: PmsWorkOrder;
		};
		space: { info: SpaceInfo; type: SpaceType };
		task: { info: TaskInfo };
	};
}
