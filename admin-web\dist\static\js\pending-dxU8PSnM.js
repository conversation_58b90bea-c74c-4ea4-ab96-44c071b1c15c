import{_ as e}from"./purchase-pending-order.vue_vue_type_script_setup_true_lang-CxJm1w1Z.js";import{c as o,q as t,o as r}from"./.pnpm-Kv7TmmH8.js";import"./index-BuqCFB-b.js";import"./index-BAHxID_w.js";import"./index-CBanFtSc.js";import"./material-table-columns.vue_vue_type_script_setup_true_lang-hBwyRg0n.js";const m=o({name:"undefined"}),u=o({...m,setup(n){return(p,_)=>(r(),t(e,{"show-completed":""}))}});export{u as default};
