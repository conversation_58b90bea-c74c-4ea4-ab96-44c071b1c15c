package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/imhuso/lookah-erp/admin/yc"
)

const TableNamePmsMaterialInbound = "pms_material_inbound"

// MaterialInboundType 定义入库类型
type MaterialInboundType int

// 0: 自定义入库 1: 采购订单入库 2: 盘盈调账 3: 生产退料 4:库存退货入库 9: 补退货
const (
	MaterialInboundTypeSelfSelect          MaterialInboundType = iota // 0
	MaterialInboundTypePurchase                                       // 1
	MaterialInboundTypeInventoryAdjustment                            // 2
	MaterialInboundTypeProductionReturn                               // 3
	MaterialInboundTypeInventoryReturn                                // 4
	_                                                                 // 跳过 5
	_                                                                 // 跳过 6
	_                                                                 // 跳过 7
	_                                                                 // 跳过 8
	MaterialInboundTypeReturnPolicy                                   // 9
)

type PmsMaterialInboundVo struct {
	//Id            int64       `json:"id"`
	Code          string      `json:"code"`
	PaymentStatus string      `json:"payment_status"`
	No            string      `json:"no"`
	OrderId       int64       `json:"orderId"`
	OrderNo       string      `json:"orderNo"` // 采购单号
	Type          int         `json:"type"`
	TypeLabel     string      `json:"typeLabel"`
	Status        int         `json:"status"`
	InboundTime   *gtime.Time `json:"inboundTime"`
	// 物料id
	MaterialId int64 `json:"materialId"`
	// 创建时间
	CreateTime     *gtime.Time `json:"createTime"`
	Voucher        string      `json:"voucher"`
	Remark         string      `json:"remark"`
	Quantity       float64     `json:"quantity"`
	ImportQuantity float64     `json:"importQuantity"`
	DiffQuantity   float64     `json:"diffQuantity"`
	ImportRemark   string      `json:"importRemark"`
	Po             string      `json:"po"` // 采购订单Po
	PurchaseId     int64       `json:"purchaseId"`
	// 采购数量
	PurchaseQuantity float64 `json:"purchaseQuantity"`
	// 物料名称
	MaterialName string `json:"materialName"`
	MaterialType string `json:"materialType"`
	Process      string `json:"process"` // 工艺
	CoverColor   string `json:"coverColor"`
	Unit         string `json:"unit"`
	// 型号
	Model              string      `json:"model"`
	ReceivedQuantity   float64     `json:"receivedQuantity"`
	UnitPrice          float64     `json:"unitPrice"`
	SupplierName       string      `json:"supplierName"`
	InboundOutboundKey int         `json:"inbound_outbound_key"`
	DeliveryDate       *gtime.Time `json:"deliveryDate"` // 交货日期
	MaterialIdList     []int64     `json:"materialIdList"`
}

type PmsMaterialInboundInfo struct {
	ID             int64                           `json:"-"`
	No             string                          `json:"no"`
	OrderId        int64                           `json:"orderId"`
	Status         int                             `json:"status"`
	Remark         string                          `json:"remark"`
	Type           int                             `json:"type"`
	TotalQuantity  float64                         `json:"totalQuantity"`
	WorkOrderId    int64                           `json:"workOrderId"`
	ProductsOutput []*MaterialInboundProductOutput `json:"products" orm:"with:inbound_id=id" gorm:"-"`

	Extras    []*PmsWorkOrderDetail `json:"extras" gorm:"-"`
	WorkOrder *PmsWorkOrder         `json:"workOrder" gorm:"-"`
}

// PmsMaterialInbound mapped from table <pms_material_inbound>
type PmsMaterialInbound struct {
	ID           int64       `json:"id"       gorm:"column:id;type:bigint(20);not null;primary_key;auto_increment;comment:ID;"` // ID
	No           string      `json:"no" gorm:"column:no;type:varchar(100);not null;default:'';comment:入库单号;"`
	CompleteTime *gtime.Time `json:"completeTime" gorm:"column:complete_time;type:datetime;comment:入库完成时间;"`
	InboundTime  *gtime.Time `json:"inboundTime" gorm:"column:inbound_time;type:date;index;comment:入库时间;"`
	Status       int         `json:"status" gorm:"column:status;type:int(11);not null;default:0;comment:状态;"`
	Remark       string      `json:"remark" gorm:"column:remark;type:varchar(500);not null;default:'';comment:备注;"`
	Voucher      string      `json:"voucher"      gorm:"column:voucher;type:varchar(1000);not null;default:'';comment:凭证;"` // 凭证
	Type         int         `json:"type" gorm:"column:type;type:tinyint(2);not null;default:0;comment:入库类型;"`
	// 是否提交审批
	IsSubmit       int         `json:"isSubmit" gorm:"column:is_submit;type:tinyint(1);not null;default:0;comment:是否提交审批; 1:已提交审批"`
	DeliveryNoteId int64       `json:"deliveryNoteId" gorm:"column:delivery_note_id;type:tinyint(20);not null;default:0;comment:供货单号"`
	OrderId        int64       `json:"orderId" gorm:"column:order_id;type:bigint(20);not null;default:0;comment:采购单订单ID;"`
	TotalQuantity  float64     `json:"totalQuantity" gorm:"column:total_quantity;type:decimal(14,4);not null;default:0.0000;comment:总数量;"`
	CreateTime     *gtime.Time `json:"createTime" gorm:"column:createTime;not null;index,priority:1;autoCreateTime;comment:创建时间"` // 创建时间
	DeleteTime     *gtime.Time `json:"-"         gorm:"column:deleteTime;type:datetime;comment:删除时间;"`                            // 删除时间
	// 工单id
	WorkOrderId    int64                           `json:"workOrderId" gorm:"column:work_order_id;type:bigint(20);not null;default:0;comment:工单ID;"` // 工单ID
	Products       []*PmsMaterialInboundProduct    `json:"-" orm:"with:inbound_id=id" gorm:"-"`
	ProductList    []*PmsMaterialInboundProduct    `json:"productList" gorm:"-"`
	ProductsOutput []*MaterialInboundProductOutput `json:"products" orm:"with:inbound_id=id" gorm:"-"`
	// 关联订单信息
	*PmsPurchaseOrderInboundOutput `orm:"with:id=order_id" gorm:"-"`
	// 关联生产订单
	*PmsProductionOrderPurchaseOutput `orm:"with:id=order_id" gorm:"-"`
}

// GroupName 返回分组名
func (m *PmsMaterialInbound) GroupName() string {
	return ""
}

// TableName PmsMaterialInbound table name
func (*PmsMaterialInbound) TableName() string {
	return TableNamePmsMaterialInbound
}

// NewPmsMaterialInbound 创建实例
func NewPmsMaterialInbound() *PmsMaterialInbound {
	return &PmsMaterialInbound{}
}

func init() {
	_ = yc.CreateTable(NewPmsMaterialInbound())
}
