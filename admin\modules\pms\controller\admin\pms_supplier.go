package admin

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/imhuso/lookah-erp/admin/modules/pms/service"
	"github.com/imhuso/lookah-erp/admin/yc"
)

type PmsSupplierController struct {
	*yc.Controller
}

// ExportSupplierReq 导出供应商
type ExportSupplierReq struct {
	g.Meta `path:"/exportSupplier" method:"GET"`
}

// ExportSupplier 导出供应商
func (c *PmsSupplierController) ExportSupplier(ctx context.Context, req *ExportSupplierReq) (res *yc.BaseRes, err error) {
	fileName, content, err := service.NewPmsSupplierService().ExportSupplier(ctx)
	if err != nil {
		return nil, err
	}

	response := g.RequestFromCtx(ctx).Response
	// 设置响应头
	response.Header().Set("Content-Disposition", "attachment; filename="+fileName)
	response.Header().Set("Content-Type", "application/octet-stream")

	// 返回 Excel 文件内容
	response.WriteExit(content)
	return nil, nil
}

func init() {
	var pmsSupplierController = &PmsSupplierController{
		&yc.Controller{
			Prefix:  "/admin/pms/supplier",
			Api:     []string{"Add", "Delete", "Update", "Info", "List", "Page"},
			Service: service.NewPmsSupplierService(),
		},
	}
	// 注册路由
	yc.RegisterController(pmsSupplierController)
}
