import{c as f,at as p,e as d,as as _,$ as m,au as g,i as s,f as o,o as l,s as k,h as i,j as h,w as y,t as S,F as L}from"./.pnpm-Kv7TmmH8.js";import{_ as $}from"./_plugin-vue_export-helper-DlAUqK2U.js";const x=f({name:"ClLink",components:{IconLink:p},props:{modelValue:[String,Array],href:[String,Array],text:{type:String,default:"查看"},target:{type:String,default:"_blank"}},setup(t){const r=d(()=>{const e=t.modelValue||t.href;return _(e)?e:m(e)?(e||"").split(",").filter(Boolean):[]});function a(e){return g(e.split("/"))}return{urls:r,filename:a}}}),C=["href","target"];function I(t,r,a,e,V,A){const c=s("IconLink"),u=s("el-icon");return l(!0),o(L,null,k(t.urls,n=>(l(),o("a",{key:n,class:"cl-link",href:n,target:t.target},[i(u,null,{default:y(()=>[i(c)]),_:1}),h(S(t.filename(n)),1)],8,C))),128)}const w=$(x,[["render",I],["__scopeId","data-v-175401c8"]]);export{w as default};
