import{i as u}from"./index-BuqCFB-b.js";import{c as d,b as D,S as E,q as _,w as t,h as o,G as M,i as n,H as B,v as N,j,E as a,T as I,o as m}from"./.pnpm-Kv7TmmH8.js";import{a as K}from"./index-BAHxID_w.js";const q=d({name:"sys-log"}),S=d({...q,setup(A){const{service:l}=K(),s=D(1),c=u.useCrud({service:l.base.sys.log},e=>{e.refresh()}),f=u.useTable({contextMenu:["refresh"],columns:[{type:"index",label:"#",width:60},{prop:"userId",label:"用户ID"},{prop:"name",label:"昵称",minWidth:150},{prop:"action",label:"请求地址",minWidth:200,showOverflowTooltip:!0},{prop:"params",label:"参数",minWidth:200,component:{name:"cl-code-json",props:{popover:!0}}},{prop:"ip",label:"ip",minWidth:150},{prop:"ipAddr",label:"ip地址",minWidth:150},{prop:"createTime",label:"创建时间",minWidth:160,sortable:"desc"}]});function b(){l.base.sys.log.setKeep({value:s.value}).then(()=>{a.success("保存成功")}).catch(e=>{a.error(e.message)})}function h(){I.confirm("是否要清空日志？","提示",{type:"warning"}).then(()=>{l.base.sys.log.clear().then(()=>{var e;a.success("清空成功"),(e=c.value)==null||e.refresh()}).catch(e=>{a.error(e.message)})}).catch(()=>null)}return E(()=>{l.base.sys.log.getKeep().then(e=>{s.value=Number(e)})}),(e,i)=>{const g=n("cl-refresh-btn"),v=n("el-button"),y=n("el-input-number"),x=n("cl-filter"),p=n("cl-flex1"),w=n("cl-search-key"),r=n("cl-row"),C=n("cl-table"),k=n("cl-pagination"),T=n("cl-crud"),W=B("permission");return m(),_(T,{ref_key:"Crud",ref:c},{default:t(()=>[o(r,null,{default:t(()=>[o(g),M((m(),_(v,{type:"danger",onClick:h},{default:t(()=>[j(" 清空 ")]),_:1})),[[W,N(l).base.sys.log.permission.clear]]),o(x,{label:"日志保存天数"},{default:t(()=>[o(y,{modelValue:s.value,"onUpdate:modelValue":i[0]||(i[0]=V=>s.value=V),"controls-position":"right",max:1e4,min:1,onChange:b},null,8,["modelValue"])]),_:1}),o(p),o(w,{placeholder:"搜索请求地址、参数、ip"})]),_:1}),o(r,null,{default:t(()=>[o(C,{ref_key:"Table",ref:f},null,512)]),_:1}),o(r,null,{default:t(()=>[o(p),o(k)]),_:1})]),_:1},512)}}});export{S as default};
