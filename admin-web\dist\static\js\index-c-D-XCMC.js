import{c as $,bq as H,z,f as y,y as s,h as t,v as o,W as A,i as b,w,t as S,br as X,bs as Z,F as O,s as R,o as h,Y as G,bt as ee,au as K,e as D,bu as te,q as C,j as oe,O as W,bv as se,G as V,I as F,b as E,B as L,af as Y,ag as J,bw as ne,bb as ae,bx as le,A as ce}from"./.pnpm-Kv7TmmH8.js";import{u as re}from"./page-config-BwmDs9Iw.js";import{k as P,i as ie,c as ue,z as Q,r as _e}from"./index-BuqCFB-b.js";import{a as I}from"./index-BAHxID_w.js";import{_ as M}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{_ as U,L as pe}from"./lang-select-bmyFIViv.js";import{_ as de}from"./index.vue_vue_type_script_setup_true_name_cl-avatar_lang-DyLe_N_c.js";const fe={class:"app-process"},me=["data-index","onClick","onContextmenu"],he=$({name:"app-process"}),ve=$({...he,setup(m){const{t:_}=H(),{refs:p,setRefs:r,route:d,router:f}=I(),{process:n}=P();function a(){if(!n.list.find(l=>l.active)){const l=K(n.list);f.push(l?l.fullPath:"/")}}function e(u){p.scroller.scrollTo({left:u,behavior:"smooth"})}function c(u){const l=p[`item-${u}`];l&&e(l.offsetLeft+l.clientWidth-p.scroller.clientWidth)}function v(u,l){c(l),f.push(u.fullPath)}function i(u){n.remove(u),a()}function k(u,l){ie.ContextMenu.open(u,{hover:{target:"app-process__item"},list:[{label:_("base.closeCurrent"),hidden:l.fullPath!==d.path,callback(g){i(n.list.findIndex(T=>T.fullPath===l.fullPath)),g(),a()}},{label:_("base.closeOther"),callback(g){n.set(n.list.filter(T=>T.fullPath===l.fullPath)),g(),a()}},{label:_("base.closeAll"),callback(g){n.clear(),g(),a()}}]})}return z(()=>d.path,u=>{c(n.list.findIndex(l=>l.fullPath===u)||0)}),(u,l)=>{const g=b("el-icon"),T=b("el-scrollbar");return h(),y("div",fe,[s("div",{class:A(["app-process__icon",{active:o(d).path==="/"}]),onClick:l[0]||(l[0]=x=>o(f).push("/"))},[t(g,null,{default:w(()=>[t(o(X))]),_:1}),s("span",null,S(o(_)("base.home")),1)],2),s("div",{class:"app-process__icon",onClick:l[1]||(l[1]=(...x)=>o(f).back&&o(f).back(...x))},[t(g,null,{default:w(()=>[t(o(Z))]),_:1}),s("span",null,S(o(_)("base.back")),1)]),t(T,{ref:o(r)("scroller"),class:"app-process__scroller"},{default:w(()=>[(h(!0),y(O,null,R(o(n).list,(x,N)=>{var q;return h(),y("div",{key:N,ref_for:!0,ref:o(r)(`item-${N}`),class:A(["app-process__item",{active:x.active}]),"data-index":N,onClick:j=>v(x,Number(N)),onContextmenu:G(j=>k(j,x),["stop","prevent"])},[s("span",null,S(o(_)(`base.menu.${((q=x.meta)==null?void 0:q.label)||x.name||x.path}`)),1),t(g,{onMousedown:G(j=>i(Number(N)),["stop"])},{default:w(()=>[t(o(ee))]),_:2},1032,["onMousedown"])],42,me)}),128))]),_:1},512)])}}}),be=M(ve,[["__scopeId","data-v-4adda9db"]]),we={class:"route-nav"},$e={key:0,class:"title"},ke=$({name:"route-nav"}),ye=$({...ke,setup(m){const{t:_}=H(),{route:p,browser:r}=I(),{menu:d}=P(),f=D(()=>{function a(e){if(p.path==="/")return!1;if(e.path==p.path)return e;if(e.children){const c=e.children.map(a).find(Boolean);return c?[e,c]:!1}else return!1}return te(d.group.map(a).filter(Boolean))}),n=D(()=>{var a;return(a=K(f.value))==null?void 0:a.name});return(a,e)=>{const c=b("el-breadcrumb-item"),v=b("el-breadcrumb");return h(),y("div",we,[o(r).isMini?(h(),y("p",$e,S(n.value),1)):(h(),C(v,{key:1},{default:w(()=>[(h(!0),y(O,null,R(f.value,(i,k)=>(h(),C(c,{key:k},{default:w(()=>{var u;return[oe(S(o(_)(`base.menu.${((u=i.meta)==null?void 0:u.label)||i.name}`)),1)]}),_:2},1024))),128))]),_:1}))])}}}),ge=M(ye,[["__scopeId","data-v-702c24a5"]]);function xe(m){return typeof m=="function"||Object.prototype.toString.call(m)==="[object Object]"&&!se(m)}const Se=$({name:"BMenu",setup(){const{router:m,route:_,browser:p}=I(),{menu:r,app:d}=ue(),{t:f}=Q();function n(e){e!==_.path&&m.push(e),p.isMini&&d.fold(!0)}function a(){function e(c,v){return c.filter(i=>i.isShow).map(i=>{let k=null;const u=l=>t("div",{class:"wrap"},[t(b("cl-svg"),{name:l.icon},null),V(t("span",null,[f(`base.menu.${l.name}`)]),[[F,!d.isFold||v!==1]])]);return i.type===0?k=W(t(b("el-sub-menu"),null,null),{index:String(i.id),key:i.id,popperClass:"app-slider__menu"},{title(){return u(i)},default(){return e(i.children||[],v+1)}}):k=W(t(b("el-menu-item"),null,null),{index:i.path,key:i.id},{default(){return u(i)}}),k})}return e(r.list,1)}return()=>{let e;return t("div",{class:"app-slider__menu"},[t(b("el-menu"),{"default-active":_.path,"background-color":"transparent","collapse-transition":!0,collapse:p.isMini?!1:d.isFold,onSelect:n},xe(e=a())?e:{default:()=>[e]})])}}}),Ce={class:"app-slider"},Ie=s("img",{src:U},null,-1),Pe=[Ie],Me={class:"app-slider__container"},Be=$({name:"app-slider"}),Te=$({...Be,setup(m){function _(){_e.push({path:"/"})}return(p,r)=>(h(),y("div",Ce,[s("div",{class:"app-slider__logo",onClick:_},Pe),s("div",Me,[t(o(Se))])]))}}),Ne={class:"a-menu"},Ae={class:"a-menu__name"},Ve=$({name:"a-menu"}),Fe=$({...Ve,setup(m){const{router:_,route:p}=I(),{menu:r}=P(),{t:d}=Q(),f=E("");function n(e){r.setMenu(e);const c=r.getPath(r.group[e]);_.push(c)}function a(){function e(c,v){switch(c.type){case 0:(c.children||[]).forEach(i=>{e(i,v)});break;case 1:p.path.includes(c.path)&&(f.value=String(v),r.setMenu(v));break}}r.group.forEach(e)}return z(()=>r.group.length,()=>{a()},{immediate:!0}),z(()=>p.path,()=>{a()}),(e,c)=>{const v=b("cl-svg"),i=b("el-menu-item"),k=b("el-menu");return h(),y("div",Ne,[t(k,{"default-active":f.value,mode:"horizontal","background-color":"transparent",onSelect:n},{default:w(()=>[(h(!0),y(O,null,R(o(r).group,(u,l)=>(h(),C(i,{key:l,index:`${l}`},{default:w(()=>[u.icon?(h(),C(v,{key:0,name:u.icon,size:18},null,8,["name"])):L("",!0),s("span",Ae,S(o(d)(`base.menu.${u.name}`)),1)]),_:2},1032,["index"]))),128))]),_:1},8,["default-active"])])}}}),je=M(Fe,[["__scopeId","data-v-1d21c257"]]),B=m=>(Y("data-v-01a409ec"),m=m(),J(),m),ze={class:"layout-header layout-header-fix layout-header-color-primary layout-header-stick layout-header-with-menu"},De={class:"layout-header-left"},Ee=B(()=>s("img",{src:U},null,-1)),Le=[Ee],Oe=B(()=>s("i",{class:"cl-iconfont cl-icon-fold"},null,-1)),Re=[Oe],qe={class:"app-topbar"},Ge={class:"app-topbar__tools"},We={key:0,class:"app-topbar__user"},He={class:"el-dropdown-link"},Ke={class:"name"},Ye=B(()=>s("i",{class:"cl-iconfont cl-icon-user"},null,-1)),Je=B(()=>s("span",null,"个人中心",-1)),Qe=B(()=>s("i",{class:"cl-iconfont cl-icon-exit"},null,-1)),Ue=B(()=>s("span",null,"退出",-1)),Xe=$({name:"app-topbar"}),Ze=$({...Xe,setup(m){const{router:_,service:p}=I(),{user:r,app:d}=P();async function f(n){switch(n){case"my":_.push("/my/info");break;case"exit":await p.base.comm.logout(),r.logout();break}}return(n,a)=>{const e=b("cl-theme"),c=b("el-dropdown-item"),v=b("el-dropdown-menu"),i=b("el-dropdown");return h(),y("div",ze,[s("div",De,[s("a",{href:"javascript:void(0);",class:"layout-header__logo",onClick:a[0]||(a[0]=k=>o(_).push("/"))},Le),s("div",{class:A(["app-topbar__collapse",{unfold:!o(d).isFold}]),onClick:a[1]||(a[1]=k=>o(d).fold())},Re,2)]),o(d).info.menu.isGroup?(h(),C(je,{key:0})):L("",!0),s("div",qe,[s("ul",Ge,[s("li",null,[t(pe)]),s("li",null,[t(e)])]),o(r).info?(h(),y("div",We,[t(i,{trigger:"click","hide-on-click":"",onCommand:f},{dropdown:w(()=>[t(v,null,{default:w(()=>[t(c,{command:"my"},{default:w(()=>[Ye,Je]),_:1}),t(c,{command:"exit"},{default:w(()=>[Qe,Ue]),_:1})]),_:1})]),default:w(()=>[s("span",He,[s("span",Ke,S(o(r).info.name),1),t(de,{src:o(r).info.headImg,title:o(r).info.name},null,8,["src","title"])])]),_:1})])):L("",!0)])])}}}),et=M(Ze,[["__scopeId","data-v-01a409ec"]]),tt={class:"app-views"},ot=$({name:"app-views"}),st=$({...ot,setup(m){const{refs:_,setRefs:p,mitt:r}=I(),{process:d,app:f}=P(),n=D(()=>d.list.filter(e=>{var c;return(c=e.meta)==null?void 0:c.keepAlive}).map(e=>e.path.substring(1,e.path.length).replace(/\//g,"-")));function a({el:e,top:c}){const v=_.scrollbar.wrapRef;e&&(c=v.querySelector(e).offsetTop),v.scrollTo({top:c,behavior:"smooth"})}return r.on("view.scrollTo",a),(e,c)=>{const v=b("el-scrollbar"),i=b("router-view");return h(),y("div",tt,[t(i,null,{default:w(({Component:k})=>[t(v,{ref:o(p)("scrollbar")},{default:w(()=>[t(ne,{name:o(f).info.router.transition},{default:w(()=>[(h(),C(le,{include:n.value},[(h(),C(ae(k)))],1032,["include"]))]),_:2},1032,["name"])]),_:2},1536)]),_:1})])}}}),nt=M(st,[["__scopeId","data-v-50e02b28"]]),at=m=>(Y("data-v-49e3be21"),m=m(),J(),m),lt={class:"app-layout__left"},ct=at(()=>s("div",{style:{height:"48px"}},null,-1)),rt={class:"page-header"},it=$({name:"undefined"}),ut=$({...it,setup(m){const{route:_}=I(),{app:p}=P(),{getPageConfig:r}=re(),d=E(!0),f=E(!0);return ce(()=>{const n=r(_.path),a=n==null?void 0:n.isShowSlider,e=n==null?void 0:n.isShowAppProcess;f.value=p.info.menu.isGroup&&a!==!1,d.value=e!==!1}),(n,a)=>(h(),y("div",{class:A(["app-layout",{collapse:o(p).isFold}])},[s("div",{class:"app-layout__mask",onClick:a[0]||(a[0]=e=>o(p).fold(!0))}),V(s("div",lt,[t(Te)],512),[[F,f.value]]),s("div",{class:A(`app-layout__right layout-content layout-content-fix-with-header layout-content-with-tabs layout-content-with-tabs-fix ${f.value?"":"layout-content-no-left"}`)},[t(et),ct,V(t(be,null,null,512),[[F,d.value]]),V(s("div",rt,[t(ge)],512),[[F,d.value]]),t(nt)],2)],2))}}),bt=M(ut,[["__scopeId","data-v-49e3be21"]]);export{bt as default};
