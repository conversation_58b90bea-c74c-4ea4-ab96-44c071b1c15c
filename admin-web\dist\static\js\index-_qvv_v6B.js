import{c as V,an as y,b as k,z as B,ao as C,a0 as D,ap as N,Q as E,i as p,f as I,o as w,h as c,w as n,j as i,y as h,t as A,af as S,ag as $}from"./.pnpm-Kv7TmmH8.js";import{_ as x}from"./_plugin-vue_export-helper-DlAUqK2U.js";const U=V({name:"ClColumnCustom",components:{Draggable:y},props:{name:String,columns:{type:Array,required:!0,default:()=>[]}},setup(o){const l=k(!1),m=`column-custom__${o.name||location.pathname}`,s=k([]);function f(){D(()=>{o.columns.forEach(a=>{var t;!a.type&&a.prop&&(a.hidden=!((t=s.value.find(e=>e.prop===a.prop))!=null&&t.checked),a.orderNum=s.value.findIndex(e=>e.prop===a.prop))})})}function b(){C.set(m,s.value),f(),r()}function d(){l.value=!0}function r(){l.value=!1}return B(()=>o.columns,a=>{if(a){const t=C.get(m);s.value=N(a.filter(e=>!e.type&&e.prop).map(e=>{var v;let u=!0,g=e.orderNum||0;return E(e.hidden)&&(u=!e.hidden),t&&(u=(v=t.find(_=>_.prop==e.prop))==null?void 0:v.checked,g=t.findIndex(_=>_.prop==e.prop)),{label:e.label,prop:e.prop,checked:u,orderNum:g}}),"orderNum","asc"),f()}}),{visible:l,list:s,open:d,close:r,save:b}}}),T=o=>(S("data-v-b6ef8f2e"),o=o(),$(),o),j={class:"cl-column-custom__wrap"},q={class:"cl-column-custom__dialog"},z={class:"left"},F=T(()=>h("div",{class:"right"},null,-1));function Q(o,l,m,s,f,b){const d=p("el-button"),r=p("el-checkbox"),a=p("Draggable"),t=p("cl-dialog");return w(),I("div",j,[c(d,{onClick:o.open},{default:n(()=>[i(" 自定义列 ")]),_:1},8,["onClick"]),c(t,{modelValue:o.visible,"onUpdate:modelValue":l[1]||(l[1]=e=>o.visible=e),title:"自定义列"},{footer:n(()=>[c(d,{onClick:o.close},{default:n(()=>[i(" 取消 ")]),_:1},8,["onClick"]),c(d,{type:"success",onClick:o.save},{default:n(()=>[i(" 保存 ")]),_:1},8,["onClick"])]),default:n(()=>[h("div",q,[h("div",z,[c(a,{modelValue:o.list,"onUpdate:modelValue":l[0]||(l[0]=e=>o.list=e)},{item:n(({element:e})=>[c(r,{modelValue:e.checked,"onUpdate:modelValue":u=>e.checked=u,border:""},{default:n(()=>[i(A(e.label),1)]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1},8,["modelValue"])]),F])]),_:1},8,["modelValue"])])}const J=x(U,[["render",Q],["__scopeId","data-v-b6ef8f2e"]]);export{J as default};
