import{_ as t}from"./space-inner.vue_vue_type_style_index_0_lang-mJhsGsb5.js";import{c as o,q as r,o as e}from"./.pnpm-Kv7TmmH8.js";import"./index-BuqCFB-b.js";import"./index-BAHxID_w.js";import"./hook-B9KKc1yL.js";import"./viewer.vue_vue_type_script_setup_true_name_item-viewer_lang-Y2-MQXPQ.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const p=o({name:"upload-list"}),d=o({...p,setup(a){return(m,_)=>(e(),r(t))}});export{d as default};
