package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/imhuso/lookah-erp/admin/yc"
)

const TableNamePmsMaterialOutbound = "pms_material_outbound"

type MaterialOutboundType int

// MaterialOutboundTypeReturn
// 0: 领料出库 MaterialOutboundTypePick
// 1: 退货出库 MaterialOutboundTypeReturn
// 2: 自定义出库 MaterialOutboundTypeCustom
// 3: 报废 MaterialOutboundTypeScrap
// 4: 库存退货出库 MaterialOutboundTypeReturnInventory

const (
	MaterialOutboundTypePick            MaterialOutboundType = 0
	MaterialOutboundTypeReturn          MaterialOutboundType = 1
	MaterialOutboundTypeCustom          MaterialOutboundType = 2
	MaterialOutboundTypeScrap           MaterialOutboundType = 3
	MaterialOutboundTypeReturnInventory MaterialOutboundType = 4
	MaterialOutboundTypeSubcontract     MaterialOutboundType = 5
)

type MaterialOutboundProductType struct {
	InboundOutboundKey int                                     `json:"inbound_outbound_key"`
	TotalQuantity      float64                                 `json:"total_quantity"`
	ContractId         int64                                   `json:"contractId"`
	MaterialId         int64                                   `json:"materialId"`
	Quantity           float64                                 `json:"quantity"`
	Address            string                                  `json:"address"`
	HandlingMethod     string                                  `json:"handling_method"`
	Responsible        string                                  `json:"responsible"`
	ScrapDate          *gtime.Time                             `json:"scrap_date"`
	ProductGroupId     int64                                   `json:"product_group_id"`
	MateriaLdetail     *PmsPurchaseOrderWithSummaryExtraOutput `json:"materiaLdetail"`
}

// MaterialOutboundDataPick 领料出库
type MaterialOutboundDataPick struct {
	MaterialId int64   `json:"materialId"`
	Quantity   float64 `json:"quantity"`
}

type PmsMaterialOutboundInfo struct {
	ID            int64   `json:"-"`
	No            string  `json:"no"`
	OrderId       int64   `json:"orderId"`
	Status        int     `json:"status"`
	Remark        string  `json:"remark"`
	Type          int     `json:"type"`
	TotalQuantity float64 `json:"totalQuantity"`
	WorkOrderId   int64   `json:"workOrderId"`

	ProductsOutput []*MaterialOutboundProductOutput `json:"products" orm:"with:outbound_id=id" gorm:"-"`

	Extras    []*PmsWorkOrderDetail `json:"extras" gorm:"-"`
	WorkOrder *PmsWorkOrder         `json:"workOrder" gorm:"-"`
}

// PmsMaterialOutbound mapped from table <pms_material_outbound>
type PmsMaterialOutbound struct {
	ID            int64       `json:"id"       gorm:"column:id;type:bigint(20);not null;primary_key;auto_increment;comment:ID;"`          // ID
	CompleteTime  *gtime.Time `json:"completeTime"  gorm:"column:complete_time;type:datetime;comment:出库完成时间;"`                            // 出库完成时间
	OutboundTime  *gtime.Time `json:"outboundTime"  gorm:"column:outbound_time;type:date;index;comment:出库时间;"`                            // 出库时间
	Status        int         `json:"status"        gorm:"column:status;type:int(11);not null;default:0;comment:状态;"`                     // 状态
	Remark        string      `json:"remark"        gorm:"column:remark;type:varchar(500);not null;default:'';comment:备注;"`               // 备注
	TotalQuantity float64     `json:"totalQuantity" gorm:"column:total_quantity;type:decimal(14,4);not null;default:0.0000;comment:总数量;"` // 总数量
	Type          int         `json:"type"      gorm:"column:type;type:tinyint(1);not null;default:0;comment:类型;"`                        // 类型
	// 是否提交审批
	IsSubmit             int                              `json:"isSubmit" gorm:"column:is_submit;type:tinyint(1);not null;default:0;comment:是否提交审批; 1:已提交审批"`
	Voucher              string                           `json:"voucher"       gorm:"column:voucher;type:varchar(1000);not null;comment:出库单凭证;"`                                  // 出库单凭证
	CreateTime           *gtime.Time                      `json:"createTime" gorm:"column:createTime;not null;index,priority:1;autoCreateTime;comment:创建时间"`                       // 创建时间
	DeleteTime           *gtime.Time                      `json:"-"            gorm:"column:deleteTime;type:datetime;comment:删除时间;"`                                               // 删除时间
	No                   string                           `json:"no"            gorm:"column:no;type:varchar(100);not null;default:'';comment:出库单号;"`                              // 出库单号
	ProductionScheduleSn string                           `json:"production_schedule_sn" gorm:"column:production_schedule_sn;type:varchar(100);not null;default:'';comment:生产单号;"` // 生产单号
	OrderId              int64                            `json:"orderId"       gorm:"column:order_id;type:bigint(20);not null;default:0;comment:订单ID;"`                           // 订单ID
	WorkOrderNo          string                           `json:"workOrderNo" gorm:"column:work_order_no;type:varchar(100);not null;default:'';comment:工单号;"`                      // 工单号
	WorkOrderId          int64                            `json:"workOrderId" gorm:"column:work_order_id;type:bigint(20);not null;default:0;comment:工单ID;"`                        // 工单ID
	ProductId            int64                            `json:"productId" gorm:"column:product_id;type:bigint(20);not null;default:0;comment:产品ID;"`                             // 产品ID
	IsReplenish          int                              `json:"isReplenish" gorm:"column:is_replenish;type:tinyint(1);not null;default:0;comment:是否完成补货; 1:已完成补货"`
	Products             []*PmsMaterialOutboundProduct    `json:"-" orm:"with:outbound_id=id" gorm:"-"`
	ProductList          []*PmsMaterialOutboundProduct    `json:"productList" gorm:"-"`
	ProductsOutput       []*MaterialOutboundProductOutput `json:"products" orm:"with:outbound_id=id" gorm:"-"`
}

// GroupName 返回分组名
func (m *PmsMaterialOutbound) GroupName() string {
	return ""
}

// TableName PmsMaterialOutbound table name
func (*PmsMaterialOutbound) TableName() string {
	return TableNamePmsMaterialOutbound
}

// NewPmsMaterialOutbound 创建实例
func NewPmsMaterialOutbound() *PmsMaterialOutbound {
	return &PmsMaterialOutbound{}
}

func init() {
	_ = yc.CreateTable(NewPmsMaterialOutbound())
}
