import{i as l}from"./index-BuqCFB-b.js";import{c as _,q as T,w as o,h as e,i as t,y as V,o as U}from"./.pnpm-Kv7TmmH8.js";import{a as W}from"./index-BAHxID_w.js";const L=V("span",{style:{marginLeft:"10px",fontSize:"12px"}},"是否关联上下级",-1),q=_({name:"sys-role"}),S=_({...q,setup(B){const{service:i}=W(),u=l.useCrud({service:i.base.sys.role},n=>{n.refresh()}),r=l.useUpsert({dialog:{width:"800px"},items:[{prop:"name",label:"名称",span:12,required:!0,component:{name:"el-input"}},{prop:"label",label:"标识",span:12,required:!0,component:{name:"el-input"}},{prop:"remark",label:"备注",span:24,component:{name:"el-input",props:{type:"textarea",rows:4}}},{label:"功能权限",prop:"menuIdList",value:[],component:{name:"cl-menu-check"}},{label:"数据权限",prop:"relevance",flex:!1,component:{name:"slot-relevance"}},{label:"",prop:"departmentIdList",value:[],component:{name:"cl-dept-check",props:{},style:{marginTop:"-10px"}}}],plugins:[l.setFocus()],onOpened(n){p(n.relevance||0)}}),d=l.useTable({columns:[{type:"selection",width:60},{prop:"name",label:"名称",minWidth:150},{prop:"label",label:"标识",minWidth:120},{prop:"remark",label:"备注",showOverflowTooltip:!0,minWidth:150},{prop:"createTime",label:"创建时间",sortable:"custom",minWidth:160},{prop:"updateTime",label:"更新时间",sortable:"custom",minWidth:160},{label:"操作",type:"op",buttons:["edit","delete"]}]});function p(n){var a;(a=r.value)==null||a.setProps("departmentIdList",{checkStrictly:n==0})}return(n,a)=>{const b=t("cl-refresh-btn"),f=t("cl-add-btn"),h=t("cl-multi-delete-btn"),s=t("cl-flex1"),v=t("cl-search-key"),c=t("cl-row"),y=t("cl-table"),x=t("cl-pagination"),k=t("el-switch"),w=t("cl-upsert"),g=t("cl-crud");return U(),T(g,{ref_key:"Crud",ref:u},{default:o(()=>[e(c,null,{default:o(()=>[e(b),e(f),e(h),e(s),e(v)]),_:1}),e(c,null,{default:o(()=>[e(y,{ref_key:"Table",ref:d,"default-sort":{prop:"createTime",order:"descending"}},null,512)]),_:1}),e(c,null,{default:o(()=>[e(s),e(x)]),_:1}),e(w,{ref_key:"Upsert",ref:r},{"slot-relevance":o(({scope:m})=>[e(k,{modelValue:m.relevance,"onUpdate:modelValue":C=>m.relevance=C,"active-value":1,"inactive-value":0,onChange:p},null,8,["modelValue","onUpdate:modelValue"]),L]),_:1},512)]),_:1},512)}}});export{S as default};
