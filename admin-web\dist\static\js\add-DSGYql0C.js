import{c as Z,b as g,A as ve,r as fe,U as _e,E as u,f as A,y as c,h as l,t as z,w as r,i as v,F as j,s as J,q as L,B as $,j as Q,v as F,al as he,am as ye,o as b,af as Ie,ag as be}from"./.pnpm-Kv7TmmH8.js";import{c as ge,s as C}from"./index-BuqCFB-b.js";import{a as Ce}from"./index-BAHxID_w.js";import{_ as ke}from"./_plugin-vue_export-helper-DlAUqK2U.js";const D=U=>(Ie("data-v-c149bbba"),U=U(),be(),U),xe={class:"delivery-note-add"},Qe={class:"header-container"},Ve={class:"form-section"},ze=D(()=>c("div",{class:"section-title"}," 基本信息 ",-1)),we={key:0,style:{color:"#999","font-size":"12px","margin-top":"5px"}},qe={key:1,style:{color:"#67c23a","font-size":"12px","margin-top":"5px"}},Ae={class:"form-section"},Me={class:"section-header"},Pe=D(()=>c("div",{class:"section-title"}," 物料信息 ",-1)),Se={key:0,class:"empty-material-tip"},Oe=D(()=>c("span",{class:"required-field"},"数量",-1)),Ee={class:"ellipsis-cell"},Le={class:"ellipsis-cell"},Ue={class:"ellipsis-cell"},Ne={class:"ellipsis-cell"},Te={class:"ellipsis-cell"},Be={key:2,class:"add-material-button-container"},$e={class:"form-actions"},Fe={class:"mode-dialog-content"},De={class:"dialog-icon"},Ge={class:"dialog-message"},Re={class:"dialog-buttons"},je=Z({name:"undefined"}),Je=Z({...je,setup(U){const M=g([]),f=g([]),p=g([]),N=g(!1),y=g(!1),I=g(0),{router:w}=Ce(),{user:V}=ge(),k=g(!1);ve(async()=>{await K();const t=w.currentRoute.value.query.id;t&&(k.value=!0,H(t))});function H(t){C.pms.delivery_note.getDeliveryInfo({id:t}).then(async e=>{var P;s.id=e.id,s.supplierId=e.supplier_id,s.supplierOrderNo=e.supplier_order_no,s.po=e.po,s.voucher=e.voucher,s.remark=e.remark,s.orderId=e.orderId,s.no=e.no,e.po&&I.value&&await se(e.po);const a=((P=e.products)==null?void 0:P.map(n=>{const d=_e(n);return{quantity:d.quantity,contractId:d.contractId,materialId:d.materialId,createTime:d.createTime||"",status:d.status||0,remark:d.remark||"",expectedInbound:d.contract.expectedQuantity,receivedQuantity:d.contract.receivedQuantity,name:d.contract.material.name,model:d.contract.material.model,size:d.contract.material.size,material:d.contract.material.material,unit:d.contract.material.unit,process:d.contract.material.process,code:d.contract.material.code,coverColor:d.contract.material.coverColor||"",orderId:d.contract.purchaseId}}))||[],q=[...a.map(n=>({id:n.materialId,code:n.code,name:n.name,model:n.model,size:n.size,material:n.material,unit:n.unit,process:n.process,coverColor:n.coverColor,contractId:n.contractId,expectedQuantity:n.expectedInbound,receivedQuantity:n.receivedQuantity,purchaseId:n.orderId}))];p.value.forEach(n=>{q.some(d=>d.id===n.id)||q.push(n)}),p.value=q,m.value=a}).catch(e=>{u.error({message:e.message||"获取送货单信息失败"})})}async function K(){var a;V.info=V.info||{};const t=g(""),e=(a=V==null?void 0:V.info)==null?void 0:a.id;C.pms.supplier_account.request({url:"/getUserRole",method:"POST",data:{user_id:e}}).then(i=>{t.value=i.name,t.value==="供应商"?W():(u.error("本页面只供供应商账号访问！"),w.push("/pms/delivery_note"))}).catch(i=>{u.error("查询角色信息失败！"),w.push("/pms/delivery_note")})}function W(){var t;C.pms.supplier_account.request({url:"/getUserBindSupplier",method:"GET",params:{user_id:(t=V==null?void 0:V.info)==null?void 0:t.id}}).then(e=>{I.value=e.supplier_id,s.supplierId=I.value,X(e.supplier_id)}).catch(e=>{w.push("/pms/delivery_note")})}function X(t){N.value=!0,C.pms.purchase.contract.request({url:"/getUnfinishedPo",method:"GET",params:{supplier_id:t}}).then(e=>{if(e){if(Array.isArray(e))M.value=e.map(a=>{if(typeof a=="string")return{po:a};if(a&&typeof a.po=="string")return a;if(a&&a.po_number)return{po:a.po_number};for(const i in a)if(typeof a[i]=="string"&&/^[A-Z0-9]+$/i.test(a[i]))return{po:a[i]};return{po:JSON.stringify(a)}});else if(typeof e=="object"){for(const a in e)if(Array.isArray(e[a])){M.value=e[a].map(i=>typeof i=="string"?{po:i}:i&&typeof i.po=="string"?i:{po:JSON.stringify(i)});break}}u.success(`已加载${M.value.length}个PO号`)}N.value=!1}).catch(e=>{u.error("获取PO列表失败"),N.value=!1})}const s=fe({id:"",no:"",po:"",remark:"",voucher:"",orderId:"",supplierId:I.value,supplierOrderNo:""}),m=g([]),S=g(!1),O=g("");function Y(t){m.value.length>0&&m.value.splice(t,1)}function T(){const t={quantity:void 0,contractId:void 0,materialId:void 0,createTime:"",status:0,remark:"",expectedInbound:void 0,receivedQuantity:void 0,name:"",model:"",size:"",material:"",unit:"",process:"",coverColor:"",orderId:0,code:""};m.value.push(t)}function ee(t,e){return m.value.some(a=>a!==e&&a.materialId===t)}function ae(t,e){const a=p.value.find(i=>i.id===t);a&&(e.name=a.name,e.model=a.model,e.size=a.size,e.material=a.material,e.unit=a.unit,e.process=a.process,e.code=a.code,e.coverColor=a.coverColor||"",a.contractId&&(e.contractId=a.contractId,e.expectedInbound=a.expectedQuantity,e.receivedQuantity=a.receivedQuantity,e.orderId=a.purchaseId))}function le(t){!t||!I.value||(y.value=!0,C.pms.purchase.contract.request({url:"/getContractListByPoAndSupplierId",method:"GET",params:{po:t,supplier_id:I.value}}).then(e=>{e&&Array.isArray(e)?(f.value=e,p.value=e.map(a=>({id:a.materialId,code:a.material.code,name:a.material.name,model:a.material.model,size:a.material.size,material:a.material.material,unit:a.material.unit,process:a.material.process,coverColor:a.material.coverColor||"",contractId:a.id,expectedQuantity:a.expectedQuantity,receivedQuantity:a.receivedQuantity,purchaseId:a.purchaseId})),u.success(`已加载${p.value.length}个可选物料`)):(f.value=[],p.value=[],u.warning("未找到相关物料")),y.value=!1}).catch(e=>{u.error("获取物料列表失败"),f.value=[],p.value=[],y.value=!1}))}function G(t){if(S.value=!1,t==="auto")B(O.value);else if(k.value)ne(O.value);else{m.value=[];for(let e=0;e<10;e++)T();le(O.value)}}function te(){S.value=!1,s.po="",O.value=""}function oe(){if(!s.po){u.error("请选择送货单PO号");return}const t=m.value.filter(a=>{if(!a.materialId)return!1;const i=Number(a.quantity);return!(a.quantity===void 0||a.quantity===""||Number.isNaN(i)||i===0)});if(t.length===0){u.error("请至少选择一个物料并填写数量");return}t.length>0&&(s.orderId=String(t[0].orderId));const e={delivery_note:s,materials:t};k.value?C.pms.delivery_note.update(e).then(a=>{u.success("修改成功"),w.push("/pms/delivery_note")}).catch(a=>{u.error(a.message||"修改失败")}):C.pms.delivery_note.add(e).then(a=>{u.success("创建成功"),w.push("/pms/delivery_note")}).catch(a=>{u.error(a.message||"创建失败")})}function ie(t){t?(O.value=t,S.value=!0):(f.value=[],k.value||(m.value=[]))}function B(t){!t||!I.value||(y.value=!0,C.pms.purchase.contract.request({url:"/getContractListByPoAndSupplierId",method:"GET",params:{po:t,supplier_id:I.value}}).then(e=>{e&&Array.isArray(e)?(f.value=e,p.value=e.map(a=>({id:a.materialId,code:a.material.code,name:a.material.name,model:a.material.model,size:a.material.size,material:a.material.material,unit:a.material.unit,process:a.material.process,coverColor:a.material.coverColor||"",contractId:a.id,expectedQuantity:a.expectedQuantity,receivedQuantity:a.receivedQuantity,purchaseId:a.purchaseId})),re(e),u.success(`已加载${f.value.length}个物料`)):(f.value=[],m.value=[],p.value=[],u.warning("未找到相关物料")),y.value=!1}).catch(e=>{u.error("获取物料列表失败"),f.value=[],m.value=[],p.value=[],y.value=!1}))}function re(t){m.value=t.map(e=>({quantity:void 0,contractId:e.id,materialId:e.materialId,createTime:"",status:0,remark:"",expectedInbound:e.expectedQuantity||0,receivedQuantity:e.receivedQuantity||0,name:e.material.name,model:e.material.model,size:e.material.size,material:e.material.material,unit:e.material.unit,process:e.material.process,code:e.material.code,coverColor:e.material.coverColor||"",orderId:e.purchaseId}))}async function se(t){if(!(!t||!I.value)){y.value=!0;try{const e=await C.pms.purchase.contract.request({url:"/getContractListByPoAndSupplierId",method:"GET",params:{po:t,supplier_id:I.value}});e&&Array.isArray(e)?(f.value=e,p.value=e.map(a=>({id:a.materialId,code:a.material.code,name:a.material.name,model:a.material.model,size:a.material.size,material:a.material.material,unit:a.material.unit,process:a.material.process,coverColor:a.material.coverColor||"",contractId:a.id,expectedQuantity:a.expectedQuantity,receivedQuantity:a.receivedQuantity,purchaseId:a.purchaseId}))):(f.value=[],p.value=[])}catch{f.value=[],p.value=[]}finally{y.value=!1}}}async function ne(t){if(!(!t||!I.value)){y.value=!0;try{const e=await C.pms.purchase.contract.request({url:"/getContractListByPoAndSupplierId",method:"GET",params:{po:t,supplier_id:I.value}});let a=[];e&&Array.isArray(e)?(f.value=e,a=e.map(i=>({id:i.materialId,code:i.material.code,name:i.material.name,model:i.material.model,size:i.material.size,material:i.material.material,unit:i.material.unit,process:i.material.process,coverColor:i.material.coverColor||"",contractId:i.id,expectedQuantity:i.expectedQuantity,receivedQuantity:i.receivedQuantity,purchaseId:i.purchaseId}))):f.value=[],p.value=a,m.value=[];for(let i=0;i<10;i++)T();a.length>0?u.success(`已切换到新PO，加载了${a.length}个可选物料，创建了10行空白行`):u.warning("新PO暂无物料，已创建10行空白行")}catch{f.value=[],p.value=[],m.value=[];for(let a=0;a<10;a++)T();u.error("获取新PO物料失败，已创建10行空白行")}finally{y.value=!1}}}return(t,e)=>{const a=v("el-option"),i=v("el-select"),q=v("el-form-item"),P=v("el-col"),n=v("el-input"),d=v("el-row"),x=v("el-button"),de=v("el-empty"),_=v("el-table-column"),ue=v("el-input-number"),E=v("el-tooltip"),ce=v("el-table"),R=v("el-icon"),pe=v("el-form"),me=v("el-dialog");return b(),A("div",xe,[c("div",Qe,[c("h2",null,z(k.value?"编辑送货单":"添加送货单"),1)]),l(pe,{"label-width":"120px",class:"delivery-form"},{default:r(()=>[c("div",Ve,[ze,l(d,{gutter:20},{default:r(()=>[l(P,{span:12},{default:r(()=>[l(q,{label:"送货单PO号",required:""},{default:r(()=>[l(i,{modelValue:s.po,"onUpdate:modelValue":e[0]||(e[0]=o=>s.po=o),filterable:"",placeholder:"请选择送货单PO号",style:{width:"100%"},loading:N.value,clearable:"",onChange:ie},{default:r(()=>[(b(!0),A(j,null,J(M.value,o=>(b(),L(a,{key:o.po,label:o.po,value:o.po},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"]),M.value.length===0?(b(),A("div",we," PO列表为空，请等待加载或刷新页面 ")):(b(),A("div",qe," 共加载了 "+z(M.value.length)+" 个PO号 ",1))]),_:1})]),_:1}),l(P,{span:12},{default:r(()=>[l(q,{label:"送货单号"},{default:r(()=>[l(n,{modelValue:s.supplierOrderNo,"onUpdate:modelValue":e[1]||(e[1]=o=>s.supplierOrderNo=o),placeholder:"送货单号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(d,{gutter:20},{default:r(()=>[l(P,{span:12},{default:r(()=>[l(q,{label:"送货单备注"},{default:r(()=>[l(n,{modelValue:s.remark,"onUpdate:modelValue":e[2]||(e[2]=o=>s.remark=o),type:"textarea",placeholder:"请输入送货单备注",rows:2},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),c("div",Ae,[c("div",Me,[Pe,s.po&&!k.value?(b(),L(x,{key:0,type:"primary",loading:y.value,onClick:e[3]||(e[3]=()=>B(s.po))},{default:r(()=>[Q(" 刷新物料列表 ")]),_:1},8,["loading"])):$("",!0),s.po&&k.value?(b(),L(x,{key:1,type:"warning",loading:y.value,onClick:e[4]||(e[4]=()=>B(s.po))},{default:r(()=>[Q(" 重新加载物料（将覆盖当前数据） ")]),_:1},8,["loading"])):$("",!0)]),m.value.length===0?(b(),A("div",Se,[l(de,{description:k.value?"编辑模式：物料信息加载中...":"请先选择PO号以加载物料信息","image-size":100},null,8,["description"])])):(b(),L(ce,{key:1,data:m.value,border:"",size:"small",class:"compact-table"},{default:r(()=>[l(_,{label:"序号",type:"index",width:"70",align:"center"}),l(_,{label:"物料编号",prop:"materialId","min-width":"150"},{default:r(({row:o})=>[l(i,{modelValue:o.materialId,"onUpdate:modelValue":h=>o.materialId=h,filterable:"",placeholder:"请选择物料",size:"small",style:{width:"100%"},onChange:h=>ae(h,o)},{default:r(()=>[(b(!0),A(j,null,J(p.value,h=>(b(),L(a,{key:h.id,label:`${h.code}/${h.name}`,value:h.id,disabled:ee(h.id,o)},null,8,["label","value","disabled"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(_,{label:"数量",prop:"quantity","min-width":"120"},{header:r(()=>[Oe]),default:r(({row:o})=>[l(ue,{modelValue:o.quantity,"onUpdate:modelValue":h=>o.quantity=h,min:0,max:Math.max(o.expectedInbound,0),precision:2,style:{width:"100%"},placeholder:"请输入数量"},null,8,["modelValue","onUpdate:modelValue","max"])]),_:1}),l(_,{label:"备注",prop:"remark","min-width":"200"},{default:r(({row:o})=>[l(n,{modelValue:o.remark,"onUpdate:modelValue":h=>o.remark=h,placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(_,{label:"合同ID",prop:"contractId","min-width":"50"}),l(_,{label:"剩余入库数量",prop:"expectedInbound","min-width":"70"}),l(_,{label:"已收数量",prop:"receivedQuantity","min-width":"60"}),l(_,{label:"物料名称",prop:"name","min-width":"60"},{default:r(({row:o})=>[l(E,{content:o.name,placement:"top","show-after":500,enterable:!1},{default:r(()=>[c("div",Ee,z(o.name),1)]),_:2},1032,["content"])]),_:1}),l(_,{label:"物料型号",prop:"model","min-width":"60"},{default:r(({row:o})=>[l(E,{content:o.model,placement:"top","show-after":500,enterable:!1},{default:r(()=>[c("div",Le,z(o.model),1)]),_:2},1032,["content"])]),_:1}),l(_,{label:"物料尺寸",prop:"size","min-width":"80"},{default:r(({row:o})=>[l(E,{content:o.size,placement:"top","show-after":500,enterable:!1},{default:r(()=>[c("div",Ue,z(o.size),1)]),_:2},1032,["content"])]),_:1}),l(_,{label:"物料材质",prop:"material","min-width":"80"},{default:r(({row:o})=>[l(E,{content:o.material,placement:"top","show-after":500,enterable:!1},{default:r(()=>[c("div",Ne,z(o.material),1)]),_:2},1032,["content"])]),_:1}),l(_,{label:"物料单位",prop:"unit","min-width":"50"}),l(_,{label:"物料工艺",prop:"process","min-width":"80"},{default:r(({row:o})=>[l(E,{content:o.process,placement:"top","show-after":500,enterable:!1},{default:r(()=>[c("div",Te,z(o.process),1)]),_:2},1032,["content"])]),_:1}),l(_,{label:"操作",width:"100",align:"center"},{default:r(({$index:o})=>[l(x,{type:"danger",text:"",onClick:h=>Y(o)},{default:r(()=>[Q(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),m.value.length>0?(b(),A("div",Be,[l(x,{type:"primary",class:"add-material-button",onClick:T},{default:r(()=>[l(R,null,{default:r(()=>[l(F(he))]),_:1}),Q(" 添加物料 ")]),_:1})])):$("",!0)]),c("div",$e,[l(x,{onClick:e[5]||(e[5]=o=>F(w).go(-1))},{default:r(()=>[Q(" 取消 ")]),_:1}),l(x,{type:"primary",onClick:oe},{default:r(()=>[Q(" 保存 ")]),_:1})])]),_:1}),l(me,{modelValue:S.value,"onUpdate:modelValue":e[8]||(e[8]=o=>S.value=o),title:"提示",width:"400px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!1},{default:r(()=>[c("div",Fe,[c("div",De,[l(R,{size:"24",color:"#E6A23C"},{default:r(()=>[l(F(ye))]),_:1})]),c("div",Ge,z(k.value?"是否需要自动填充该订单的全部物料（将覆盖当前物料列表）":"是否需要自动填充该订单的全部物料"),1),c("div",Re,[l(x,{onClick:te},{default:r(()=>[Q(" 取消 ")]),_:1}),l(x,{onClick:e[6]||(e[6]=o=>G("manual"))},{default:r(()=>[Q(" 手动输入 ")]),_:1}),l(x,{type:"primary",onClick:e[7]||(e[7]=o=>G("auto"))},{default:r(()=>[Q(" 自动填充 ")]),_:1})])])]),_:1},8,["modelValue"])])}}}),Xe=ke(Je,[["__scopeId","data-v-c149bbba"]]);export{Xe as default};
