package service

import (
	"context"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"strings"

	"github.com/gogf/gf/v2/text/gstr"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/yc"

	"github.com/gogf/gf/v2/os/gtime"
	"github.com/xuri/excelize/v2"
)

type PmsFinancePaymentService struct {
	*yc.Service
}

// SettleAccountsSummaryExport 批量导出财务付款明细
func (s *PmsFinancePaymentService) SettleAccountsSummaryExport(ctx context.Context, reqVo *model.QueryVo) (fileName string, fileBytes []byte, err error) {
	// 获取当前时间
	currentTime := gtime.Now()
	currentYear := currentTime.Year()
	currentMonth := int(currentTime.Month())

	// 从请求的日期范围中获取年份
	targetYear := currentYear // 默认使用当前年份
	if reqVo.Date != "" {
		dateParts := strings.Split(reqVo.Date, ",")
		if len(dateParts) > 0 && dateParts[0] != "" {
			if dateObj, parseErr := gtime.StrToTime(dateParts[0]); parseErr == nil {
				targetYear = dateObj.Year()
			}
		}
	}

	// 创建Excel文件
	file := excelize.NewFile()
	defer func() {
		if file != nil {
			_ = file.Close()
		}
	}()

	// 设置Sheet名称
	sheetName := "绿烟月结供应商合同总金额明细表"
	_ = file.SetSheetName("Sheet1", sheetName)

	// 设置文件属性，解决中文乱码问题
	err = file.SetDocProps(&excelize.DocProperties{
		Title:          "绿烟月结供应商合同总金额明细表",
		Creator:        "系统",
		Description:    "供应商月度合同总金额明细数据",
		Keywords:       "财务,供应商,月度,合同总金额",
		Category:       "财务报表",
		LastModifiedBy: "系统",
		Language:       "zh-CN",
	})
	if err != nil {
		return "", nil, fmt.Errorf("设置文档属性失败: %w", err)
	}

	// 设置默认字体为支持中文的字体
	_, err = file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "宋体",
		},
	})
	if err != nil {
		return "", nil, fmt.Errorf("设置默认字体失败: %w", err)
	}

	// 创建通用样式
	// 标题样式
	titleStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   16,
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})

	// 表头样式
	headerStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   11,
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#DCE6F1"},
			Shading: 0,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})

	// 内容样式
	contentStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})

	// 数字样式（保留两位小数）
	numberStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "right",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		NumFmt: 2, // 设置数字格式为保留两位小数
	})

	// 设置标题行
	_ = file.MergeCell(sheetName, "A1", "O1")
	_ = file.SetCellValue(sheetName, "A1", fmt.Sprintf("%d年绿烟月结供应商 合同总金额 明细表", targetYear))
	_ = file.SetCellStyle(sheetName, "A1", "O1", titleStyle)
	_ = file.SetRowHeight(sheetName, 1, 30)

	// 设置表头
	_ = file.SetCellValue(sheetName, "A2", "序号")
	_ = file.SetCellValue(sheetName, "B2", "供应商产品类别")
	_ = file.SetCellValue(sheetName, "C2", "供应商名称")
	_ = file.SetCellValue(sheetName, "D2", "合同总金额人民币（元）")
	_ = file.MergeCell(sheetName, "D2", "O2")
	_ = file.SetCellStyle(sheetName, "A2", "O2", headerStyle)

	// 设置月份表头 - 只显示1月到当前月
	for i := 1; i <= currentMonth; i++ {
		col := string(rune('C' + i))
		_ = file.SetCellValue(sheetName, col+"3", fmt.Sprintf("%d月合同总金额", i))
	}
	_ = file.SetCellValue(sheetName, "A3", "")
	_ = file.SetCellValue(sheetName, "B3", "")
	_ = file.SetCellValue(sheetName, "C3", "")
	_ = file.SetCellStyle(sheetName, "A3", "O3", headerStyle)

	//
	// 设置列宽
	_ = file.SetColWidth(sheetName, "A", "A", 6)  // 序号
	_ = file.SetColWidth(sheetName, "B", "B", 18) // 供应商产品类别
	_ = file.SetColWidth(sheetName, "C", "C", 30) // 供应商名称
	// 设置月份列宽 - 只设置1月到当前月
	for i := 1; i <= currentMonth; i++ {
		col := string(rune('C' + i))
		_ = file.SetColWidth(sheetName, col, col, 12) // 月份数据列宽
	}

	// 查询供应商列表
	supplierList := make([]*model.PmsSupplier, 0)

	// 使用原始查询条件，保留筛选条件
	supplierQuery := yc.DBM(model.NewPmsSupplier()).Ctx(ctx)
	if reqVo.KeyWord != "" {
		supplierQuery = supplierQuery.WhereLike("name", "%"+reqVo.KeyWord+"%")
	}

	err = supplierQuery.Scan(&supplierList)
	if err != nil {
		return "", nil, fmt.Errorf("查询供应商列表失败: %w", err)
	}

	// 查询每个供应商每月的合同金额
	currentRow := 4
	for i, supplier := range supplierList {
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), i+1)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", currentRow), supplier.MainProduct)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", currentRow), supplier.Name)

		// 为每个月份查询合同金额
		for month := 1; month <= currentMonth; month++ {
			// 计算查询的月份开始和结束日期
			startDate := fmt.Sprintf("%d-%02d-01 00:00:00", targetYear, month)

			// 获取月份最后一天
			var lastDay int
			switch month {
			case 2:
				// 处理闰年
				if targetYear%4 == 0 && (targetYear%100 != 0 || targetYear%400 == 0) {
					lastDay = 29
				} else {
					lastDay = 28
				}
			case 4, 6, 9, 11:
				lastDay = 30
			default:
				lastDay = 31
			}

			endDate := fmt.Sprintf("%d-%02d-%02d 23:59:59", targetYear, month, lastDay)

			// 创建查询参数
			monthQueryVo := &model.QueryVo{
				SupplierId: supplier.ID,
				Date:       fmt.Sprintf("%s,%s", startDate, endDate),
				Page:       1,
				Size:       999,
			}

			// 查询该月供应商财务数据
			monthData, err := s.FinancePaymentPage(ctx, monthQueryVo)
			if err != nil {
				continue
			}

			// 计算该月合同总金额
			var monthlyAmount float64
			for _, financeData := range monthData.List {
				monthlyAmount += financeData.ContractTotalAmount
			}

			// 设置单元格值
			col := string(rune('C' + month))
			_ = file.SetCellValue(sheetName, fmt.Sprintf("%s%d", col, currentRow), monthlyAmount)
			_ = file.SetCellStyle(sheetName, fmt.Sprintf("%s%d", col, currentRow), fmt.Sprintf("%s%d", col, currentRow), numberStyle)
		}

		// 设置行样式
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("C%d", currentRow), contentStyle)

		currentRow++
	}

	// 生成文件名
	fileName = fmt.Sprintf("%d年绿烟月结供应商合同总金额_%s.xlsx", targetYear, gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)

	// 生成文件内容
	buffer, err := file.WriteToBuffer()
	if err != nil {
		return "", nil, fmt.Errorf("生成Excel文件失败: %w", err)
	}

	return fileName, buffer.Bytes(), nil
}

// Export 供应商财务数据导出
func (s *PmsFinancePaymentService) Export(ctx context.Context, req *model.FinancePaymentVo) (fileName string, bytes []byte, err error) {
	// 检查数据是否存在
	if req == nil {
		return "", nil, errors.New("没有数据")
	}

	// 创建Excel文件
	file := excelize.NewFile()
	defer func() {
		if file != nil {
			_ = file.Close()
		}
	}()

	// 设置Sheet名称
	sheetName := "供应商财务数据"
	_ = file.SetSheetName("Sheet1", sheetName)

	// 设置文件属性，解决中文乱码问题
	err = file.SetDocProps(&excelize.DocProperties{
		Title:          "供应商财务数据",
		Creator:        "系统",
		Description:    "供应商财务明细数据",
		Keywords:       "财务,供应商,导出",
		Category:       "财务报表",
		LastModifiedBy: "系统",
		Language:       "zh-CN",
	})
	if err != nil {
		return "", nil, fmt.Errorf("设置文档属性失败: %w", err)
	}

	// 设置默认字体为支持中文的字体
	_, err = file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "宋体",
		},
	})
	if err != nil {
		return "", nil, fmt.Errorf("设置默认字体失败: %w", err)
	}

	// 创建通用样式
	// 标题样式
	titleStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   16,
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#D9D9D9"},
			Shading: 0,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})

	// 副标题样式
	subTitleStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   12,
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F2F2F2"},
			Shading: 0,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})

	// 表头样式
	headerStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   11,
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#E9EAED"},
			Shading: 0,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})

	// 内容样式
	contentStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})

	// 数字样式（保留两位小数）
	numberStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "right",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		NumFmt: 2, // 设置数字格式为保留两位小数
	})

	// 合计样式（加粗）
	totalStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   11,
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "right",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F2F2F2"},
			Shading: 0,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		NumFmt: 2, // 设置数字格式为保留两位小数
	})

	// 红色文本样式（用于突出显示金额）
	redTotalStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   11,
			Family: "微软雅黑",    // 使用微软雅黑以确保中文正确显示
			Color:  "#FF0000", // 红色
		},
		Alignment: &excelize.Alignment{
			Horizontal: "right",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F2F2F2"},
			Shading: 0,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		NumFmt: 2, // 设置数字格式为保留两位小数
	})

	// 导出供应商数据到工作表
	err = s.exportSupplierToSheet(ctx, file, sheetName, req,
		titleStyle, subTitleStyle, headerStyle, contentStyle, numberStyle, totalStyle, redTotalStyle)
	if err != nil {
		return "", nil, fmt.Errorf("导出数据失败: %w", err)
	}

	// 生成文件名 - 使用中文文件名并进行URL编码
	fileName = fmt.Sprintf("供应商财务数据_%s.xlsx", gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)

	// 生成文件内容
	buffer, err := file.WriteToBuffer()
	if err != nil {
		return "", nil, err
	}

	// 返回文件名和内容
	return fileName, buffer.Bytes(), nil
}

// 查询PO是否完成
func getPoFinish(ctx context.Context, po string, end string) ([]*model.Contract, bool, error) {
	contract := make([]*model.Contract, 0)
	err := yc.DBM(model.NewPmsPurchaseContract()).Ctx(ctx).
		WithAll().
		Where("po", po).
		Scan(&contract)
	if err != nil {
		return nil, false, err
	}
	for _, c := range contract {
		// PO单没有在结束时间段之后的入库单(不包含补退货)
		count, err := yc.DBM(model.NewPmsMaterialInboundProduct()).As("a").Ctx(ctx).
			LeftJoin("pms_material_inbound as b", "b.id = a.inbound_id").
			WhereNot("b.Type", model.MaterialInboundTypeReturnPolicy).
			Where("a.contract_id", c.ID).
			Where("b.createTime > ?", end).Count()
		if err != nil {
			return nil, false, err
		}
		if count > 0 {
			return nil, false, nil
		}
	}
	return contract, true, nil
}

// BatchExport 批量导出财务付款
func (s *PmsFinancePaymentService) BatchExport(ctx context.Context, reqVo *model.QueryVo) (fileName string, fileBytes []byte, err error) {
	// 获取财务付款数据
	result, err := s.FinancePaymentPage(ctx, reqVo)
	if err != nil {
		return "", nil, fmt.Errorf("获取财务付款数据失败: %w", err)
	}

	if len(result.List) == 0 {
		return "", nil, fmt.Errorf("无数据可导出")
	}

	// 创建Excel文件
	file := excelize.NewFile()
	defer func() {
		if file != nil {
			_ = file.Close()
		}
	}()

	// 删除默认的Sheet1
	_ = file.DeleteSheet("Sheet1")

	// 设置文件属性，解决中文乱码问题
	err = file.SetDocProps(&excelize.DocProperties{
		Title:          "财务付款批量导出",
		Creator:        "系统",
		Description:    "财务付款数据",
		Keywords:       "财务,付款,导出",
		Category:       "财务报表",
		LastModifiedBy: "系统",
		Language:       "zh-CN",
	})
	if err != nil {
		return "", nil, fmt.Errorf("设置文档属性失败: %w", err)
	}

	// 设置默认字体为支持中文的字体
	_, err = file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "宋体",
		},
	})
	if err != nil {
		return "", nil, fmt.Errorf("设置默认字体失败: %w", err)
	}

	// 创建通用样式
	// 标题样式
	titleStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   16,
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#D9D9D9"},
			Shading: 0,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})

	// 副标题样式
	subTitleStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   12,
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F2F2F2"},
			Shading: 0,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})

	// 表头样式
	headerStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   11,
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#E9EAED"},
			Shading: 0,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})

	// 内容样式
	contentStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})

	// 数字样式（保留两位小数）
	numberStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "right",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		NumFmt: 2, // 设置数字格式为保留两位小数
	})

	// 合计样式（加粗）
	totalStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   11,
			Family: "微软雅黑", // 使用微软雅黑以确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "right",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F2F2F2"},
			Shading: 0,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		NumFmt: 2, // 设置数字格式为保留两位小数
	})

	// 红色文本样式（用于突出显示金额）
	redTotalStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   11,
			Family: "微软雅黑",    // 使用微软雅黑以确保中文正确显示
			Color:  "#FF0000", // 红色
		},
		Alignment: &excelize.Alignment{
			Horizontal: "right",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Pattern: 1,
			Color:   []string{"#F2F2F2"},
			Shading: 0,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		NumFmt: 2, // 设置数字格式为保留两位小数
	})

	// 为每个供应商创建一个工作表并导出数据
	for _, supplier := range result.List {
		// 使用供应商名称作为工作表名称（确保工作表名称合法）
		// 防止中文乱码: 确保工作表名称正确编码
		sheetName := strings.TrimSpace(supplier.Name)
		if sheetName == "" {
			sheetName = "未命名供应商"
		}
		// 限制工作表名称长度为31个字符（Excel限制）
		if len([]rune(sheetName)) > 31 {
			sheetName = string([]rune(sheetName)[:31])
		}
		// 替换不允许的字符
		sheetName = strings.ReplaceAll(sheetName, ":", "")
		sheetName = strings.ReplaceAll(sheetName, "/", "")
		sheetName = strings.ReplaceAll(sheetName, "\\", "")
		sheetName = strings.ReplaceAll(sheetName, "?", "")
		sheetName = strings.ReplaceAll(sheetName, "*", "")
		sheetName = strings.ReplaceAll(sheetName, "[", "")
		sheetName = strings.ReplaceAll(sheetName, "]", "")

		// 如果工作表名称已存在，添加序号后缀
		baseName := sheetName
		for i := 1; ; i++ {
			exists := false
			for _, name := range file.GetSheetList() {
				if name == sheetName {
					exists = true
					break
				}
			}
			if !exists {
				break
			}
			suffix := fmt.Sprintf("(%d)", i)
			// 确保添加后缀后的长度不超过31个字符
			if len([]rune(baseName))+len([]rune(suffix)) > 31 {
				// 如果添加序号后超过31个字符，截断原名称
				remainingLength := 31 - len([]rune(suffix))
				sheetName = string([]rune(baseName)[:remainingLength]) + suffix
			} else {
				sheetName = baseName + suffix
			}
		}

		// 创建工作表
		index, err := file.NewSheet(sheetName)
		if err != nil {
			continue // 跳过此供应商
		}

		// 设置为活动工作表
		file.SetActiveSheet(index)

		// 导出供应商数据到工作表
		err = s.exportSupplierToSheet(ctx, file, sheetName, supplier,
			titleStyle, subTitleStyle, headerStyle, contentStyle, numberStyle, totalStyle, redTotalStyle)
		if err != nil {
			continue // 导出失败跳过此供应商
		}
	}

	// 生成文件内容
	buffer, err := file.WriteToBuffer()
	if err != nil {
		return "", nil, fmt.Errorf("生成Excel文件失败: %w", err)
	}

	// 生成文件名（使用中文文件名并进行URL编码）
	fileName = fmt.Sprintf("财务付款批量导出_%s.xlsx", gtime.Now().Format("YmdHis"))
	fileName = url.QueryEscape(fileName)

	return fileName, buffer.Bytes(), nil
}

// FinancePaymentPage 财务付款列表
func (s *PmsFinancePaymentService) FinancePaymentPage(ctx context.Context, reqVo *model.QueryVo) (result model.PageResult[*model.FinancePaymentVo], err error) {
	result.List = make([]*model.FinancePaymentVo, 0)
	result.Pagination = &model.Pagination{Page: reqVo.Page, Size: reqVo.Size}
	reqVo.KeyWord = gstr.Trim(reqVo.KeyWord)
	result.Pagination.Page = reqVo.Page
	result.Pagination.Size = reqVo.Size

	financePaymentData := make([]*model.FinancePaymentVo, 0)
	m := yc.DBM(model.NewPmsSupplier()).As("ps").Ctx(ctx)

	if reqVo.SupplierId > 0 {
		m.Where("id = ?", reqVo.SupplierId)
	}

	count, err := m.Clone().Count()
	if err != nil {
		return result, err
	}
	result.Pagination.Total = int64(count)

	// 获取基础数据
	err = m.Order("id ASC").Limit(reqVo.Size).Offset((reqVo.Page - 1) * reqVo.Size).
		Scan(&financePaymentData)
	if err != nil {
		return result, err
	}

	// 获取供应商ID
	supplierIds := make([]int64, 0)
	for _, item := range financePaymentData {
		supplierIds = append(supplierIds, item.ID)
	}

	// 获取物料
	materialList := make([]*model.PmsMaterial, 0)
	materialIds := make([]int64, 0)
	if reqVo.KeyWord != "" {
		err := yc.DBM(model.NewPmsMaterial()).Ctx(ctx).Where("code LIKE ?", fmt.Sprintf("%%%s%%", reqVo.KeyWord)).Scan(&materialList)
		if err != nil {
			return result, err
		}
		for _, item := range materialList {
			materialIds = append(materialIds, item.ID)
		}
	}
	split := gstr.Split(reqVo.Date, ",")
	// 获取供应商合同
	m = yc.DBM(model.NewPmsPurchaseContract()).As("ppc").Ctx(ctx)
	if reqVo.KeyWord != "" && len(materialList) > 0 {
		m.WhereIn("material_id", materialIds)
	} else if reqVo.KeyWord != "" && len(materialList) == 0 {
		result.List = financePaymentData
		return result, nil
	}
	// 查询已经完成的订单
	//m.WhereIn("supplier_id", supplierIds).
	//	Where("quantity = received_quantity")

	m.WhereIn("supplier_id", supplierIds)
	// 查询在搜索时间段有入库记录的数据
	m.Where("id IN ?", yc.DBM(model.NewPmsMaterialInboundProduct()).As("a").Ctx(ctx).
		LeftJoin("pms_material_inbound as b", "b.id = a.inbound_id").
		WhereNot("b.type", model.MaterialInboundTypeReturnPolicy). //补退货不影响结算时间
		WhereBetween("b.complete_time", split[0], split[1]).Fields("a.contract_id as contract_id"))
	// 查询在搜索时间段之后没有入库记录，即最后的入库记录为当前搜索日期
	//m.Where("id NOT IN ?", yc.DBM(model.NewPmsMaterialInboundProduct()).As("a").Ctx(ctx).
	//	LeftJoin("pms_material_inbound as b", "b.id = a.inbound_id").
	//	Where("b.createTime > ?", split[1]).Fields("a.contract_id as contract_id"))

	contracts := make([]*model.Contract, 0)
	err = m.Scan(&contracts)
	if err != nil {
		return result, err
	}

	contracts1 := make([]*model.Contract, 0)
	finishStatus := make(map[string]bool)
	for _, item := range contracts {
		var pocontracts []*model.Contract
		var flag bool
		if v, ok := finishStatus[item.Po]; !ok {
			pocontracts, flag, err = getPoFinish(ctx, item.Po, split[1])
			if err != nil {
				return result, err
			}
			finishStatus[item.Po] = flag
			if !flag {
				continue
			}
		} else if !v {
			continue
		}
		contracts1 = append(contracts1, pocontracts...)
	}

	// 供应商合同ID
	contractIds := make([]int64, 0)
	// 供应商合同
	mSupplierContract := make(map[int64][]*model.Contract)
	for _, item := range contracts1 {
		contractIds = append(contractIds, item.ID)
	}

	materialInboundProductList := make([]*model.PmsMaterialInboundProduct, 0)

	materialOutboundProductList := make([]*model.PmsMaterialOutboundProduct, 0)

	// 物料入库列表（不包含补退货）  2025/07/04 修改为包含补退货数据
	err = yc.DBM(model.NewPmsMaterialInboundProduct()).
		As("pmp").
		LeftJoin("pms_material_inbound pmi", "pmi.id = pmp.inbound_id").
		WhereIn("pmp.contract_id", contractIds).
		Where("DATE_FORMAT(pmi.complete_time,'%Y-%m-%d') BETWEEN ? AND ?", split[0], split[1]).
		//WhereNot("pmi.type", model.MaterialInboundTypeReturnPolicy).
		Fields("pmp.*").
		Scan(&materialInboundProductList)
	if err != nil {
		return result, err
	}

	materialInboundReturnPolicyList := make([]*model.PmsMaterialReturnPolicyProduct, 0)
	// 补退货列表
	m = yc.DBM(model.NewPmsMaterialInboundProduct()).
		As("pmp").
		LeftJoin("pms_material_inbound pmi", "pmi.id = pmp.inbound_id").
		LeftJoin("pms_purchase_contract ppc", "ppc.id = pmp.contract_id")
	// 查询所有的补退货
	//WhereIn("pmp.contract_id", contractIds)
	if reqVo.KeyWord != "" {
		m.LeftJoin("pms_material pm", "pm.id = pmp.material_id").
			Where("pm.code LIKE ?", fmt.Sprintf("%%%s%%", reqVo.KeyWord))
	}
	err = m.Where("DATE_FORMAT(pmi.createTime,'%Y-%m-%d') BETWEEN ? AND ?", split[0], split[1]).
		Where("pmi.type", model.MaterialInboundTypeReturnPolicy).
		Fields("pmp.*,ppc.supplier_id,ppc.unit_price,ppc.po as po").
		Scan(&materialInboundReturnPolicyList)
	if err != nil {
		return result, err
	}

	// 退补货MAP结构
	mSupplierMaterialInboundReturnPolicy := make(map[int64][]*model.PmsMaterialReturnPolicyProduct)
	for _, item := range materialInboundReturnPolicyList {
		if _, ok := mSupplierMaterialInboundReturnPolicy[item.SupplierId]; !ok {
			mSupplierMaterialInboundReturnPolicy[item.SupplierId] = make([]*model.PmsMaterialReturnPolicyProduct, 0)
		}
		mSupplierMaterialInboundReturnPolicy[item.SupplierId] = append(mSupplierMaterialInboundReturnPolicy[item.SupplierId], item)
	}

	// 物料出库列表
	err = yc.DBM(model.NewPmsMaterialOutboundProduct()).
		As("po").
		LeftJoin("pms_material_outbound pmo", "pmo.id = po.outbound_id").
		WhereIn("po.contract_id", contractIds).
		Where("DATE_FORMAT(pmo.complete_time,'%Y-%m-%d') BETWEEN ? AND ?", split[0], split[1]).
		WhereIn("po.contract_id", contractIds).
		Fields("po.*").
		Scan(&materialOutboundProductList)
	if err != nil {
		return result, err
	}

	mMaterialinboundproduct := make(map[int64][]*model.PmsMaterialInboundProduct)
	mMaterialoutboundproduct := make(map[int64][]*model.PmsMaterialOutboundProduct)
	for _, item := range materialInboundProductList {
		mMaterialinboundproduct[item.ContractId] = append(mMaterialinboundproduct[item.ContractId], item)
	}
	for _, item := range materialOutboundProductList {
		mMaterialoutboundproduct[item.ContractId] = append(mMaterialoutboundproduct[item.ContractId], item)
	}

	// 装载数据
	for _, item := range contracts1 {
		if v, ok := mMaterialinboundproduct[item.ID]; ok {
			if item.MaterialInboundProductList == nil {
				item.MaterialInboundProductList = make([]*model.PmsMaterialInboundProduct, 0)
			}
			item.MaterialInboundProductList = append(item.MaterialInboundProductList, v...)
		}

		if v, ok := mMaterialoutboundproduct[item.ID]; ok {
			if item.MaterialOutboundProductList == nil {
				item.MaterialOutboundProductList = make([]*model.PmsMaterialOutboundProduct, 0)
			}
			item.MaterialOutboundProductList = append(item.MaterialOutboundProductList, v...)
		}
	}

	//for _, item := range contracts {
	//	if item.MaterialInboundProductList != nil || item.MaterialOutboundProductList != nil {
	//		contracts1 = append(contracts1, item)
	//	}
	//}

	//生成MAP
	for _, item := range contracts1 {
		if _, ok := mSupplierContract[item.SupplierId]; !ok {
			mSupplierContract[item.SupplierId] = make([]*model.Contract, 0)
		}
		mSupplierContract[item.SupplierId] = append(mSupplierContract[item.SupplierId], item)
	}

	// 计算数据
	for _, item := range contracts1 {
		// 计算当前入库数量
		curInboundQuantity := 0
		// 计算当前出库数量
		curOutboundQuantity := 0
		if item.MaterialInboundProductList != nil {
			for _, inbound := range item.MaterialInboundProductList {
				curInboundQuantity = curInboundQuantity + int(inbound.Quantity)
			}
			item.CurInboundQuantity = curInboundQuantity
		}
		if item.MaterialOutboundProductList != nil {
			for _, outbound := range item.MaterialOutboundProductList {
				curOutboundQuantity = curOutboundQuantity + int(outbound.Quantity)
			}
			item.CurOutboundQuantity = curOutboundQuantity
		}
		// 计算原始值
		//original := float64(curInboundQuantity-curOutboundQuantity) * item.UnitPrice
		// 四舍五入到一位小数
		//item.CurTotalAmount = math.Round(original*10) / 10
		item.CurTotalAmount = item.Subtotal
	}

	// 查询扣款信息
	manHourDeduct := make([]*model.PmsProductionManHourDeduct, 0)
	//split = gstr.Split(reqVo.Date, ",")

	err = yc.DBM(model.NewPmsProductionManHourDeduct()).Ctx(ctx).
		WhereIn("supplier_id", supplierIds).
		WhereBetween("createTime", split[0], split[1]).
		Scan(&manHourDeduct)
	if err != nil {
		return result, err
	}

	materialDeduct := make([]*model.PmsProductionMaterialDeduct, 0)
	err = yc.DBM(model.NewPmsProductionMaterialDeduct()).Ctx(ctx).
		WhereIn("supplier_id", supplierIds).
		WhereBetween("createTime", split[0], split[1]).
		Scan(&materialDeduct)

	if err != nil {
		return result, err
	}

	// 扣款信息MAP结构
	mSupplierManHourDeduct := make(map[int64][]*model.PmsProductionManHourDeduct)
	mSupplierMaterialDeduct := make(map[int64][]*model.PmsProductionMaterialDeduct)
	for _, item := range manHourDeduct {
		if _, ok := mSupplierManHourDeduct[item.SupplierId]; !ok {
			mSupplierManHourDeduct[item.SupplierId] = make([]*model.PmsProductionManHourDeduct, 0)
		}
		mSupplierManHourDeduct[item.SupplierId] = append(mSupplierManHourDeduct[item.SupplierId], item)
	}
	for _, item := range materialDeduct {
		if _, ok := mSupplierMaterialDeduct[item.SupplierId]; !ok {
			mSupplierMaterialDeduct[item.SupplierId] = make([]*model.PmsProductionMaterialDeduct, 0)
		}
		mSupplierMaterialDeduct[item.SupplierId] = append(mSupplierMaterialDeduct[item.SupplierId], item)
	}

	// 关联合同
	for _, item := range financePaymentData {
		if contracts, ok := mSupplierContract[item.ID]; ok {
			item.Contract = contracts
		}
		// 计算合同总金额
		totalAmount := 0.0

		for _, contract := range item.Contract {
			//if contract.MaterialInboundProductList != nil && len(contract.MaterialInboundProductList) > 0 {
			//	for _, item := range contract.MaterialInboundProductList {
			//		totalAmount += item.Quantity * contract.UnitPrice
			//	}
			//}
			//if contract.MaterialOutboundProductList != nil && len(contract.MaterialOutboundProductList) > 0 {
			//	for _, item := range contract.MaterialOutboundProductList {
			//		totalAmount -= item.Quantity * contract.UnitPrice
			//	}
			//}
			totalAmount += contract.Subtotal
		}
		item.ContractTotalAmount = math.Round(totalAmount*100) / 100

		// 关联退补货
		if returnPolicy, ok := mSupplierMaterialInboundReturnPolicy[item.ID]; ok {
			item.MaterialInboundReturnPolicy = returnPolicy
		}

		// 关联工时扣款
		if manHourDeduct, ok := mSupplierManHourDeduct[item.ID]; ok {
			item.ManHourDeduct = manHourDeduct
		}

		// 关联物料扣款
		if materialDeduct, ok := mSupplierMaterialDeduct[item.ID]; ok {
			item.MaterialDeduct = materialDeduct
		}
		totalAmount = 0.0
		for _, manHour := range item.ManHourDeduct {
			totalAmount += manHour.TotalAmount
		}
		item.ManHourDeductAmount = math.Round(totalAmount*100) / 100

		totalAmount = 0.0
		for _, material := range item.MaterialDeduct {
			totalAmount += material.TotalAmount
		}
		item.MaterialDeductAmount = math.Round(totalAmount*100) / 100
	}

	result.List = financePaymentData
	return result, nil
}

func getFileExtension(url string) string {
	parts := strings.Split(url, ".")
	if len(parts) > 1 {
		ext := strings.ToLower(parts[len(parts)-1])
		// 处理查询参数
		ext = strings.Split(ext, "?")[0]
		switch ext {
		case "jpg", "jpeg":
			return ".jpg"
		case "png":
			return ".png"
		case "gif":
			return ".gif"
		default:
			return ".jpg" // 默认为jpg
		}
	}
	return ".jpg" // 默认为jpg
}

// exportSupplierToSheet 将单个供应商的数据导出到Excel工作表
func (s *PmsFinancePaymentService) exportSupplierToSheet(ctx context.Context, file *excelize.File, sheetName string, supplier *model.FinancePaymentVo,
	titleStyle, subTitleStyle, headerStyle, contentStyle, numberStyle, totalStyle, redTotalStyle int) error {

	// 设置中文字体样式
	chineseStyle, _ := file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   11,
			Family: "微软雅黑", // 使用微软雅黑确保中文正确显示
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
	})

	// 设置标题
	_ = file.MergeCell(sheetName, "A1", "M1")
	_ = file.SetCellValue(sheetName, "A1", "供应商财务数据")
	_ = file.SetCellStyle(sheetName, "A1", "M1", titleStyle)
	_ = file.SetRowHeight(sheetName, 1, 30)

	// 设置供应商基本信息
	currentRow := 3
	_ = file.MergeCell(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow))
	_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), "基本信息")
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow), subTitleStyle)
	_ = file.SetRowHeight(sheetName, currentRow, 25)

	currentRow++
	_ = file.MergeCell(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("B%d", currentRow))
	_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), "供应商名称:")
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("B%d", currentRow), chineseStyle)

	_ = file.MergeCell(sheetName, fmt.Sprintf("C%d", currentRow), fmt.Sprintf("E%d", currentRow))
	_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", currentRow), supplier.Name)
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("C%d", currentRow), fmt.Sprintf("E%d", currentRow), chineseStyle)

	_ = file.MergeCell(sheetName, fmt.Sprintf("F%d", currentRow), fmt.Sprintf("G%d", currentRow))
	_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", currentRow), "联系人:")
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("F%d", currentRow), fmt.Sprintf("G%d", currentRow), chineseStyle)

	_ = file.MergeCell(sheetName, fmt.Sprintf("H%d", currentRow), fmt.Sprintf("M%d", currentRow))
	_ = file.SetCellValue(sheetName, fmt.Sprintf("H%d", currentRow), supplier.Contact)
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("H%d", currentRow), fmt.Sprintf("M%d", currentRow), chineseStyle)

	_ = file.SetRowHeight(sheetName, currentRow, 22)

	// 移除联系电话字段显示
	// 原代码：
	// _ = file.MergeCell(sheetName, fmt.Sprintf("K%d", currentRow), fmt.Sprintf("L%d", currentRow))
	// _ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", currentRow), "联系电话:")
	// _ = file.MergeCell(sheetName, fmt.Sprintf("M%d", currentRow), fmt.Sprintf("N%d", currentRow))
	// _ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", currentRow), supplier.Phone)

	// 合同明细
	currentRow += 2
	_ = file.MergeCell(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow))
	_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), "合同明细信息")
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow), subTitleStyle)
	_ = file.SetRowHeight(sheetName, currentRow, 25)

	// 合同列表头部
	currentRow += 1
	// 删除本期合同金额，并将合同金额列移到最后
	contractHeaders := []string{"序号", "创建时间", "PO号", "物料编码", "品名", "合同单价", "合同数量", "已收数量", "在途数量", "税率", "本期入库数量", "本期出库数量", "合同金额"}
	for idx, header := range contractHeaders {
		col := string(rune('A' + idx))
		cell := fmt.Sprintf("%s%d", col, currentRow)
		_ = file.SetCellValue(sheetName, cell, header)
		// 使用带有中文支持的样式
		_ = file.SetCellStyle(sheetName, cell, cell, headerStyle)
	}
	_ = file.SetRowHeight(sheetName, currentRow, 22)

	// 设置列宽
	_ = file.SetColWidth(sheetName, "A", "A", 6)  // 序号
	_ = file.SetColWidth(sheetName, "B", "B", 22) // 创建时间
	_ = file.SetColWidth(sheetName, "C", "C", 22) // 订单号
	_ = file.SetColWidth(sheetName, "D", "D", 18) // 物料编码
	_ = file.SetColWidth(sheetName, "E", "E", 20) // 品名
	_ = file.SetColWidth(sheetName, "F", "F", 15) // 合同单价
	_ = file.SetColWidth(sheetName, "G", "G", 15) // 合同数量
	_ = file.SetColWidth(sheetName, "H", "H", 10) // 已收数量
	_ = file.SetColWidth(sheetName, "I", "I", 10) // 在途数量
	_ = file.SetColWidth(sheetName, "J", "J", 8)  // 税率
	_ = file.SetColWidth(sheetName, "K", "K", 15) // 本期入库数量
	_ = file.SetColWidth(sheetName, "L", "L", 15) // 本期出库数量
	_ = file.SetColWidth(sheetName, "M", "M", 15) // 合同金额 - 移到最后

	// 写入合同明细数据
	startContractRow := currentRow + 1
	for i, contract := range supplier.Contract {
		currentRow++
		materialName := ""
		if contract.MaterialOutput != nil {
			materialName = contract.MaterialOutput.Name
		}

		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), i+1)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", currentRow), contract.CreateTime)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", currentRow), contract.Po)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", currentRow), contract.MaterialOutput.Code)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", currentRow), materialName)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", currentRow), contract.UnitPrice)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", currentRow), contract.Quantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("H%d", currentRow), contract.ReceivedQuantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("I%d", currentRow), contract.ExpectedQuantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", currentRow), fmt.Sprintf("%.2f%%", contract.TaxRate))
		_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", currentRow), contract.CurInboundQuantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("L%d", currentRow), contract.CurOutboundQuantity)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", currentRow), contract.Subtotal)

		// 文本类内容使用中文字体样式
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("A%d", currentRow), contentStyle) // 序号
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("B%d", currentRow), fmt.Sprintf("B%d", currentRow), contentStyle) // 创建时间
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("C%d", currentRow), fmt.Sprintf("C%d", currentRow), contentStyle) // 订单号
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("D%d", currentRow), fmt.Sprintf("D%d", currentRow), contentStyle) // 物料编码
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("E%d", currentRow), fmt.Sprintf("E%d", currentRow), contentStyle) // 品名

		// 数字类内容使用数字样式
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("F%d", currentRow), fmt.Sprintf("M%d", currentRow), numberStyle)
	}

	// 合同汇总行
	currentRow++
	_ = file.MergeCell(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("L%d", currentRow))
	_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), "合同金额合计:")
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("L%d", currentRow), totalStyle)

	_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", currentRow), supplier.ContractTotalAmount)
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("M%d", currentRow), fmt.Sprintf("M%d", currentRow), redTotalStyle)

	// 给所有合同数据添加表格样式
	if len(supplier.Contract) > 0 {
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", startContractRow), fmt.Sprintf("M%d", currentRow-1), contentStyle)
	}

	// 工时扣款信息
	if len(supplier.ManHourDeduct) > 0 {
		currentRow += 2
		_ = file.MergeCell(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow))
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), "工时扣款信息")
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow), subTitleStyle)
		_ = file.SetRowHeight(sheetName, currentRow, 25)

		currentRow++
		manHourHeaders := []string{"序号", "事故时间", "扣款单号", "不良物料名称", "人数", "工时合计", "人均工时(元/小时)", "人均工时", "扣款金额", "描述", "凭证"}
		for i, header := range manHourHeaders {
			col := string(rune('A' + i))
			_ = file.SetCellValue(sheetName, fmt.Sprintf("%s%d", col, currentRow), header)
			_ = file.SetCellStyle(sheetName, fmt.Sprintf("%s%d", col, currentRow), fmt.Sprintf("%s%d", col, currentRow), headerStyle)
		}
		_ = file.SetRowHeight(sheetName, currentRow, 22)

		// 设置工时扣款表格的列宽和行高
		_ = file.SetColWidth(sheetName, "A", "A", 6)  // 序号
		_ = file.SetColWidth(sheetName, "B", "B", 22) // 事故时间 - 增加宽度
		_ = file.SetColWidth(sheetName, "C", "C", 22) // 扣款单号 - 增加宽度
		_ = file.SetColWidth(sheetName, "D", "D", 20) // 不良物料名称
		_ = file.SetColWidth(sheetName, "E", "E", 10) // 人数
		_ = file.SetColWidth(sheetName, "F", "F", 12) // 工时合计
		_ = file.SetColWidth(sheetName, "G", "G", 25) // 人均工时(元/小时) - 进一步增加宽度
		_ = file.SetColWidth(sheetName, "H", "H", 12) // 人均工时
		_ = file.SetColWidth(sheetName, "I", "I", 12) // 扣款金额
		_ = file.SetColWidth(sheetName, "J", "J", 20) // 描述
		_ = file.SetColWidth(sheetName, "K", "K", 20) // 凭证列宽增加

		// 写入工时扣款数据
		startManHourRow := currentRow + 1
		for i, manHour := range supplier.ManHourDeduct {
			currentRow++
			// 设置足够的行高来容纳图片
			_ = file.SetRowHeight(sheetName, currentRow, 80)

			_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), i+1) // 序号
			// 事故时间只显示年月日
			accidentTime := ""
			if manHour.AccidentTime != nil {
				// 使用gtime格式化，只保留年月日部分
				accidentTime = manHour.AccidentTime.Format("Y-m-d")
			}
			_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", currentRow), accidentTime)                  // 事故时间（只显示年月日）
			_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", currentRow), manHour.NO)                    // 扣款单号
			_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", currentRow), manHour.BadMaterialName)       // 不良物料名称
			_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", currentRow), manHour.NumberOfPeople)        // 人数
			_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", currentRow), manHour.ManHour)               // 工时合计
			_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", currentRow), manHour.LaborRate)             // 人均工时(元/小时)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("H%d", currentRow), manHour.PerCapitaWorkingHours) // 人均工时
			_ = file.SetCellValue(sheetName, fmt.Sprintf("I%d", currentRow), manHour.TotalAmount)           // 扣款金额
			_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", currentRow), manHour.Description)           // 描述

			// 如果有凭证图片，则添加到Excel中
			if manHour.Voucher != "" {
				// 获取图片URLs（可能有多个，以逗号分隔）
				voucherImgUrls := strings.Split(manHour.Voucher, ",")
				if len(voucherImgUrls) > 0 {
					// 获取第一张图片的URL
					voucherImgUrl := voucherImgUrls[0]

					// 尝试下载图片并插入到Excel
					if voucherImgUrl != "" {
						resp, err := http.Get(voucherImgUrl)
						if err == nil && resp.StatusCode == 200 {
							defer resp.Body.Close()
							imgData, err := io.ReadAll(resp.Body)
							if err == nil {
								// 添加图片到Excel
								err := file.AddPictureFromBytes(sheetName, fmt.Sprintf("K%d", currentRow),
									&excelize.Picture{
										Extension: getFileExtension(voucherImgUrl),
										File:      imgData,
										Format: &excelize.GraphicOptions{
											ScaleX:      0.8, // 适当缩小以确保不溢出
											ScaleY:      0.8,
											Positioning: "oneCell",
											AutoFit:     true,
											OffsetX:     5, // 水平居中偏移
											OffsetY:     5, // 垂直居中偏移
										},
									})
								if err != nil {
									// 如果添加图片失败，则显示链接文本
									_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", currentRow), "查看图片")
									// 添加超链接
									_ = file.SetCellHyperLink(sheetName, fmt.Sprintf("K%d", currentRow), voucherImgUrl, "External")
									// 设置蓝色下划线样式
									hyperlinkStyle, _ := file.NewStyle(&excelize.Style{
										Font: &excelize.Font{
											Color:     "#0563C1",
											Underline: "single",
										},
									})
									_ = file.SetCellStyle(sheetName, fmt.Sprintf("K%d", currentRow), fmt.Sprintf("K%d", currentRow), hyperlinkStyle)
								}
							} else {
								// 如果读取图片内容失败，则显示链接文本
								_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", currentRow), "查看图片")
								// 添加超链接
								_ = file.SetCellHyperLink(sheetName, fmt.Sprintf("K%d", currentRow), voucherImgUrl, "External")
								// 设置蓝色下划线样式
								hyperlinkStyle, _ := file.NewStyle(&excelize.Style{
									Font: &excelize.Font{
										Color:     "#0563C1",
										Underline: "single",
									},
								})
								_ = file.SetCellStyle(sheetName, fmt.Sprintf("K%d", currentRow), fmt.Sprintf("K%d", currentRow), hyperlinkStyle)
							}
						} else {
							// 如果无法访问图片URL，则显示链接文本
							_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", currentRow), "查看图片")
							// 添加超链接
							_ = file.SetCellHyperLink(sheetName, fmt.Sprintf("K%d", currentRow), voucherImgUrl, "External")
							// 设置蓝色下划线样式
							hyperlinkStyle, _ := file.NewStyle(&excelize.Style{
								Font: &excelize.Font{
									Color:     "#0563C1",
									Underline: "single",
								},
							})
							_ = file.SetCellStyle(sheetName, fmt.Sprintf("K%d", currentRow), fmt.Sprintf("K%d", currentRow), hyperlinkStyle)
						}
					} else {
						_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", currentRow), "无凭证")
					}
				} else {
					_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", currentRow), "无凭证")
				}
			} else {
				_ = file.SetCellValue(sheetName, fmt.Sprintf("K%d", currentRow), "无凭证")
			}

			// 设置样式
			_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("D%d", currentRow), contentStyle)
			_ = file.SetCellStyle(sheetName, fmt.Sprintf("E%d", currentRow), fmt.Sprintf("I%d", currentRow), numberStyle)
			_ = file.SetCellStyle(sheetName, fmt.Sprintf("J%d", currentRow), fmt.Sprintf("J%d", currentRow), contentStyle)
		}

		// 工时扣款合计
		currentRow++
		_ = file.MergeCell(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow))
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), "工时扣款合计:")
		_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", currentRow), supplier.ManHourDeductAmount)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow), totalStyle)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("M%d", currentRow), fmt.Sprintf("M%d", currentRow), redTotalStyle)

		// 给所有工时扣款数据添加表格样式
		if len(supplier.ManHourDeduct) > 0 {
			_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", startManHourRow), fmt.Sprintf("M%d", currentRow-1), contentStyle)
		}
	}

	// 物料扣款信息
	if len(supplier.MaterialDeduct) > 0 {
		currentRow += 2
		_ = file.MergeCell(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow))
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), "物料扣款信息")
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow), subTitleStyle)
		_ = file.SetRowHeight(sheetName, currentRow, 25)

		currentRow++
		materialDeductHeaders := []string{"序号", "事故时间", "扣款单号", "不良物料名称", "报废物料名称", "数量", "单价", "扣款金额", "描述", "凭证"}
		for i, header := range materialDeductHeaders {
			col := string(rune('A' + i))
			_ = file.SetCellValue(sheetName, fmt.Sprintf("%s%d", col, currentRow), header)
			_ = file.SetCellStyle(sheetName, fmt.Sprintf("%s%d", col, currentRow), fmt.Sprintf("%s%d", col, currentRow), headerStyle)
		}
		_ = file.SetRowHeight(sheetName, currentRow, 22)

		// 设置物料扣款表格的列宽
		_ = file.SetColWidth(sheetName, "A", "A", 6)  // 序号
		_ = file.SetColWidth(sheetName, "B", "B", 22) // 事故时间 - 增加宽度
		_ = file.SetColWidth(sheetName, "C", "C", 22) // 扣款单号 - 增加宽度
		_ = file.SetColWidth(sheetName, "D", "D", 20) // 不良物料名称
		_ = file.SetColWidth(sheetName, "E", "E", 20) // 报废物料名称
		_ = file.SetColWidth(sheetName, "F", "F", 10) // 数量
		_ = file.SetColWidth(sheetName, "G", "G", 25) // 单价 - 进一步增加宽度
		_ = file.SetColWidth(sheetName, "H", "H", 12) // 扣款金额
		_ = file.SetColWidth(sheetName, "I", "I", 20) // 描述 - 增加宽度
		_ = file.SetColWidth(sheetName, "J", "J", 20) // 凭证 - 保持宽度

		// 写入物料扣款数据
		startMaterialDeductRow := currentRow + 1
		for i, materialDeduct := range supplier.MaterialDeduct {
			currentRow++
			// 设置足够的行高来容纳图片
			_ = file.SetRowHeight(sheetName, currentRow, 80)

			materialInfo := materialDeduct.BadMaterialName
			if materialInfo == "" {
				materialInfo = materialDeduct.MaterialName
			}

			_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), i+1)
			// 事故时间只显示年月日
			accidentTime := ""
			if materialDeduct.AccidentTime != nil {
				// 使用gtime格式化，只保留年月日部分
				accidentTime = materialDeduct.AccidentTime.Format("Y-m-d")
			}
			_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", currentRow), accidentTime) // 事故时间（只显示年月日）
			_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", currentRow), materialDeduct.NO)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", currentRow), materialInfo)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", currentRow), materialDeduct.MaterialName)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", currentRow), materialDeduct.Quantity)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", currentRow), materialDeduct.UnitPrice)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("H%d", currentRow), materialDeduct.TotalAmount)
			_ = file.SetCellValue(sheetName, fmt.Sprintf("I%d", currentRow), materialDeduct.Description)

			// 处理凭证图片，与工时扣款部分类似
			if materialDeduct.Voucher != "" {
				// 获取图片URLs（可能有多个，以逗号分隔）
				voucherImgUrls := strings.Split(materialDeduct.Voucher, ",")
				if len(voucherImgUrls) > 0 {
					// 获取第一张图片的URL
					voucherImgUrl := voucherImgUrls[0]

					// 尝试下载图片并插入到Excel
					if voucherImgUrl != "" {
						resp, err := http.Get(voucherImgUrl)
						if err == nil && resp.StatusCode == 200 {
							defer resp.Body.Close()
							imgData, err := io.ReadAll(resp.Body)
							if err == nil {
								// 添加图片到Excel
								err := file.AddPictureFromBytes(sheetName, fmt.Sprintf("J%d", currentRow),
									&excelize.Picture{
										Extension: getFileExtension(voucherImgUrl),
										File:      imgData,
										Format: &excelize.GraphicOptions{
											ScaleX:      0.8, // 适当缩小以确保不溢出
											ScaleY:      0.8,
											Positioning: "oneCell",
											AutoFit:     true,
											OffsetX:     5, // 水平居中偏移
											OffsetY:     5, // 垂直居中偏移
										},
									})
								if err != nil {
									// 如果添加图片失败，则显示链接文本
									_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", currentRow), "查看图片")
									// 添加超链接
									_ = file.SetCellHyperLink(sheetName, fmt.Sprintf("J%d", currentRow), voucherImgUrl, "External")
									// 设置蓝色下划线样式
									hyperlinkStyle, _ := file.NewStyle(&excelize.Style{
										Font: &excelize.Font{
											Color:     "#0563C1",
											Underline: "single",
										},
									})
									_ = file.SetCellStyle(sheetName, fmt.Sprintf("J%d", currentRow), fmt.Sprintf("J%d", currentRow), hyperlinkStyle)
								}
							} else {
								// 如果读取图片内容失败，则显示链接文本
								_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", currentRow), "查看图片")
								// 添加超链接
								_ = file.SetCellHyperLink(sheetName, fmt.Sprintf("J%d", currentRow), voucherImgUrl, "External")
								// 设置蓝色下划线样式
								hyperlinkStyle, _ := file.NewStyle(&excelize.Style{
									Font: &excelize.Font{
										Color:     "#0563C1",
										Underline: "single",
									},
								})
								_ = file.SetCellStyle(sheetName, fmt.Sprintf("J%d", currentRow), fmt.Sprintf("J%d", currentRow), hyperlinkStyle)
							}
						} else {
							// 如果无法访问图片URL，则显示链接文本
							_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", currentRow), "查看图片")
							// 添加超链接
							_ = file.SetCellHyperLink(sheetName, fmt.Sprintf("J%d", currentRow), voucherImgUrl, "External")
							// 设置蓝色下划线样式
							hyperlinkStyle, _ := file.NewStyle(&excelize.Style{
								Font: &excelize.Font{
									Color:     "#0563C1",
									Underline: "single",
								},
							})
							_ = file.SetCellStyle(sheetName, fmt.Sprintf("J%d", currentRow), fmt.Sprintf("J%d", currentRow), hyperlinkStyle)
						}
					} else {
						_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", currentRow), "无凭证")
					}
				} else {
					_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", currentRow), "无凭证")
				}
			} else {
				_ = file.SetCellValue(sheetName, fmt.Sprintf("J%d", currentRow), "无凭证")
			}

			_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("E%d", currentRow), contentStyle)
			_ = file.SetCellStyle(sheetName, fmt.Sprintf("F%d", currentRow), fmt.Sprintf("H%d", currentRow), numberStyle)
			_ = file.SetCellStyle(sheetName, fmt.Sprintf("I%d", currentRow), fmt.Sprintf("I%d", currentRow), contentStyle)
			_ = file.SetCellStyle(sheetName, fmt.Sprintf("J%d", currentRow), fmt.Sprintf("J%d", currentRow), contentStyle)
		}

		// 物料扣款合计
		currentRow++
		_ = file.MergeCell(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow))
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), "物料扣款合计:")
		_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", currentRow), supplier.MaterialDeductAmount)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow), totalStyle)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("M%d", currentRow), fmt.Sprintf("M%d", currentRow), redTotalStyle)

		// 给所有物料扣款数据添加表格样式
		if len(supplier.MaterialDeduct) > 0 {
			_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", startMaterialDeductRow), fmt.Sprintf("M%d", currentRow-1), contentStyle)
		}
	}

	// 应付金额(合同金额 - 工时扣款 - 物料扣款)
	currentRow += 2
	_ = file.MergeCell(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow))
	_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), "应付金额合计 = 合同总金额 - 工时扣款 - 物料扣款")
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("M%d", currentRow), subTitleStyle)
	_ = file.SetRowHeight(sheetName, currentRow, 25)

	// 计算实际应付金额 = 合同总金额 - 工时扣款 - 物料扣款
	actualPayable := supplier.ContractTotalAmount - supplier.ManHourDeductAmount - supplier.MaterialDeductAmount

	// 合同总金额
	currentRow++
	_ = file.MergeCell(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("L%d", currentRow))
	_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), "合同总金额:")
	_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", currentRow), supplier.ContractTotalAmount)
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("L%d", currentRow), totalStyle)
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("M%d", currentRow), fmt.Sprintf("M%d", currentRow), redTotalStyle)
	_ = file.SetRowHeight(sheetName, currentRow, 22)

	// 工时扣款
	currentRow++
	_ = file.MergeCell(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("L%d", currentRow))
	_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), "工时扣款:")
	_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", currentRow), supplier.ManHourDeductAmount)
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("L%d", currentRow), totalStyle)
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("M%d", currentRow), fmt.Sprintf("M%d", currentRow), redTotalStyle)
	_ = file.SetRowHeight(sheetName, currentRow, 22)

	// 物料扣款
	currentRow++
	_ = file.MergeCell(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("L%d", currentRow))
	_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), "物料扣款:")
	_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", currentRow), supplier.MaterialDeductAmount)
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("L%d", currentRow), totalStyle)
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("M%d", currentRow), fmt.Sprintf("M%d", currentRow), redTotalStyle)
	_ = file.SetRowHeight(sheetName, currentRow, 22)

	// 最终应付金额
	currentRow++
	_ = file.MergeCell(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("L%d", currentRow))
	_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), "应付金额:")
	_ = file.SetCellValue(sheetName, fmt.Sprintf("M%d", currentRow), actualPayable)
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", currentRow), fmt.Sprintf("L%d", currentRow), totalStyle)
	_ = file.SetCellStyle(sheetName, fmt.Sprintf("M%d", currentRow), fmt.Sprintf("M%d", currentRow), redTotalStyle)
	_ = file.SetRowHeight(sheetName, currentRow, 22)

	return nil
}

func NewPmsFinancePaymentService() *PmsFinancePaymentService {
	return &PmsFinancePaymentService{
		Service: &yc.Service{},
	}
}
