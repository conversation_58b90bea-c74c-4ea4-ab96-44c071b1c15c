package admin

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/imhuso/lookah-erp/admin/modules/pms/model"
	"github.com/imhuso/lookah-erp/admin/modules/pms/service"
	"github.com/imhuso/lookah-erp/admin/yc"
)

type PmsMaterialController struct {
	*yc.Controller
}

// SetMaterialLevelReq 设置物料等级
type SetMaterialLevelReq struct {
	g.Meta `path:"/setMaterialLevel" method:"POST" summary:"设置物料等级" tags:"物料等级"`
}

// SetMaterialLevel 设置物料等级
func (c *PmsMaterialController) SetMaterialLevel(ctx context.Context, req *SetMaterialLevelReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsMaterialService().SetMaterialLevel(ctx)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil
}

// ImportMaterialAddressReq 导入物料地址
type ImportMaterialAddressReq struct {
	g.Meta      `path:"/importMaterialAddressData" method:"POST" summary:"删除物料地址" tags:"物料地址"`
	AddressData []*model.ImportMaterialAddressData `json:"address_data"`
}

// ImportMaterialAddress 控制器
func (c *PmsMaterialController) ImportMaterialAddress(ctx context.Context, req *ImportMaterialAddressReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsMaterialService().ImportMaterialAddress(ctx, req.AddressData)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil
}

// DeleteMaterialAddressReq 删除物料地址
type DeleteMaterialAddressReq struct {
	g.Meta      `path:"/deleteMaterialAddress" method:"POST" summary:"删除物料地址" tags:"物料地址"`
	AddressName string `json:"address_name" v:"required#物料地址不能位空"`
}

// DeleteMaterialAddress 控制器
func (c *PmsMaterialController) DeleteMaterialAddress(ctx context.Context, req *DeleteMaterialAddressReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsMaterialService().DeleteMaterialAddress(ctx, req.AddressName)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil
}

// AddMaterialAddressReq 增加物料地址
type AddMaterialAddressReq struct {
	g.Meta      `path:"/addMaterialAddress" method:"POST" summary:"增加物料地址" tags:"物料地址"`
	AddressName string `json:"address_name" v:"required#物料地址不能位空"`
}

// AddMaterialAddress 控制器
func (c *PmsMaterialController) AddMaterialAddress(ctx context.Context, req *AddMaterialAddressReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsMaterialService().AddMaterialAddress(ctx, req.AddressName)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// GetMaterialByIdReq 根据物料ID获取物料位置列表
type GetMaterialByIdReq struct {
	g.Meta `path:"/getMaterialById" method:"GET" summary:"获取物料列表" tags:"物料管理"`
	Id     int64 `json:"id" v:"required#物料id不能为空"`
}

// GetMaterialById 控制器
func (c *PmsMaterialController) GetMaterialById(ctx context.Context, req *GetMaterialByIdReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsMaterialService().GetMaterialById(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// GetMaterialByNameReq 根据物料名或物料编码获取物料位置列表
type GetMaterialByNameReq struct {
	g.Meta  `path:"/getMaterialByName" method:"GET" summary:"获取物料列表" tags:"物料管理"`
	Keyword string `json:"keyword" v:"required#物料名称或物料编码不能为空"`
}

// GetMaterialByName 控制器
func (c *PmsMaterialController) GetMaterialByName(ctx context.Context, req *GetMaterialByNameReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsMaterialService().GetMaterialByName(ctx, req.Keyword)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// GetMaterialAddressReq 获取物料位置列表
type GetMaterialAddressReq struct {
	g.Meta `path:"/getMaterialAddress" method:"GET" summary:"获取物料位置列表" tags:"物料管理"`
}

// GetMaterialAddress 控制器
func (c *PmsMaterialController) GetMaterialAddress(ctx context.Context, req *GetMaterialAddressReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsMaterialService().GetMaterialAddress(ctx)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// MaterialInboundSummaryReq 出入库汇总
type MaterialInboundSummaryReq struct {
	g.Meta `path:"/inbound/summary" method:"GET" summary:"出入库汇总" tags:"物料管理"`

	Year int `json:"year"`
	// 月份
	Month int `json:"month"`
}

// MaterialSummary 控制器
func (c *PmsMaterialController) MaterialSummary(ctx context.Context, req *MaterialInboundSummaryReq) (res *yc.BaseRes, err error) {
	data, err := service.NewPmsMaterialService().MaterialInboundSummary(ctx, req.Year, req.Month)
	if err != nil {
		return nil, err
	}
	return yc.Ok(data), nil
}

// ExportMaterialListReq 导出物料列表
type ExportMaterialListReq struct {
	g.Meta `path:"/export" method:"GET" summary:"导出物料列表" tags:"物料管理"`
}

// ExportMaterialList 导出物料列表
func (c *PmsMaterialController) ExportMaterialList(ctx context.Context, _ *ExportMaterialListReq) (res *yc.BaseRes, err error) {
	fileName, content, err := service.NewPmsMaterialService().ExportMaterialList(ctx)
	if err != nil {
		return nil, err
	}

	response := g.RequestFromCtx(ctx).Response
	// 设置响应头
	response.Header().Set("Content-Disposition", "attachment; filename="+fileName)
	response.Header().Set("Content-Type", "application/octet-stream")

	// 返回 Excel 文件内容
	response.WriteExit(content)
	return nil, nil
}

// ExportMaterialInboundSummaryReq 导出出入库汇总
type ExportMaterialInboundSummaryReq struct {
	g.Meta `path:"/summaryExport" method:"GET" summary:"导出出入库汇总" tags:"物料管理"`

	Start *gtime.Time `json:"start" v:"required#开始时间不能为空"`
	// 结束时间
	End *gtime.Time `json:"end" v:"required#结束时间不能为空"`
}

// ExportMaterialInboundSummary 导出出入库汇总
func (c *PmsMaterialController) ExportMaterialInboundSummary(ctx context.Context, req *ExportMaterialInboundSummaryReq) (res *yc.BaseRes, err error) {
	fileName, content, err := service.NewPmsMaterialService().ExportMaterialInboundSummary(ctx, req.Start, req.End)
	if err != nil {
		return nil, err
	}

	response := g.RequestFromCtx(ctx).Response
	// 设置响应头
	response.Header().Set("Content-Disposition", "attachment; filename="+fileName)
	response.Header().Set("Content-Type", "application/octet-stream")

	// 返回 Excel 文件内容
	response.WriteExit(content)
	return nil, nil
}

// ImportExcelReq 导入物料
type ImportExcelReq struct {
	g.Meta       `path:"/importExcel" method:"POST" summary:"导入物料" tags:"物料管理"`
	MaterialList []*model.PmsMaterial `json:"materialList" v:"required#物料列表不能为空"`
}

func (c *PmsMaterialController) ImportExcel(ctx context.Context, req *ImportExcelReq) (res *yc.BaseRes, err error) {
	err = service.NewPmsMaterialService().ImportExcel(ctx, req.MaterialList)
	if err != nil {
		return nil, err
	}
	return yc.Ok(nil), nil
}

func init() {
	var pmsMaterialController = &PmsMaterialController{
		&yc.Controller{
			Prefix:  "/admin/pms/material",
			Api:     []string{"Add", "Delete", "Update", "Info", "List", "Page"},
			Service: service.NewPmsMaterialService(),
		},
	}
	// 注册路由
	yc.RegisterController(pmsMaterialController)
}
