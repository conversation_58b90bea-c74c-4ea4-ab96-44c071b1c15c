<script setup lang="ts" name="MaterialInbound">
import { useCrud, useTable } from '@cool-vue-p/crud'
import { service } from '/@/cool'
import dayjs from 'dayjs'
import { downloadBlob } from '/@/cool/utils'
import { ElMessage } from 'element-plus'
import { INBOUND_TYPE } from '/$/pms/views/material/js/constant.js'
import {ref} from "vue";

const props = withDefaults(defineProps<{
  api?: any
  showPrice?: boolean
}>(), {
  api: service.pms.finance,
  showPrice: true,
})

const queryLoading = ref<boolean>(false)
const exportLoading = ref<boolean>(false)
const queryParams = ref<any>({
  date: [],
  keyWord: '',
  type: undefined,
  status: undefined,
  useStatus: false,
  useType: false,
})
const supplierList = ref<any[]>([])
// 获取供应商列表
async function getSupplier() {
  try {
    const res = await service.pms.supplier.request({
      url: '/list',
      method: 'POST',
    })
    supplierList.value = res
  }
  catch (e: any) {
    console.error(e)
  }
}
getSupplier()

const Crud = useCrud(
  {
    dict: {
      api: { page: 'materialInboundPage' },
    },
    service: props.api,
    async onRefresh(params, { next, done, render }) {
      done()
      formatQueryParams(params)
      const { list, pagination } = await next(params)
      // 渲染数据
      render(list, pagination)
    },
  },
  (app) => {
    app.refresh()
  },
)

function formatQueryParams(params: any) {
  if (queryParams.value.date && queryParams.value.date.length > 0) {
    const start = dayjs(queryParams.value.date[0]).format('YYYY-MM-DD')
    const end = dayjs(queryParams.value.date[1]).format('YYYY-MM-DD')
    params.date = `${start},${end}`
  }
  if (queryParams.value.keyWord) {
    params.keyWord = queryParams.value.keyWord
  }
  if (queryParams.value.supplierId !== undefined) {
    params.supplierId = queryParams.value.supplierId
  }
  if (queryParams.value.type !== undefined) {
    params.type = queryParams.value.type
    params.useType = true
  }
  else {
    params.useType = false
  }
  if (queryParams.value.status !== undefined) {
    params.status = queryParams.value.status
    params.useStatus = true
    if (params.status === 0) {
      params.status = -1
    }
  }
  else {
    params.useStatus = false
  }
  return params
}

const typeOptions = ref<any>(INBOUND_TYPE)

const statusOptions = ref<any>([
  { label: '草稿', value: 0, type: 'info' },
  { label: '待审批', value: 4, type: 'primary' },
  { label: '入库中', value: 2, type: 'info' },
  { label: '已完成', value: 3, type: 'success' },
])

const Table = useTable({
  columns: [
    { label: '单据时间', prop: 'inboundTime', minWidth: 90, formatter(row) {
      return row.inboundTime ? dayjs(row.inboundTime).format('YYYY-MM-DD') : ''
    } },
    { label: '入库类型', prop: 'type', minWidth: 90, dict: typeOptions.value },
    { label: '采购PO', prop: 'po', minWidth: 150 },
    { label: '物料编码', prop: 'code', minWidth: 160, showOverflowTooltip: true },
    { label: '物料名称', prop: 'materialName', minWidth: 160, showOverflowTooltip: true },
    // { label: '物料等级', prop: 'level', minWidth: 70 },
    {
      label: '物料等级',
      prop: 'level',
      minWidth: 70,
    },
    { label: '入库数量', prop: 'quantity', minWidth: 80 },
    { label: '采购数量', prop: 'purchaseQuantity', minWidth: 80 },
    { label: '采购单价', prop: 'unitPrice', minWidth: 80 },
    { label: '入库金额', minWidth: 100,
      formatter: (row: any) => {
        return (row.unitPrice * row.quantity).toFixed(2) || 0
      },
    },
    { label: '付款状态', prop: 'payment_status', minWidth: 120 },
    { label: '供应商', prop: 'supplierName', minWidth: 180, showOverflowTooltip: true },
    { label: '系统单号', prop: 'no', minWidth: 120 },
    { label: '规格/型号', prop: 'model', minWidth: 180, showOverflowTooltip: true },

    // { label: '单位', prop: 'unit', width: 80 },
    // { label: '状态', prop: 'status', width: 120, dict: statusOptions.value },
    // {
    //   label: '入库凭证',
    //   prop: 'voucher',
    //   width: 120,
    //   component: { name: 'cl-image', props: { fit: 'cover', lazy: true, size: [50, 50] } },
    // },
    // { label: '创建时间', prop: 'createTime', width: 180 },
    // {
    //   type: 'op',
    //   label: '操作',
    //   width: 150,
    //   buttons: ['slot-btn-updateStatus'],
    // },
  ],
})

function doQuery() {
  try {
    if (Crud?.value?.params?.page) {
      Crud.value.params.page = 1
    }
    queryLoading.value = true
    Crud?.value?.refresh()
  }
  catch (e: any) {
    console.error(e)
  }
  finally {
    queryLoading.value = false
  }
}

function refresh() {
  queryParams.value.keyWord = ''
  queryParams.value.date = []
  queryParams.value.supplierId = undefined
  if (Crud?.value?.params) {
    Crud.value.params.page = 1
    Crud.value.params.size = 20
  }
  Crud?.value?.refresh()
}

function dateChange(val: any) {
  val ? doQuery() : refresh()
}

async function exportExcel() {
  exportLoading.value = true
  try {
    const reqParams: any = formatQueryParams({ page: 1, size: 100000 })
    const res = await props.api.request({
      url: '/materialInboundExportExcel',
      method: 'GET',
      responseType: 'blob',
      params: reqParams,
    })
    if (downloadBlob(res))
      ElMessage.success('导出成功')
  }
  catch (e: any) {
    console.error(e)
  }
  finally {
    exportLoading.value = false
  }
}
function getTipText(level: string) {
  let text = ""
  switch (level) {
    case 'A':
      text = 'BOM用量等于1，单价大于等于1';
      break;
    case 'B':
      text = 'BOM用量大于1，单价大于等于0.1';
      break;
    case 'C':
      text = 'BOM用量等于1，单价大于等于0.5';
      break;
    case 'D':
      text = 'BOM用量小于1，单价大于等于5';
      break;
    case 'E':
      text = 'BOM用量小于1，单价大于等于1并且小于5';
      break;
    case 'F':
      text = 'BOM用量小于1，单价小于1';
      break;
    case 'G':
      text = 'BOM用量等于1，单价小于0.5';
      break;
    case 'H':
      text = 'BOM用量大于1，单价小于0.1';
      break;
    default:
      text = '';
  }
  return text
}

</script>

<template>
  <cl-crud ref="Crud">
    <el-row>
      <el-button @click="refresh">
        刷新
      </el-button>
      <el-button type="success" icon="Download" :loading="exportLoading" @click="exportExcel">
        导出excel
      </el-button>
      <cl-flex1 />
      <div>
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="mr-20px w-200px" @change="doQuery">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div>
        <el-select v-model="queryParams.type" placeholder="请选择类型" clearable class="mr-20px w-200px" @change="doQuery">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div>
        <el-select v-model="queryParams.supplierId" placeholder="请选择供应商" clearable class="mr-20px w-200px" @change="doQuery">
          <el-option v-for="item in supplierList" :key="item.id" :label="item.supplierName" :value="item.id" />
        </el-select>
      </div>
      <div style="margin-right: 20px">
        <el-date-picker
          v-model="queryParams.date"
          type="daterange"
          clearable
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="dateChange"
        />
      </div>
      <el-input v-model="queryParams.keyWord" placeholder="请输入物料编号或物料名称或入库单号或Po" style="width: 500px" clearable @clear="refresh" @keyup.enter="doQuery" />
      <el-button type="primary" mx="10px" :loading="queryLoading" @click="doQuery">
        搜索
      </el-button>
    </el-row>
    <el-row>
      <!-- 数据表格 -->
      <cl-table ref="Table" row-key="rowIndex">
        <template #column-level="{ scope }">
          <el-tooltip
            v-if="scope.row.level != ''"
            class="box-item"
            effect="dark"
            :content="getTipText(scope.row.level)"
            placement="top-start"
          >
            <el-button>{{scope.row.level}}</el-button>
          </el-tooltip>
        </template>
      </cl-table>
    </el-row>
    <el-row>
      <cl-flex1 />
      <!-- 分页控件 -->
      <cl-pagination />
    </el-row>
  </cl-crud>
</template>

<style scoped>
/* 简洁对话框样式 */
.order-info {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  align-items: center;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.value {
  color: #303133;
  font-weight: 500;
}

.payment-input {
  margin-top: 20px;
}

.input-tips {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}
</style>
