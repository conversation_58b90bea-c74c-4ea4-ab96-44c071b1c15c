import{c as O,b as B,A as J,K as C,q as W,w as o,h as t,i as c,J as R,v as d,j as f,L as F,G as X,H as Z,t as x,y as ee,W as te,T as D,E as b,U as Q,o as T}from"./.pnpm-Kv7TmmH8.js";import{i as V,s as S}from"./index-BuqCFB-b.js";import{_ as oe}from"./material-table-columns.vue_vue_type_script_setup_true_lang-hBwyRg0n.js";import{_ as ae}from"./_plugin-vue_export-helper-DlAUqK2U.js";const le=O({name:"undefined"}),ne=O({...le,props:{showBtn:{type:Boolean,default:!1},showMaterialDtl:{type:Boolean,default:!1},tableOp:{},status:{}},emits:["selected","deleted","detail","edit"],setup(U,{expose:M,emit:Y}){const p=U,w=Y,u=B(!1),n=B({date:[],keyWord:"",status:void 0,isShowShiftWork:!1,isShowUnfinished:!1});J(()=>{p.status!==void 0&&(n.value.status=p.status)});const s=V.useCrud({dict:{api:{page:"queryPage"}},service:S.pms.workOrder,async onRefresh(e,{next:a,done:l,render:m}){l(),$(e);const{list:y,pagination:g}=await a(e);m(y,g)}},e=>{e.refresh()});function $(e){return n.value.keyWord&&(e.keyWord=n.value.keyWord),n.value.status!==void 0&&(e.status=n.value.status),n.value.isShowShiftWork!==void 0&&(e.isShowShiftWork=n.value.isShowShiftWork),n.value.isShowUnfinished!==void 0&&(e.isShowUnfinished=n.value.isShowUnfinished),e}const E=V.useTable({columns:[{type:"expand",prop:"extras"},{label:"生产单号",prop:"sn",width:120},{label:"工单号",prop:"workOrderNo",width:120},{label:"产品信息",prop:"productName"},{label:"计划生产数量",prop:"quantity",width:200},{label:"计划生产日期",prop:"planProductionDate",width:200,formatter(e){return e.planProductionDate?C(e.planProductionDate).format("YYYY-MM-DD"):""}},{label:"创建时间",prop:"createTime",width:200,formatter(e){return e.createTime?C(e.createTime).format("YYYY-MM-DD HH:mm:ss"):""}},{type:"op",label:"操作",fixed:"right",hidden:p.showBtn,buttons:p.tableOp.buttons,width:p.tableOp.width}]});function v(){var e,a,l;try{(a=(e=s==null?void 0:s.value)==null?void 0:e.params)!=null&&a.page&&(s.value.params.page=1),u.value=!0,(l=s==null?void 0:s.value)==null||l.refresh().finally(()=>{u.value=!1})}catch(m){console.error(m),u.value=!1}}function _(){var e,a;n.value.keyWord="",n.value.date=[],(e=s==null?void 0:s.value)!=null&&e.params&&(s.value.params.page=1,s.value.params.size=20),u.value=!0,(a=s==null?void 0:s.value)==null||a.refresh().finally(()=>{u.value=!1})}function N(e){w("selected",e)}async function P(e){await D.confirm("确定完成吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})&&(u.value=!0,S.pms.workOrder.complete(e).then(l=>{b.success("修改成功"),_()}).catch(l=>{b.error(l.message),u.value=!1}))}async function q(e){w("detail",Q(e))}async function z(e){await D.confirm("确定要删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})&&(u.value=!0,S.pms.workOrder.delWorkOrder(e).then(l=>{b.success("删除成功"),_(),w("deleted",e)}).catch(l=>{b.error(l.message),u.value=!1}))}async function j(e){w("edit",Q(e))}function H(e){if(!e||Object.keys(e).length===0)return"";const a=e.outboundQuantity-e.calcBomQuantity;return a===0?"row-green":a>0?"row-red":""}return M({refresh:_}),(e,a)=>{const l=c("el-button"),m=c("cl-flex1"),y=c("el-switch"),g=c("el-input"),k=c("el-row"),h=c("el-table-column"),K=c("el-table"),I=c("cl-table"),L=c("cl-pagination"),A=c("cl-crud"),G=Z("loading");return T(),W(A,{ref_key:"Crud",ref:s},{default:o(()=>[t(k,null,{default:o(()=>[t(l,{loading:d(u),onClick:_},{default:o(()=>[f(" 刷新 ")]),_:1},8,["loading"]),R(e.$slots,"add-btn",{},void 0,!0),t(m),t(y,{modelValue:d(n).isShowUnfinished,"onUpdate:modelValue":a[0]||(a[0]=i=>d(n).isShowUnfinished=i),size:"large","active-text":"只显示未结工单","inactive-text":"全部",mr:"30px",onChange:v},null,8,["modelValue"]),t(y,{modelValue:d(n).isShowShiftWork,"onUpdate:modelValue":a[1]||(a[1]=i=>d(n).isShowShiftWork=i),size:"large","active-text":"只显示欠料","inactive-text":"全部",mr:"30px",onChange:v},null,8,["modelValue"]),t(g,{modelValue:d(n).keyWord,"onUpdate:modelValue":a[2]||(a[2]=i=>d(n).keyWord=i),placeholder:"请输入工单号或生产单号",style:{width:"500px"},clearable:"",onClear:_,onKeyup:F(v,["enter"])},null,8,["modelValue"]),t(l,{type:"primary",mx:"10px",loading:d(u),onClick:v},{default:o(()=>[f(" 搜索 ")]),_:1},8,["loading"])]),_:3}),t(k,null,{default:o(()=>[X((T(),W(I,{ref_key:"Table",ref:E,"auto-height":!1,height:600,"element-loading-text":"数据加载中..."},{"slot-btn-outbound":o(({scope:i})=>[t(l,{type:"primary",text:"",bg:"",onClick:r=>N(i.row)},{default:o(()=>[f(" 选择 ")]),_:2},1032,["onClick"])]),"slot-btn-finish":o(({scope:i})=>[t(l,{type:"success",text:"",bg:"",onClick:r=>P(i.row)},{default:o(()=>[f(" 完成 ")]),_:2},1032,["onClick"])]),"slot-btn-detail":o(({scope:i})=>[t(l,{type:"primary",text:"",bg:"",onClick:r=>q(i.row)},{default:o(()=>[f(" 查看 ")]),_:2},1032,["onClick"])]),"slot-btn-del":o(({scope:i})=>[t(l,{type:"danger",text:"",bg:"",onClick:r=>z(i.row)},{default:o(()=>[f(" 删除 ")]),_:2},1032,["onClick"])]),"slot-btn-edit":o(({scope:i})=>[t(l,{type:"warning",text:"",bg:"",onClick:r=>j(i.row)},{default:o(()=>[f(" 刷新工单 ")]),_:2},1032,["onClick"])]),"column-extras":o(({scope:i})=>[t(K,{data:i.row.extras,stripe:"",border:"","max-height":"600"},{default:o(()=>[t(oe,{"show-detail":p.showMaterialDtl,"auto-width":""},null,8,["show-detail"]),t(h,{prop:"unit",label:"单位",align:"center",width:"70","show-overflow-tooltip":""}),t(h,{prop:"expectedInbound",label:"在途",align:"center",width:"100","show-overflow-tooltip":""}),t(h,{prop:"inventory",label:"库存",align:"center",width:"100","show-overflow-tooltip":""}),t(h,{prop:"calcBomQuantity",label:"计算用量",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:r})=>[f(x(r.calcBomQuantity),1)]),_:1}),t(h,{label:"工单待发",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:r})=>[f(x(r.calcBomQuantity-r.outboundQuantity-r.inventory>=0?r.calcBomQuantity-r.outboundQuantity-r.inventory:0),1)]),_:1}),t(h,{prop:"outboundQuantity",label:"已出库数量",align:"center",width:"100","show-overflow-tooltip":""},{default:o(({row:r})=>[ee("span",{class:te([H(r)])},x(r.outboundQuantity),3)]),_:1})]),_:2},1032,["data"])]),_:1})),[[G,d(u)]])]),_:1}),t(k,null,{default:o(()=>[t(m),t(L)]),_:1})]),_:3},512)}}}),ce=ae(ne,[["__scopeId","data-v-e590ca98"]]);export{ce as default};
