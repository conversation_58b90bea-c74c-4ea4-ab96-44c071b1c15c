import{c as ae,aw as ce,e as w,as as Q,aq as pe,b as fe,r as me,z as ge,i as S,f as C,o as c,y,h as f,q as $,B as m,W as G,w as g,J as N,j as ve,t as x,v as d,F as ye,G as he,Y as ee,aJ as _e,I as be,aK as ke,ar as we,m as Se,aL as Ce,E as M,Z as $e}from"./.pnpm-Kv7TmmH8.js";import{f as xe,a as Be,_ as Pe,g as ze,b as De}from"./viewer.vue_vue_type_script_setup_true_name_item-viewer_lang-Y2-MQXPQ.js";import{k as Ue,n as Ve,o as Fe,q as Ne,t as X}from"./index-BuqCFB-b.js";import{a as Ie}from"./index-BAHxID_w.js";import{_ as Te}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ee={class:"cl-upload__item"},Le={class:"cl-upload__text"},Oe={class:"cl-upload__name"},qe={class:"cl-upload__size"},Ae={class:"cl-upload__actions"},Ge={key:2,class:"cl-upload__progress"},Me={key:3,class:"cl-upload__error"},Xe=ae({name:"cl-upload"}),je=ae({...Xe,props:{modelValue:{type:[String,Array],default:()=>[]},whitelist:{type:Array,default:["png","jpg","jpeg","PNG","JPG","JPEG","xls","xlsx","XLS","XLSX","doc","docx","DOC","DOCX","pdf","PDF","ppt","PPT","pptx","PPTX","txt","TXT","DWG","DXF","DWF","SVG","dwg","dxf","dwf","svg","STL","STEP","IGES","OBJ","3MF","FBX","stl","step","iges","obj","3mf","fbx"]},type:{type:String,default:"image"},showResult:{type:Boolean,default:!0},accept:String,multiple:Boolean,limit:Number,limitSize:{type:Number,default:20},limitUpload:{type:Boolean,default:!0},size:[String,Number,Array],text:String,prefixPath:{type:String,default:"app"},menu:{type:String,default:"base"},showFileList:{type:Boolean,default:!0},draggable:Boolean,disabled:Boolean,customClass:String,beforeUpload:Function,isSpace:Boolean,isPrivate:{type:Boolean,default:!0},classifyId:{type:Number,default:0},isEdit:null,scope:null,isDisabled:Boolean,compress:Boolean},emits:["update:modelValue","upload","success","error","progress"],setup(p,{expose:te,emit:le}){ce(a=>({"1a9e0224":j.value[0],"1a9e0243":j.value[1]}));const s=p,B=le,{service:I,refs:b,setRefs:P}=Ie(),{user:se}=Ue(),{options:z}=Ve.get("upload"),j=w(()=>{const a=s.size||z.size;return(Q(a)?a:[a,a]).map(e=>pe(e)?`${e}px`:e)}),T=w(()=>s.isDisabled||s.disabled),E=w(()=>T.value||s.isSpace),J=s.limit||z.limit.upload,R=s.limitSize||z.limit.size,Y=s.text||z.text,L=w(()=>({Authorization:se.token})),r=fe([]),oe=me({options:{group:"Upload",animation:300,ghostClass:"Ghost",dragClass:"Drag",draggable:".is-drag",disabled:!s.draggable}}),D=w(()=>s.accept||(s.type==="file"?"*":"image/*")),O=w(()=>s.multiple?J-r.value.length>0:r.value.length===0);function K(a){var e;return s.type==="image"?"image":(e=De(a))==null?void 0:e.value}async function U(a,e){function o(){const t={type:K(a.name),preload:"",progress:0,url:a.url,uid:a.uid,size:a.size};return t.preload=t.url||(t.type==="image"?window.webkitURL.createObjectURL(a):a.name),e?Object.assign(e,t):s.multiple?(O.value||!s.limitUpload)&&r.value.push(t):r.value=[t],B("upload",t),!0}if(s.beforeUpload){const t=s.beforeUpload(a,e);return Ne(t)?t.then(o).catch(()=>null):t&&o(),t}else return a.size/1024/1024>=R?(M.error(`上传文件大小不能超过 ${R}MB!`),!1):o()}function W(a){r.value.splice(a,1),V()}function Z(){r.value=[]}function re(a){var e;a.type==="image"?(e=b.viewer)==null||e.open(a,r.value.map(o=>({...o,url:o.preload}))):window.open(a.url)}async function q(a,e){if(e||(e=r.value.find(t=>t.uid===a.file.uid)),!e)return!1;const o=X("");try{let t=`${o}_${a.file.name}`;const{mode:i,type:k}=await I.base.comm.uploadMode();return new Promise((A,F)=>{async function l({host:n,preview:v,data:H}){const _=new FormData;for(const u in H)_.append(u,H[u]);i==="cloud"&&(t=`${$e().format("YYYYMMDD")}/${t}`,s.isPrivate?t=`private/${t}`:t=`public/${t}`),_.append("key",t),_.append("type",e.type),_.append("classifyId",s.classifyId.toString()),_.append("isPrivate",s.isPrivate.toString()),_.append("file",a.file),await I.request({url:n,method:"POST",headers:{"Content-Type":"multipart/form-data"},timeout:6e5,data:_,onUploadProgress(u){const de=Number.parseInt(String(u.loaded/u.total*100));e.progress=Math.min(de,99),B("progress",e)},proxy:i==="local"}).then(u=>{e.progress=100,i==="local"?(e.url=u,e.key=u.replace(/^https?:\/\/[^/]+/,"")):(e.url=`${v||n}/${t}`,e.key=t),e.fileId=o,e.name=e.preload,B("success",{...e,filename:a.file.name}),A(e.url),V()}).catch(u=>{M.error(u.message),e.error=u.message,B("error",e),F(u)})}i==="local"?l({host:"/admin/base/comm/upload"}):I.base.comm.upload().then(n=>{switch(k){case"cos":l({host:n.url,data:n.credentials});break;case"oss":l({host:n.host,data:{OSSAccessKeyId:n.OSSAccessKeyId,policy:n.policy,signature:n.signature}});break;case"qiniu":l({host:n.uploadUrl,preview:n.publicDomain,data:{token:n.token}});break}}).catch(F)})}catch{M.error("上传配置错误")}}function ne(){return r.value.find(a=>a.progress!==100)}function V(){r.value.find(e=>!e.url)||B("update:modelValue",ze(r.value))}function ie(a){var e,o,t;Z(),(e=b.upload)==null||e.clearFiles(),(o=b.upload)==null||o.handleStart(a),(t=b.upload)==null||t.submit()}function ue(){b.upload.$el.querySelector("input").click()}const h={data:null,open(a){var e;h.data=a,(e=b.space)==null||e.open({limit:a||!s.multiple?1:J})},onConfirm(a){a.forEach(e=>{U({uid:X(),...e},h.data)}),V(),h.data=null}};return ge(()=>s.modelValue,a=>{const e=(Q(a)?a:(a||"").split(",")).filter(Boolean),o=[];r.value=e.map(t=>{const i=r.value.find(k=>t===k.url&&!o.includes(k.uid));return i?(o.push(i.uid),i):{type:K(t),progress:0,uid:X(),url:t,preload:t}}).filter((t,i)=>s.multiple?!0:i===0)},{immediate:!0}),te({isAdd:O,list:r,check:ne,clear:Z,remove:W,upload:ie,open,handleOpen:ue}),(a,e)=>{const o=S("el-button"),t=S("el-upload"),i=S("el-icon"),k=S("el-image"),A=S("el-progress"),F=S("cl-upload-space");return c(),C("div",null,[y("div",{class:G(["cl-upload__wrap",[p.customClass]])},[y("div",{class:G(["cl-upload",[`cl-upload--${p.type}`,{"is-disabled":T.value}]])},[p.type==="file"?(c(),C("div",{key:0,class:"cl-upload__file-btn",onClick:e[0]||(e[0]=l=>h.open())},[f(t,{ref:d(P)("upload"),action:"",accept:D.value,"show-file-list":!1,"before-upload":U,"http-request":q,headers:L.value,multiple:p.multiple,disabled:E.value},{default:g(()=>[N(a.$slots,"default",{},()=>[f(o,{type:"success"},{default:g(()=>[ve(x(d(Y)),1)]),_:1}),N(a.$slots,"uploadSlot",{},void 0,!0)],!0)]),_:3},8,["accept","headers","multiple","disabled"])])):m("",!0),p.showFileList?(c(),$(d(Ce),Se({key:1,modelValue:r.value,"onUpdate:modelValue":e[2]||(e[2]=l=>r.value=l),class:"cl-upload__list",tag:"div"},oe.options,{"item-key":"uid",onEnd:V}),{footer:g(()=>[p.type==="image"&&O.value?(c(),C("div",{key:0,class:"cl-upload__footer",onClick:e[1]||(e[1]=l=>h.open())},[f(t,{ref:d(P)("upload"),action:"",accept:D.value,"show-file-list":!1,"before-upload":U,"http-request":q,headers:L.value,multiple:p.multiple,disabled:E.value},{default:g(()=>[N(a.$slots,"default",{},()=>[y("div",Ee,[f(i,{size:24},{default:g(()=>[f(d(we))]),_:1}),y("span",Le,x(d(Y)),1)])],!0)]),_:3},8,["accept","headers","multiple","disabled"])])):m("",!0)]),item:g(({element:l,index:n})=>[p.showFileList?(c(),$(t,{key:0,action:"",class:"is-drag",accept:D.value,"show-file-list":!1,"http-request":v=>q(v,l),"before-upload":v=>{U(v,l)},headers:L.value,disabled:E.value,onClick:v=>h.open(l)},{default:g(()=>[N(a.$slots,"item",{item:l,index:n},()=>[y("div",{class:G(["cl-upload__item",[`is-${l.type}`]])},[l.type==="image"?(c(),$(k,{key:0,src:l.preload,fit:"cover"},null,8,["src"])):(c(),C(ye,{key:1},[y("span",Oe,x(d(xe)(l.preload))+"."+x(d(Fe)(l.preload)),1),y("span",qe,x(d(Be)(l.size)),1)],64)),y("div",Ae,[p.isPrivate?m("",!0):he((c(),$(i,{key:0,onClick:ee(v=>re(l),["stop"])},{default:g(()=>[f(d(_e))]),_:2},1032,["onClick"])),[[be,l.url]]),T.value?m("",!0):(c(),$(i,{key:1,onClick:ee(v=>W(n),["stop"])},{default:g(()=>[f(d(ke))]),_:2},1032,["onClick"]))]),l.progress>0&&l.progress<100&&!l.url?(c(),C("div",Ge,[f(A,{percentage:l.progress,"show-text":!1},null,8,["percentage"])])):m("",!0),l.error?(c(),C("div",Me,x(l.error),1)):m("",!0)],2)],!0)]),_:2},1032,["accept","http-request","before-upload","headers","disabled","onClick"])):m("",!0)]),_:3},16,["modelValue"])):m("",!0)],2)],2),f(Pe,{ref:d(P)("viewer")},null,512),p.isSpace?(c(),$(F,{key:0,ref:d(P)("space"),"show-btn":!1,accept:D.value,onConfirm:h.onConfirm},null,8,["accept","onConfirm"])):m("",!0)])}}}),Ze=Te(je,[["__scopeId","data-v-026c7f2a"]]);export{Ze as default};
