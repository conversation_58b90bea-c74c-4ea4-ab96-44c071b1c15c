import{g as fe,i as M,e as Z}from"./index-BuqCFB-b.js";import{a as _e}from"./index-BAHxID_w.js";import{c as Q,b as i,A as be,K as he,q as O,w as y,h as n,B as ye,G as ve,y as ge,i as p,v as J,H as ke,j as $,E as v,M as we,N as xe,Z as qe,o as F}from"./.pnpm-Kv7TmmH8.js";const Ee=Q({name:"pms-job-instruction"}),Ce=Q({...Ee,setup(Ne){var G,U;const A=i([]),q=i([]),D=i([]),C=i([]),E=i([]),X=i([]),R=i([]),S=i([]),{service:o}=_e(),{dict:ee}=fe(),j=i(null),s=i(!1),I=ee.get("color");(G=I.value)!=null&&G.find(e=>e.value===0)||(U=I.value)==null||U.unshift({label:"无",value:0});async function ae(){try{const e=await o.pms.production.schedule.request({url:"/list",method:"POST"});A.value=e,q.value=e==null?void 0:e.map(a=>({value:a.id,label:a.sn}))}catch(e){console.error(e)}}async function te(e){var a;try{E.value=await o.pms.production.schedule.request({url:"/getProductListByOrderId",method:"POST",data:{order_id:e}}),E.value=(a=E.value)==null?void 0:a.map(l=>({...l,value:l.sku,label:`${l.sku} ${l.name}`}))}catch(l){console.error(l)}}async function le(){try{const e=await o.pms.product.request({url:"/getAllProduct",method:"GET"});X.value=e==null?void 0:e.map(a=>({...a,group_id:a.groupId,value:a.id,sku:a.sku,label:`${a.sku} ${a.name}`}))}catch(e){console.error(e)}}async function re(e){try{const a=await o.pms.material.request({url:"/getMaterialByName",method:"GET",params:{keyword:e}});S.value=a.map(l=>({...l,value:l.id,label:`${l.name} / ${l.code}`}))}catch(a){console.error(a)}}async function oe(e){try{const a=await o.pms.material.request({url:"/getMaterialById",method:"GET",params:{id:e}});S.value=[a].map(l=>({...l,value:l.id,label:`${l.name} / ${l.code}`}))}catch(a){console.error(a)}}async function ne(){try{D.value=await o.pms.supplier.request({url:"/list",method:"POST"}),C.value=D.value.map(e=>({label:e.supplierName,value:e.id}))}catch(e){console.error(e)}}async function se(){try{const e=await o.pms.material.request({url:"/list",method:"POST"});R.value=e.map(a=>({...a,value:a.id,label:`${a.name} / ${a.code}`}))}catch(e){console.error(e)}}be(()=>{ne(),ae(),se(),le()});const x=[{label:"加工段",value:1,name:"-",nameEn:"-",type:"info"},{label:"组装段",value:2,name:"-",nameEn:"-",type:"info"},{label:"老化段",value:3,name:"-",nameEn:"-",type:"info"},{label:"包装段",value:4,name:"-",nameEn:"-",type:"info"},{label:"加工段一",value:5,name:"-",nameEn:"-",type:"info"},{label:"加工段二",value:6,name:"-",nameEn:"-",type:"info"},{label:"加工段三",value:7,name:"-",nameEn:"-",type:"info"},{label:"芯子配件加工段一",value:8,name:"-",nameEn:"-",type:"info"},{label:"芯子配件加工段二",value:9,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段一",value:10,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段二",value:11,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段三",value:12,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段四",value:13,name:"-",nameEn:"-",type:"info"},{label:"芯子配件组装段五",value:14,name:"-",nameEn:"-",type:"info"},{label:"芯子配件包装段",value:15,name:"-",nameEn:"-",type:"info"}],W=M.useUpsert({props:{class:"material-anomaly-form",labelWidth:"120px"},items:[{label:"异常日期",prop:"abnormal_date",required:!0,component:{name:"el-date-picker",props:{type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",clearable:!0,disabledDate:e=>e.getTime()>Date.now()}}},{label:"生产订单",prop:"order_id",required:!0,component:{options:q,name:"el-select",props:{clearable:!0,filterable:!0,onChange(e){var a;(a=W.value)==null||a.setForm("sku",void 0),te(e)}}}},{label:"机型",prop:"sku",required:!0,component:{options:E,name:"el-select",props:{clearable:!0,filterable:!0}}},{label:"物料",prop:"material_id",required:!0,component:{options:S,name:"el-select",props:{clearable:!0,filterable:!0,"filter-method":e=>{e!==""&&re(e)}}}},{label:"供应商",prop:"supplier_id",required:!0,component:{options:C,name:"el-select",props:{clearable:!0,filterable:!0}}},{label:"工段",prop:"workshop_section",required:!0,component:{name:"el-select",props:{filterable:!0},options:x}},{label:"损耗数量",prop:"quantity",required:!0,component:{name:"el-input-number",props:{min:1}}},{label:"描述",prop:"description",required:!0,component:{name:"el-input"}},{label:"责任单位",prop:"accountability_unit",required:!0,component:{name:"el-input"}},{label:"备注",prop:"remark",required:!0,component:{name:"el-input"}}],async onInfo(e,{done:a}){console.log("data",e),oe(e.material_id),a(e)}}),ie=M.useTable({columns:[{label:"ID",prop:"id",width:60},{label:"异常日期",prop:"abnormal_date",width:100,formatter:e=>he(new Date(e.abnormal_date)).format("YYYY-MM-DD")},{label:"订单号",prop:"order_id",width:120,dict:q},{label:"订单数量",prop:"order_quantity",width:120},{label:"产品名",prop:"product_name",width:120},{label:"SKU",prop:"sku",width:120},{label:"颜色",prop:"color",width:120,dict:I},{label:"工序",prop:"workshop_section",width:150,dict:x},{label:"异常描述",prop:"description"},{label:"责任单位",prop:"accountability_unit"},{label:"实际生产数据",align:"center",children:[{label:"物料编码",prop:"material_code",width:150},{label:"物料名称",prop:"material_name",width:180},{label:"供应商",prop:"supplier_id",width:220,dict:C},{label:"损耗数(pcs)",prop:"quantity",width:100},{label:"物料单价（元/pcs)",prop:"unit_price",width:100,formatter(e){return e.unit_price.toFixed(3)}},{label:"损耗金额（元）",prop:"subtotal",width:100,formatter(e){return e.subtotal.toFixed(3)}}]},{label:"备注",prop:"remark"},{type:"op",label:"操作",buttons:["edit","delete"],width:300}]}),g=M.useCrud({service:o.pms.material_anomaly,async onRefresh(e,{next:a,render:l}){const{list:c,pagination:r}=await a(e);l(c,r)}},e=>{e.refresh()}),N=M.useSearch({items:[{label:"异常时间",prop:"dateRange",props:{labelWidth:"100px"},component:{name:"el-date-picker",props:{type:"daterange","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始日期",endPlaceholder:"结束日期",clearable:!0,onChange(e){var a;(a=g.value)==null||a.refresh({dateRange:e})}}}},{label:"sku",prop:"keyword",props:{labelWidth:"120px"},component:{name:"el-input",props:{clearable:!0,onChange(e){var a;(a=g.value)==null||a.refresh({keyword:e,page:1})}}}},{label:"订单号",prop:"order_id",props:{labelWidth:"80px"},component:{name:"el-select",props:{style:"width: 200px",clearable:!0,filterable:!0,onChange(e){var a;(a=g.value)==null||a.refresh({order_id:e})}},options:q}}]});function pe(){const e="物料异常表_模板.xlsx";fetch("/material_abnormal_template.xlsx").then(l=>l.blob()).then(l=>{Z(l,e)}).catch(()=>{v.error({message:"下载模板文件失败"})})}function ce(){const e=j.value;e&&e.click()}function b(e){e&&(e.value="")}async function ue(e){const a=e.target,l=a.files,c={};if(x&&x.length>0&&x.forEach(r=>{c[r.label]=r.value}),l&&l.length>0){s.value=!0;const r=l[0],d=new FileReader;d.onload=k=>{var V,z,K,H;const u=new Uint8Array((V=k.target)==null?void 0:V.result),h=we(u,{type:"array"}),T=h.Sheets[h.SheetNames[0]],w=xe.sheet_to_json(T,{header:1}),m=[void 0,null,"","undefined","null","NaN"],P=["abnormal_date","order_id","sku","workshop_section","description","accountability_unit","material_code","supplier_name","quantity","re_mark"],B=[];if(w&&w.length>0){for(let f=4;f<w.length;f++){const L=w[f];console.log("row",L);const t={};for(let _=0;_<L.length;_++){const Y=P[_];t[Y]=(L[_].toString()||"").trim(),Y==="abnormal_date"&&t.abnormal_date&&(t.abnormal_date=qe(t.abnormal_date).format("YYYY-MM-DD")),Y==="workshop_section"&&(t.workshop_section=c[t.workshop_section]?c[t.workshop_section]:0),!(Y==="order_id"&&(t.order_id=(z=A.value.find(me=>me.sn.toLowerCase().includes(t.order_id.toLowerCase())))==null?void 0:z.id,t.order_id===void 0||t.order_id===0))&&(t.quantity=Number.parseInt(t.quantity))}if(t.supplier_id=(K=D.value.find(_=>_.supplierName===t.supplier_name))==null?void 0:K.id,t.material_id=(H=R.value.find(_=>_.code===t.material_code))==null?void 0:H.id,m.includes(t.abnormal_date)){b(a),s.value=!1;break}if(m.includes(t.workshop_section)||Number.isNaN(t.workshop_section)||t.workshop_section===0){b(a),s.value=!1;break}if(m.includes(t.quantity)||Number.isNaN(t.quantity)||t.quantity===0){b(a),s.value=!1;break}if(m.includes(t.order_id)||Number.isNaN(t.order_id)||t.order_id===0){b(a),s.value=!1;break}if(m.includes(t.supplier_id)||Number.isNaN(t.supplier_id)||t.order_id===0){b(a),s.value=!1;break}if(m.includes(t.sku)||t.sku===""){b(a),s.value=!1;break}B.push(t)}B.length>0?o.pms.material_anomaly.ImportMaterialAnomaly({material_anomaly:B}).then(()=>{var f;(f=g.value)==null||f.refresh(),v.success("导入成功")}).catch(f=>{v.error(f.message||"导入失败")}).finally(()=>{s.value=!1}):(s.value=!1,v.error("导入有效数据为空")),b(a)}},d.readAsArrayBuffer(r)}else s.value=!1,v.error("请选择文件")}function de(){var r,d,k;const e=(r=N.value)==null?void 0:r.getForm("sku"),a=(d=N.value)==null?void 0:d.getForm("order_id"),l=(k=N.value)==null?void 0:k.getForm("dateRange"),c={url:"/export",method:"GET",responseType:"blob",params:{sku:e,order_id:a,dateRange:l}};o.pms.material_anomaly.request(c).then(u=>{var h;Z(u)&&v.success("导出成功"),(h=g.value)==null||h.refresh()}).catch(u=>{v.error(u.message||"导出失败")})}return(e,a)=>{const l=p("cl-refresh-btn"),c=p("cl-add-btn"),r=p("el-button"),d=p("cl-flex1"),k=p("cl-search"),u=p("el-row"),h=p("cl-table"),T=p("cl-pagination"),w=p("cl-upsert"),m=p("cl-crud"),P=ke("permission");return F(),O(m,{ref_key:"Crud",ref:g},{default:y(()=>[n(u,null,{default:y(()=>[n(l),J(o).pms.material_anomaly.permission.add?(F(),O(c,{key:0})):ye("",!0),ve((F(),O(r,{style:{"margin-left":"10px"},type:"info",class:"mb-10px mr-10px",ml:"10px",size:"default",onClick:pe},{default:y(()=>[$(" 下载Excel模板 ")]),_:1})),[[P,J(o).pms.material_anomaly.permission.ImportMaterialAnomaly]]),ge("input",{ref_key:"fileInputRef",ref:j,type:"file",style:{display:"none"},accept:".xlsx, .xls",onChange:ue},null,544),n(r,{size:"default",loading:s.value,type:"warning",class:"mb-10px mr-10px",onClick:ce},{default:y(()=>[$(" Excel导入 ")]),_:1},8,["loading"]),n(r,{type:"success",onClick:de},{default:y(()=>[$(" 导出 ")]),_:1}),n(d),n(k,{ref_key:"Search",ref:N},null,512)]),_:1}),n(u,{style:{"margin-top":"10px"}},{default:y(()=>[n(h,{ref_key:"Table",ref:ie},null,512)]),_:1}),n(u,null,{default:y(()=>[n(d),n(T)]),_:1}),n(w,{ref_key:"Upsert",ref:W},null,512)]),_:1},512)}}});export{Ce as default};
