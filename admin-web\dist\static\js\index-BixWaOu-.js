import{f as i}from"./index-BuqCFB-b.js";import{c as m,b as u,r as c,z as d,O as f,h as l,i as n,m as p}from"./.pnpm-Kv7TmmH8.js";const C=m({name:"ClEditor",props:{name:{type:String,required:!0}},setup(e,{slots:o,expose:s}){const t=u(),r=c({});return d(t,a=>{a&&Object.assign(r,a)}),s(r),()=>i(e.name)?f(n(e.name),{...e,ref:t},o):l(n("el-input"),p({type:"textarea",rows:4,placeholder:"请输入"},e),null)}});export{C as default};
