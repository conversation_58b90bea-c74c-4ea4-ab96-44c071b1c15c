import be from"./index-BQv5-z8i.js";import{_ as ge}from"./material-table-columns.vue_vue_type_script_setup_true_lang-hBwyRg0n.js";import{_ as G}from"./select-dict.vue_vue_type_script_setup_true_name_select-dict_lang-rWvH4Sy8.js";import{_ as xe}from"./material-selector.vue_vue_type_script_name_product-selector_setup_true_lang-B31T1lMk.js";import{c as X,b as m,ae as ke,A as J,E as f,U as P,T as $,f as U,y as s,h as r,w as i,q as C,B as D,i as h,t as b,j as _,v as Ie,F as Ve,s as qe,W as Me,o as w,af as Ce,ag as Oe}from"./.pnpm-Kv7TmmH8.js";import"./index-BuqCFB-b.js";import{a as Se}from"./index-BAHxID_w.js";import{_ as Qe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./material-CnT3-AWx.js";import"./index-CBanFtSc.js";const V=O=>(Ce("data-v-28a8299a"),O=O(),Oe(),O),Be={class:"cl-crud inbound-create"},$e={class:"order-create-body"},Ue=V(()=>s("div",{class:"inbound-create-header"}," 基础信息 ",-1)),De={key:0,flex:"~"},Ee={"text-green-6":""},ze={pl:"50px"},Ne=V(()=>s("span",{pr:"12px"},"工单号",-1)),Te={"text-green-6":""},Le={pl:"50px"},Pe=V(()=>s("span",{pr:"12px"},"产品名称",-1)),Fe={"text-green-6":""},Ke={pl:"50px"},We=V(()=>s("span",{pr:"12px"},"计划生产数量",-1)),Ae={"text-green-6":""},Re={key:1,"text-red":""},je=V(()=>s("div",{class:"inbound-create-header"},b("物料入库信息"),-1)),He={flex:"~ justify-between items-center"},Ge={flex:"~ "},Je={style:{display:"flex","align-items":"center"}},Xe=V(()=>s("span",{style:{color:"var(--el-color-danger)"}},"*",-1)),Ye={class:"dialog-footer"},Ze=X({name:"undefined"}),ea=X({...Ze,setup(O){const g=m(!1),{service:F,router:E}=Se(),S=F.pms.outsource.inbound,Q=m(!1),x=m({}),c=m({id:null,type:null,orderId:0,remark:"",materials:[],productId:0,workOrder:"",workOrderId:0}),z=m([]),v=m([]),Y=m(!1),q=m(!1),M=m(!1),o=m([]),k=m(""),Z=m({buttons:["slot-btn-outbound"],width:160}),K=m(null),{height:ee}=ke(),I=m(null),N=m(!1),T=m(),W=m(!1),A="/pms/outsource/inbound/index",p=m(new Map);J(()=>{var t;const a=(t=o.value)==null?void 0:t.map(e=>e.uniqueKey);v.value=v.value.map(e=>(a.includes(e.uniqueKey)?e.disabled=!0:e.disabled=!1,e)),o.value.map(e=>{p.value.has(e.materialId)?e.receivedQuantity=p.value.get(e.materialId):e.receivedQuantity=0})}),J(()=>{const a=E.currentRoute.value.query.id;a&&(W.value=!0,c.value.id=Number.parseInt(a),ae(a))});async function R(){await S.getOutsourceByWorkOrder({id:c.value.workOrderId}).then(a=>{a&&a.length>0&&(p.value.clear(),a.forEach(t=>{const{materialId:e,quantity:l}=t;p.value.has(e)?p.value.set(e,p.value.get(e)+l):p.value.set(e,l)}))}).catch(a=>{f.error({message:a.message})})}function ae(a){S.info({id:a}).then(async t=>{c.value.remark=(t==null?void 0:t.remark)||"";const e=(t==null?void 0:t.orderId)||0;c.value.orderId=e;const l=(t==null?void 0:t.products)||[];await te(t),o.value=l==null?void 0:l.map(d=>{const L=v.value.find(B=>B.id===d.materialId||B.materialId===d.materialId);return{...d,...L,id:d.materialId,index:new Date().getTime()+Math.floor(Math.random()*1e3),uniqueKey:d.contractId,quantity:d.quantity}}),await R(),o.value.map(d=>{p.value.has(d.materialId)?d.receivedQuantity=p.value.get(d.materialId):d.receivedQuantity=0})}).catch(t=>{f.error({message:t.message||"获取入库单信息失败"})})}async function te(a){try{le({extras:a.extras,workOrder:a.workOrder,products:a.products})}catch(t){f.error({message:t.message||"获取生产订单信息失败"})}}function le(a){var t;a&&(x.value=P(a.workOrder),k.value=a.sn||x.value.productionScheduleSn,c.value.workOrderId=a.workOrder.id,o.value.length>0&&$.confirm("当前物料列表中存在已选数据，如果确定，让先清除列表中的数据，是否继续").then(e=>{e&&(o.value=[])}).catch(()=>{}),o.value=[],v.value=(t=a.extras)==null?void 0:t.map(e=>({...e,value:e.id,materialId:e.id,uniqueKey:`${e.id}`,label:`${e.name}`,index:new Date().getTime()+Math.floor(Math.random()*1e3),disabled:o.value.some(l=>l.id===e.id)})))}function oe(a){o.value=o.value.filter(t=>t.index!==a)}function ne(){F.dict.info.data({}).then(a=>{try{a.warehouse_name.forEach(t=>{t.name==="委外仓"&&(I.value=t.id)})}catch(t){console.error(t,"handleDictMap(Error)")}})}ne();async function re(){T.value&&await T.value.validate(a=>{if(a){if(Q.value=!0,o.value.length===0){f.error({message:"请添加物料"});return}if(o.value=o.value.filter(e=>e.id||e.materialId),o.value.find(e=>e.quantity===null||e.quantity===void 0||e.quantity===0||e.quantity<0)){f.error({message:"请填写正确的物料数量"});return}o.value.forEach(e=>{if(!e.warehouseId)throw f.error({message:"请选择仓库"}),new Error("请选择仓库")}),Q.value=!1,c.value.materials=o.value.map(e=>({materiaLdetail:e,materialId:c.value.type===1?e.materialId:e.id,quantity:e.quantity,warehouseId:e.warehouseId,inbound_outbound_key:e.inbound_outbound_key})),g.value=!0,W.value&&c.value.id&&c.value.id>0?S.update(c.value).then(()=>{E.push(`${A}?tab=0`),f.success({message:"保存成功",onClose:()=>{g.value=!1}})}).catch(e=>{f.error({message:e.message}),g.value=!1}):(delete c.value.id,S.add(c.value).then(e=>{const l=e==null?void 0:e.id;E.push(`${A}?tab=0&expand=${l}`),f.success({message:"保存成功",onClose:()=>{g.value=!1}})}).catch(e=>{f.error({message:e.message}),g.value=!1}))}})}async function ue(a){x.value=P(a),await de(a),q.value=!1}function ie(a=10){for(let t=0;t<a;t++)j()}function j(){const a={index:new Date().getTime()+Math.floor(Math.random()*1e3),id:null,name:"",model:"",inventory:0,unit:"",code:"",expectedInbound:0,quantity:null,coverColor:"",size:"",material:"",process:"",receivedQuantity:0,orderQuantity:0,returnOrderQuantity:0,restockingQty:0,transfer:0,uniqueKey:"",warehouseId:0};a.warehouseId=I,o.value.push(a)}async function de(a,t=!0){var e;if(c.value.orderId=a.productionScheduleId,c.value.workOrderId=a.id,c.value.workOrder=a.workOrderNo,k.value=a.sn,await R(),!a.extras){f.error({message:"该生产订单没有物料"});return}if(o.value.length>0)try{if(!await $.confirm("当前物料列表中存在已选数据，如果确定，让先清除列表中的数据，是否继续"))return}catch{return}if(v.value=[],o.value=[],v.value=(e=a.extras)==null?void 0:e.map(l=>({...l,value:l.id,materialId:l.id,uniqueKey:`${l.id}`,label:`${l.name}`,index:new Date().getTime()+Math.floor(Math.random()*1e3),disabled:o.value.some(d=>d.id===l.id)})),t){try{await $.confirm("是否需要自动填充该订单的全部物料","提示",{confirmButtonText:"自动填充",cancelButtonText:"手动输入",type:"warning",showClose:!1}),N.value=!0}catch{ie(),N.value=!1}N.value&&(o.value=P(v.value),o.value=o.value.filter(l=>l.outboundQuantity>0),o.value.length>0&&o.value.forEach(l=>{l.warehouseId||(l.warehouseId=I)})),o.value.map(l=>{p.value.has(l.materialId)?l.receivedQuantity=p.value.get(l.materialId):l.receivedQuantity=0})}}function se({row:a}){return""}function ce(){$.confirm("确定清空已选择的物料列表吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{o.value=[],K.value.resetData()}).catch(()=>{})}function me(){M.value=!1,z.value.forEach(a=>{if(!o.value.find(e=>e.id===a.id)){v.value.push({...a,value:a.id,disabled:!1,label:`${a.name}`});const e={...a,index:new Date().getTime()+Math.floor(Math.random()*1e3),orderQuantity:a.quantity,quantity:null,warehouseId:0};e.warehouseId=I,o.value.push(e)}})}function ve(){q.value=!0}function fe(a){const t=v.value.find(l=>l.value===a);if(!t)return;const e=o.value.findIndex(l=>l.id===a);v.value=v.value.map(l=>(l.value!==a&&(l.disabled=!1),l)),e!==-1?o.value[e]={...t,index:o.value[e].index,id:t.value,warehouseId:t.warehouseId||I}:o.value.push({...t,index:new Date().getTime()+Math.floor(Math.random()*1e3),id:t.value,quantity:null,warehouseId:t.warehouseId||I})}function pe(a){let t=!1;const e=a.calcBomQuantity-a.receivedQuantity;a.quantity>e&&(f.error({message:"入库数量不能大于剩余可入库数量"}),t=!0),t&&(a.quantity=null,Q.value=!0)}return(a,t)=>{const e=h("el-form-item"),l=h("el-input"),d=h("el-button"),L=h("el-option"),B=h("el-select"),y=h("el-table-column"),he=h("el-input-number"),_e=h("el-table"),ye=h("el-row"),we=h("el-form"),H=h("el-dialog");return w(),U("div",null,[s("div",Be,[r(we,{ref_key:"inboundForm",ref:T,model:c.value,"label-width":"90px",size:"large","status-icon":""},{default:i(()=>[s("div",$e,[Ue,r(e,{label:"委外入库",prop:"type"},{default:i(()=>[r(e,{pb:"20px",label:"生产单号",prop:"orderId"},{default:i(()=>[k.value?(w(),U("div",De,[s("span",Ee,b(k.value),1),s("div",ze,[Ne,s("span",Te,b(x.value.workOrderNo),1)]),s("div",Le,[Pe,s("span",Fe,b(x.value.productName),1)]),s("div",Ke,[We,s("span",Ae,b(x.value.quantity),1)])])):(w(),U("div",Re," 请先选择生产订单 "))]),_:1})]),_:1}),r(e,{label:"备注",prop:"remark",style:{width:"550px"},pb:"7px"},{default:i(()=>[r(l,{modelValue:c.value.remark,"onUpdate:modelValue":t[0]||(t[0]=n=>c.value.remark=n),type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),je,s("div",He,[s("div",Ge,[r(d,{type:"primary",class:"mb-10px mr-10px",size:"default",onClick:ve},{default:i(()=>[_(" 选择工单 ")]),_:1}),r(d,{type:"danger",disabled:o.value.length===0,class:"mb-10px mr-10px",size:"default",onClick:ce},{default:i(()=>[_(" 清空列表 ")]),_:1},8,["disabled"])])]),r(_e,{data:o.value,style:{width:"100%"},border:"",size:"small","row-class-name":se,"max-height":Math.max(Ie(ee)-680,300)},{default:i(()=>[r(y,{prop:"code",label:"选择物料",width:"450",align:"center"},{default:i(n=>[s("div",Je,[r(B,{modelValue:n.row.id,"onUpdate:modelValue":u=>n.row.id=u,filterable:"","reserve-keyword":"",loading:Y.value,placeholder:"请选择物料",style:{width:"400px"},size:"small",onChange:fe},{default:i(()=>[(w(!0),U(Ve,null,qe(v.value,u=>(w(),C(L,{key:u.value,size:"small",disabled:u.disabled,label:`${u.code} / ${u.name} / ${u.model} ${u!=null&&u.po?` / ${u.po}`:""} ${u!=null&&u.supplierName?` / ${u.supplierName}`:""}`,value:u.value},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])])]),_:1}),r(y,{prop:"quantity",label:"*数量",align:"center",width:"150"},{header:i(()=>[Xe,_(" 数量 ")]),default:i(n=>[s("div",{class:Me(Q.value&&!(n.row.quantity>0)?"quantity-input-error":"")},[r(he,{modelValue:n.row.quantity,"onUpdate:modelValue":u=>n.row.quantity=u,min:void 0,placeholder:"请输入物料数量",size:"small",precision:4,onChange:u=>pe(n.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])],2)]),_:1}),r(y,{prop:"inbound_outbound_key",label:"关键字",align:"center",width:"120"},{default:i(n=>[r(G,{modelValue:n.row.inbound_outbound_key,"onUpdate:modelValue":u=>n.row.inbound_outbound_key=u,code:"inbound_outbound_key",size:"small",width:"100px"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),r(y,{prop:"remark",label:"*数量",align:"center",width:"150"},{header:i(()=>[_(" 备注 ")]),default:i(n=>[s("div",null,[r(l,{modelValue:n.row.remark,"onUpdate:modelValue":u=>n.row.remark=u,size:"small",placeholder:"请输入备注"},null,8,["modelValue","onUpdate:modelValue"])])]),_:1}),r(y,{prop:"quantity",label:"仓位",align:"center",width:"120"},{default:i(n=>[r(G,{modelValue:n.row.warehouseId,"onUpdate:modelValue":u=>n.row.warehouseId=u,disabled:!0,code:"warehouse_name",size:"small",width:"100px"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),r(y,{prop:"",label:"剩余数量",align:"center",width:"120"},{default:i(({row:n})=>[_(b(isNaN(n.calcBomQuantity-n.receivedQuantity)?0:n.calcBomQuantity-n.receivedQuantity),1)]),_:1}),r(y,{prop:"receivedQuantity",label:"已收数量",width:"100",align:"center"},{default:i(n=>{var u;return[_(b(((u=n.row)==null?void 0:u.receivedQuantity)||0),1)]}),_:1}),r(y,{prop:"calcBomQuantity",label:"Bom用量",width:"80",align:"center","show-overflow-tooltip":""}),r(y,{prop:"unit",label:"单位",align:"center",width:"80","show-overflow-tooltip":""}),r(ge,{"auto-width":"","show-code":!1}),r(y,{label:"操作",width:"120",align:"center"},{default:i(n=>[r(d,{type:"danger",size:"small",onClick:u=>oe(n.row.index)},{default:i(()=>[_(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","max-height"]),k.value?(w(),C(d,{key:0,style:{width:"100%"},size:"small",class:"btn-material-add",onClick:j},{default:i(()=>[_(" + 添加物料 ")]),_:1})):D("",!0),k.value?(w(),C(ye,{key:1,flex:"~ 1 items-center justify-end"},{default:i(()=>[r(d,{type:"success",style:{"margin-top":"20px"},loading:g.value,onClick:re},{default:i(()=>[_(" 保存为草稿 ")]),_:1},8,["loading"])]),_:1})):D("",!0)])]),_:1},8,["model"])]),r(H,{modelValue:q.value,"onUpdate:modelValue":t[1]||(t[1]=n=>q.value=n),title:"选择工单",width:"80%",height:"600"},{default:i(()=>[q.value?(w(),C(be,{key:0,"table-op":Z.value,onSelected:ue},null,8,["table-op"])):D("",!0)]),_:1},8,["modelValue"]),r(H,{modelValue:M.value,"onUpdate:modelValue":t[4]||(t[4]=n=>M.value=n),title:"选择物料",width:"80%"},{footer:i(()=>[s("span",Ye,[r(d,{onClick:t[3]||(t[3]=n=>M.value=!1)},{default:i(()=>[_("取消")]),_:1}),r(d,{type:"primary",onClick:me},{default:i(()=>[_(" 确认选择 ")]),_:1})])]),default:i(()=>[M.value?(w(),C(xe,{key:0,ref_key:"materialSelector",ref:K,modelValue:z.value,"onUpdate:modelValue":t[2]||(t[2]=n=>z.value=n),"has-stock":!1},null,8,["modelValue"])):D("",!0)]),_:1},8,["modelValue"])])}}}),ca=Qe(ea,[["__scopeId","data-v-28a8299a"]]);export{ca as default};
