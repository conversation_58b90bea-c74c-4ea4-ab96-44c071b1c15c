import{h as v}from"./index-BuqCFB-b.js";import{a as _}from"./index-BAHxID_w.js";import{c as u,b as n,z as k,S as V,i as y,f as C,o as b,h as g,E as x}from"./.pnpm-Kv7TmmH8.js";import{_ as B}from"./_plugin-vue_export-helper-DlAUqK2U.js";const S={class:"cl-dept-select"},w=u({name:"cl-dept-select"}),E=u({...w,props:{modelValue:[Array,Number,String],multiple:Bo<PERSON>an,checkStrictly:{type:Boolean,default:!0}},emits:["update:modelValue","change"],setup(t,{emit:r}){const a=t,s=r,{service:i}=_(),o=n(),c=n();function m(e){a.multiple||s("update:modelValue",e)}function d(e,{checkedKeys:l}){a.multiple&&s("update:modelValue",l)}function p(){i.base.sys.department.list().then(e=>{c.value=v(e)}).catch(e=>{c.value=[],x.error(e.message)})}return k(()=>a.modelValue,e=>{o.value=e},{immediate:!0}),V(()=>{p()}),(e,l)=>{const h=y("el-tree-select");return b(),C("div",S,[g(h,{modelValue:o.value,"onUpdate:modelValue":l[0]||(l[0]=f=>o.value=f),"node-key":"id",data:c.value,props:{label:"name",value:"id",children:"children"},multiple:t.multiple,"check-strictly":t.checkStrictly,"show-checkbox":t.multiple,"default-expand-all":"",onChange:m,onCheck:d},null,8,["modelValue","data","multiple","check-strictly","show-checkbox"])])}}}),I=B(E,[["__scopeId","data-v-f19366ee"]]);export{I as default};
