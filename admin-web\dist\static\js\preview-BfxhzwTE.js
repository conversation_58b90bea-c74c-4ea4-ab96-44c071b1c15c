import{c as v,_ as k,b as m,i as u,f as w,o as S,h as a,w as s,m as j,v as E,j as f,$ as N,P as O,a0 as h,E as B}from"./.pnpm-Kv7TmmH8.js";import"./index-BuqCFB-b.js";import{a as P}from"./index-BAHxID_w.js";const T=v({name:"cl-editor-preview"}),J=v({...T,props:{title:{type:String,default:"文本预览"},name:{type:String,required:!0},props:Object},setup(i,{expose:_}){const c=i,{refs:g,setRefs:C}=P(),{copy:b}=k(),l=m(!1),t=m("");async function x(o){var e,n;N(o)&&(t.value=o),O(o)&&(t.value=JSON.stringify(o,null,4)),l.value=!0,await h(),c.name=="monaco"&&((n=(e=g.editor)==null?void 0:e.formatCode)==null||n.call(e))}function p(){l.value=!1}function y(){b(t.value),B.success("复制成功")}return _({open:x,close:p}),(o,e)=>{const n=u("cl-editor"),d=u("el-button"),V=u("cl-dialog");return S(),w("div",null,[a(V,{modelValue:l.value,"onUpdate:modelValue":e[1]||(e[1]=r=>l.value=r),width:"1000px",title:i.title,"append-to-body":""},{footer:s(()=>[a(d,{onClick:p},{default:s(()=>[f(" 关闭 ")]),_:1}),a(d,{type:"success",onClick:y},{default:s(()=>[f(" 复制 ")]),_:1})]),default:s(()=>[a(n,j({ref:E(C)("editor")},c.props,{modelValue:t.value,"onUpdate:modelValue":e[0]||(e[0]=r=>t.value=r),name:`cl-editor-${i.name}`,height:600}),null,16,["modelValue","name"])]),_:1},8,["modelValue","title"])])}}});export{J as default};
