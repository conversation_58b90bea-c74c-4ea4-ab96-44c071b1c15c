import{c as E,b as z,r as G,q as $,w as i,h as o,i as l,a0 as K,o as r,aT as L,f as m,y as p,G as T,F as H,s as O,H as J,v as c,al as P,W as Q,E as b,B as X,t as S,j as U,aU as Z,Y as W,aV as ee,aW as se,aK as te,af as ne,ag as oe,T as R}from"./.pnpm-Kv7TmmH8.js";import{i as q,u as ae}from"./index-BuqCFB-b.js";import{a as j}from"./index-BAHxID_w.js";import{_ as le}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ie=E({name:"undefined"}),ce=E({...ie,setup(g,{expose:n}){const{service:I}=j(),k=z(!1),M=z(""),y=G({status:[{label:"成功",value:1,type:"success"},{label:"失败",value:0,type:"danger"}]}),V=q.useTable({autoHeight:!1,columns:[{label:"#",type:"index"},{label:"描述",prop:"detail",showOverflowTooltip:!0,minWidth:200},{label:"执行状态",prop:"status",minWidth:120,dict:y.status},{label:"执行时间",prop:"createTime",minWidth:160}]}),u=q.useCrud({service:I.task.info,dict:{api:{page:"log"}}});function B(h){k.value=!0,M.value=`日志列表（${h.name}）`,K(()=>{var _;(_=u.value)==null||_.refresh({id:h.id,page:1})})}function D(){k.value=!1}return n({open:B,close:D}),(h,_)=>{const x=l("cl-refresh-btn"),N=l("cl-select"),Y=l("cl-filter"),s=l("cl-row"),t=l("cl-table"),e=l("cl-flex1"),d=l("cl-pagination"),C=l("cl-crud"),w=l("cl-dialog");return r(),$(w,{modelValue:k.value,"onUpdate:modelValue":_[0]||(_[0]=f=>k.value=f),title:M.value,width:"1000px"},{default:i(()=>[o(C,{ref_key:"Crud",ref:u,padding:"0"},{default:i(()=>[o(s,null,{default:i(()=>[o(x),o(Y,{label:"状态"},{default:i(()=>[o(N,{options:y.status,prop:"status"},null,8,["options"])]),_:1})]),_:1}),o(s,null,{default:i(()=>[o(t,{ref_key:"Table",ref:V},null,512)]),_:1}),o(s,null,{default:i(()=>[o(e),o(d)]),_:1})]),_:1},512)]),_:1},8,["modelValue","title"])}}}),F=g=>(ne("data-v-279851e2"),g=g(),oe(),g),re={class:"list"},pe=["onClick","onContextmenu"],ue={class:"name"},de={class:"row"},_e=F(()=>p("span",null,"执行服务",-1)),fe={class:"row"},me=F(()=>p("span",null,"定时规则",-1)),ke={key:0,class:"row"},he=F(()=>p("span",null,"下次执行时间",-1)),ve={class:"status"},be=F(()=>p("p",null,"添加计划任务",-1)),ge=E({name:"task-list"}),ye=E({...ge,setup(g){const{service:n,refs:I,setRefs:k}=j(),{browser:M}=ae(),y=q.useForm(),V=z([]);u();function u(){n.task.info.page({size:100,page:1}).then(s=>{V.value=s.list.map(t=>(t.every&&(t._every=Number.parseInt(String(t.every/1e3))),t))})}function B(s){R.confirm(`此操作将启用任务（${s.name}），是否继续？`,"提示",{type:"warning"}).then(()=>{n.task.info.start({id:s.id,type:s.type}).then(()=>{u()}).catch(t=>{b.error(t.message)})}).catch(()=>null)}function D(s){R.confirm(`此操作将停用任务（${s.name}），是否继续？`,"提示",{type:"warning"}).then(()=>{n.task.info.stop({id:s.id}).then(()=>{u()}).catch(t=>{b.error(t.message)})}).catch(()=>null)}function h(s){R.confirm(`此操作将删除任务（${s.name}），是否继续？`,"提示",{type:"warning"}).then(()=>{n.task.info.delete({ids:[s.id]}).then(()=>{u()}).catch(t=>{b.error(t.message)})}).catch(()=>null)}function _(s){I.log.open(s)}async function x(s){var t;if(s&&!n.task.info._permission.update)return!1;(t=y.value)==null||t.open({title:"编辑计划任务",width:"600px",props:{labelWidth:"80px"},items:[{label:"名称",prop:"name",component:{name:"el-input",props:{placeholder:"请输入名称"}},required:!0},{label:"类型",prop:"taskType",value:0,component:{name:"el-radio-group",options:[{label:"cron",value:0},{label:"时间间隔",value:1}]},required:!0},{label:"cron",prop:"cron",hidden:({scope:e})=>e.taskType===1,component:{name:"el-input",props:{placeholder:"* * * * * *"}},required:!0},{label:"间隔(秒)",prop:"every",hidden:({scope:e})=>e.taskType===0,hook:{bind(e){return e/1e3},submit(e){return e*1e3}},component:{name:"el-input-number",props:{min:1,max:1e8}},required:!0},{label:"service",prop:"service",component:{name:"el-input",props:{placeholder:"taskDemoService.test([1, 2])"}}},{label:"开始时间",prop:"startDate",hidden:({scope:e})=>e.taskType===1,component:{name:"el-date-picker",props:{type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"}}},{label:"备注",prop:"remark",component:{name:"el-input",props:{type:"textarea",rows:3}}}],form:{...s},on:{submit:(e,{close:d,done:C})=>{e.limit||(e.limit=null),n.task.info[s!=null&&s.id?"update":"add"](e).then(()=>{u(),b.success("保存成功"),d()}).catch(w=>{b.error(w.message),C()})}}})}function N(s){n.task.info.once({id:s.id}).then(()=>{u()}).catch(t=>{b.error(t.message)})}function Y(s,t){q.ContextMenu.open(s,{list:[t.status?{label:"暂停",hidden:!n.task.info._permission.stop,callback(e){D(t),e()}}:{label:"开始",hidden:!n.task.info._permission.start,callback(e){B(t),e()}},{label:"立即执行",hidden:!n.task.info._permission.once,callback(e){N(t),e()}},{label:"编辑",hidden:!(n.task.info._permission.update&&n.task.info._permission.info),callback(e){x(t),e()}},{label:"删除",hidden:!n.task.info._permission.delete,callback(e){h(t),e()}},{label:"查看日志",hidden:!n.task.info._permission.log,callback(e){_(t),e()}}]})}return L(()=>{u()}),(s,t)=>{const e=l("el-tag"),d=l("el-icon"),C=l("cl-flex1"),w=l("cl-form"),f=J("permission");return r(),m("div",{class:Q(["task-list",{"is-mini":c(M).isMini}])},[p("div",re,[(r(!0),m(H,null,O(V.value,(a,A)=>(r(),m("div",{key:A,class:"item",onClick:v=>x(a),onContextmenu:v=>{Y(v,a)}},[p("p",ue,S(a.name),1),p("p",de,[_e,p("span",null,S(a.service),1)]),p("p",fe,[me,p("span",null,S(a.taskType===1?`间隔${a._every}秒执行`:a.cron),1)]),a.nextRunTime?(r(),m("p",ke,[he,p("span",null,S(a.nextRunTime),1)])):X("",!0),p("div",ve,[a.status?(r(),m(H,{key:0},[o(e,{"disable-transitions":"",effect:"dark",type:"success"},{default:i(()=>[U(" 进行中 ")]),_:1}),T((r(),$(d,{class:"pause",onClick:W(v=>D(a),["stop"])},{default:i(()=>[o(c(Z))]),_:2},1032,["onClick"])),[[f,c(n).task.info.permission.stop]])],64)):(r(),m(H,{key:1},[o(e,{"disable-transitions":"",effect:"dark",type:"danger"},{default:i(()=>[U(" 已停止 ")]),_:1}),T((r(),$(d,{class:"play",onClick:W(v=>B(a),["stop"])},{default:i(()=>[o(c(ee))]),_:2},1032,["onClick"])),[[f,c(n).task.info.permission.start]])],64)),o(C),T((r(),$(d,{class:"log",onClick:W(v=>_(a),["stop"])},{default:i(()=>[o(c(se))]),_:2},1032,["onClick"])),[[f,c(n).task.info.permission.log]]),T((r(),$(d,{class:"delete",onClick:W(v=>h(a),["stop"])},{default:i(()=>[o(c(te))]),_:2},1032,["onClick"])),[[f,c(n).task.info.permission.delete]])])],40,pe))),128)),T((r(),m("div",{class:"item is-add",onClick:t[0]||(t[0]=a=>x())},[o(d,null,{default:i(()=>[o(c(P))]),_:1}),be])),[[f,c(n).task.info.permission.add]])]),o(w,{ref_key:"Form",ref:y},null,512),o(ce,{ref:c(k)("log")},null,512)],2)}}}),$e=le(ye,[["__scopeId","data-v-279851e2"]]);export{$e as default};
