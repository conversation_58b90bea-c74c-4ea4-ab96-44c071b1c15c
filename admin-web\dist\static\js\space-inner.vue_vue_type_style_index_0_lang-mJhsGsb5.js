import{ax as ce,c as D,b as q,e as $,z as ee,f as c,o as a,y as f,_ as re,i as b,B as F,t as C,F as M,q as V,w as _,v as n,V as ue,h as d,aU as pe,Y as z,aV as de,aJ as fe,aK as ve,W as te,E as W,r as _e,G as X,j as L,H as Z,s as me,bd as ge,T as ye,p as he}from"./.pnpm-Kv7TmmH8.js";import{o as ke,i as be}from"./index-BuqCFB-b.js";import{a as ae}from"./index-BAHxID_w.js";import{u as xe}from"./hook-B9KKc1yL.js";import{c as we,f as Ce,_ as $e}from"./viewer.vue_vue_type_script_setup_true_name_item-viewer_lang-Y2-MQXPQ.js";import{_ as se}from"./_plugin-vue_export-helper-DlAUqK2U.js";function le(){return{space:ce("upload-space")}}const Ve={class:"item-video"},Ee=["src"],De=D({name:"item-video"}),Ie=D({...De,props:{data:Object,list:Array},setup(m,{expose:I}){const S=m,{space:h}=le(),g=q(),v=$(()=>S.data||{}),B=$(()=>v.value.progress===void 0||v.value.progress===100);function k(){var r;h.list.value.forEach(u=>{u.isPlay=v.value.id==u.id}),(r=g.value)==null||r.play()}function t(){var u;const r=h.list.value.find(s=>s.id==v.value.id);r&&(r.isPlay=!1),(u=g.value)==null||u.pause()}return ee(()=>v.value.isPlay,r=>{r?k():t()}),I({play:k,pause:t,loaded:B}),(r,u)=>(a(),c("div",Ve,[f("video",{ref_key:"Video",ref:g,src:v.value.url},null,8,Ee)]))}}),Se=se(Ie,[["__scopeId","data-v-356dc887"]]),Be={class:"item-file__wrap"},je={key:0,class:"item-file__error"},ze={class:"image-error"},Me={key:2,class:"item-file__name"},Ne={class:"item-file__actions"},Pe={class:"item-file__progress-bar"},Ge={class:"item-file__progress-value"},Oe={key:2,class:"item-file__index"},Re=D({name:"item-file"}),Te=D({...Re,props:{data:Object,list:Array},emits:["select","remove","confirm"],setup(m,{emit:I}){const S=m,h=I,{refs:g,setRefs:v}=ae(),{copy:B}=re(),{space:k}=le(),t=$(()=>S.data||{}),r=$(()=>k.selection.value.findIndex(y=>y.id===t.value.id)),u=$(()=>r.value>=0),s=$(()=>t.value.preload||t.value.url),p=$(()=>we(t.value.type||""));function x(){h("select",t.value)}function A(){}function N(){h("remove",t.value)}function P(){k.preview(t.value)}function E(y){be.ContextMenu.open(y,{hover:{target:"item-file__wrap"},list:[{label:"预览",callback(l){P(),l()}},{label:"复制链接",callback(l){t.value.url&&(B(t.value.url),W.success("复制成功")),l()}},{label:u.value?"取消选中":"选中",callback(l){x(),l()}},{label:"删除",callback(l){N(),l()}}]})}return(y,l)=>{var G,O,R;const U=b("el-image"),w=b("el-icon"),H=b("el-progress");return a(),c("div",Be,[f("div",{class:te(["item-file",[`is-${t.value.type}`]]),onClick:l[2]||(l[2]=e=>x()),onDblclick:l[3]||(l[3]=e=>void 0),onContextmenu:z(E,["stop","prevent"])},[t.value.error?(a(),c("div",je," 上传失败："+C(t.value.error),1)):(a(),c(M,{key:1},[t.value.type==="image"?(a(),V(U,{key:0,fit:"contain",src:s.value,lazy:""},{error:_(()=>[f("div",ze,[f("span",null,C(s.value),1)])]),_:1},8,["src"])):t.value.type==="video"?(a(),V(Se,{key:1,ref:n(v)("video"),data:t.value,list:m.list},null,8,["data","list"])):(a(),c("span",Me,C(n(Ce)(s.value))+"."+C(n(ke)(s.value)),1)),f("span",{class:"item-file__type",style:ue({backgroundColor:(G=p.value)==null?void 0:G.color})},C((O=p.value)==null?void 0:O.label),5),f("div",Ne,[t.value.type=="video"?(a(),c(M,{key:0},[(R=n(g).video)!=null&&R.loaded?(a(),c(M,{key:0},[t.value.isPlay?(a(),V(w,{key:0,onClick:l[0]||(l[0]=z(e=>n(g).video.pause(),["stop"]))},{default:_(()=>[d(n(pe))]),_:1})):(a(),V(w,{key:1,onClick:l[1]||(l[1]=z(e=>{var o;return(o=n(g).video)==null?void 0:o.play()},["stop"]))},{default:_(()=>[d(n(de))]),_:1}))],64)):F("",!0)],64)):(a(),V(w,{key:1,onClick:z(P,["stop"])},{default:_(()=>[d(n(fe))]),_:1})),d(w,{onClick:z(N,["stop"])},{default:_(()=>[d(n(ve))]),_:1})]),t.value.progress>0&&t.value.progress<100&&!t.value.url?(a(),c(M,{key:3},[f("div",Pe,[d(H,{percentage:t.value.progress,"show-text":!1},null,8,["percentage"])]),f("span",Ge,C(t.value.progress)+"%",1)],64)):F("",!0)],64)),u.value?(a(),c("div",Oe,[f("span",null,C(r.value+1),1)])):F("",!0)],34)])}}}),qe=se(Te,[["__scopeId","data-v-f17e19f9"]]),Fe={class:"cl-upload-space-inner__right"},Ae={class:"cl-upload-space-inner__header"},Ue={style:{margin:"0px 10px"}},He={"infinite-scroll-immediate":!1},Je={key:1,class:"empty"},Ke=f("p",null,"将文件拖到此处，或点击按钮上传",-1),Le=D({name:"cl-upload-space-inner"}),tt=D({...Le,props:{limit:{type:Number,default:99},accept:String,selectable:Boolean},emits:["selectionChange","confirm"],setup(m,{expose:I,emit:S}){const h=m,g=S,{service:v,browser:B,refs:k,setRefs:t}=ae(),{ViewGroup:r}=xe({label:"分类",title:"文件列表",service:v.space.type,onEdit(){return{width:"400px",props:{labelPosition:"top"},dialog:{controls:["close"]},items:[{label:"名称",prop:"name",value:"",required:!0,component:{name:"el-input",props:{maxlength:20,clearable:!0}}}]}},onSelect(e){y({classifyId:e.id,page:1})}}),u=q(!1),s=q([]),p=q([]),x=_e({page:1,size:50,total:0});function A(){s.value=[]}function N(e){}function P(e){p.value.unshift(e)}const E={page:1};async function y(e){var o;A(),Object.assign(E,{type:((o=h.accept)==null?void 0:o.split("/")[0].replace("*",""))||void 0,...x,...e}),E.page===1&&(u.value=!0),await v.space.info.page(E).then(i=>{Object.assign(x,i.pagination),E.page===1&&(p.value=[]),p.value.push(...i.list)}),u.value=!1}function l(e){const o=s.value.findIndex(i=>i.id===e.id);o>=0?s.value.splice(o,1):h.limit===1?s.value=[e]:s.value.length<h.limit&&s.value.push(e)}function U(e){g("confirm",[e])}function w(e){const o=e?[e.id]:s.value.map(i=>i.id);ye.confirm("此操作将删除文件, 是否继续?","提示",{type:"warning"}).then(()=>{W.success("删除成功"),o.forEach(i=>{[p.value,s.value].forEach(T=>{const J=T.findIndex(K=>K.id===i);T.splice(J,1)})}),v.space.info.delete({ids:o}).catch(i=>{W.error(i.message)})}).catch(()=>null)}function H(e){k.viewer.open(e,p.value)}ee(s,e=>{g("selectionChange",e)},{deep:!0});function G(){p.value.length&&p.value.length<x.total&&y({page:x.page+1})}function O(e){e.preventDefault()}function R(e){e.preventDefault(),e.dataTransfer.files.forEach((o,i)=>{setTimeout(()=>{k.upload.upload(o)},i*10)})}return he("upload-space",{selection:s,refresh:y,loading:u,list:p,preview:H}),I({selection:s,open,close,clear:A,refresh:y}),(e,o)=>{const i=b("el-button"),T=b("cl-upload"),J=b("el-icon"),K=b("el-scrollbar"),oe=b("cl-view-group"),ie=Z("infinite-scroll"),ne=Z("loading");return a(),c("div",{class:"cl-upload-space-inner",onDragover:O,onDrop:R},[d(oe,{ref_key:"ViewGroup",ref:r},{right:_(()=>{var Y,Q;return[f("div",Fe,[f("div",Ae,[d(i,{onClick:o[0]||(o[0]=j=>y({page:1}))},{default:_(()=>[L(" 刷新 ")]),_:1}),f("div",Ue,[d(T,{ref:n(t)("upload"),menu:"space",type:"file","show-file-list":!1,limit:m.limit,"limit-upload":!1,accept:m.accept,multiple:"","classify-id":(Q=(Y=n(r))==null?void 0:Y.selected)==null?void 0:Q.id,onSuccess:N,onUpload:P},{default:_(()=>[d(i,{type:"primary"},{default:_(()=>[L(" 点击上传 ")]),_:1})]),_:1},8,["limit","accept","classify-id"])]),m.selectable?F("",!0):(a(),V(i,{key:0,type:"danger",disabled:s.value.length===0,onClick:o[1]||(o[1]=j=>w())},{default:_(()=>[L(" 删除选中文件 ")]),_:1},8,["disabled"]))]),X((a(),V(K,{class:"cl-upload-space-inner__file"},{default:_(()=>[X((a(),c("div",He,[p.value.length>0?(a(),c("div",{key:0,class:te(["list",{"is-mini":n(B).isMini}])},[(a(!0),c(M,null,me(p.value,j=>(a(),c("div",{key:j.preload||j.url,class:"item"},[d(qe,{data:j,list:p.value,onConfirm:U,onSelect:l,onRemove:w},null,8,["data","list"])]))),128))],2)):(a(),c("div",Je,[d(J,{class:"el-icon--upload"},{default:_(()=>[d(n(ge))]),_:1}),Ke]))])),[[ie,G]])]),_:1})),[[ne,u.value]])])]}),_:1},512),d($e,{ref:n(t)("viewer")},null,512)],32)}}});export{tt as _};
