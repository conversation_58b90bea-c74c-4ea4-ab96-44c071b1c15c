import{c as qe,ae as gt,b as h,e as Se,z as Ve,A as bt,S as wt,f as M,y as _,h as l,B as f,G as D,w as n,i as k,I as pe,q as g,j as p,t as x,H as ze,v as C,W as Pe,F as Be,s as Qe,E as b,o as c,T as $e,af as It,ag as kt}from"./.pnpm-Kv7TmmH8.js";import{g as Ot,i as xt,s as z}from"./index-BuqCFB-b.js";import{u as Ct,_ as Mt}from"./bom-B8yQin8t.js";import{_ as Te}from"./material-table-columns.vue_vue_type_script_setup_true_lang-hBwyRg0n.js";import{n as P}from"./index-CBanFtSc.js";import{a as St}from"./index-BAHxID_w.js";import{_ as Vt}from"./material-selector.vue_vue_type_script_name_product-selector_setup_true_lang-B31T1lMk.js";import{_ as zt}from"./material-excel-import.vue_vue_type_script_setup_true_lang-xGZIJ5UI.js";import{_ as Pt}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./material-CnT3-AWx.js";const De=H=>(It("data-v-fd9d3b44"),H=H(),kt(),H),Bt={class:"cl-crud purchase-order-create"},Qt={class:"purchase-order-create-body"},$t={key:0,class:"purchase-order-create-step-one"},Tt=De(()=>_("div",{class:"purchase-order-create-header"}," 基础信息 ",-1));const qt={class:"purchase-order-create-header"},Dt={"w-full":"",flex:"~ justify-between items-center"},Nt={flex:"~"},Ut={key:1,flex:"~"},Et={key:1},Lt={mt1:""},Rt={class:"purchase-order-materials-card-header"},Ft={class:"purchase-order-materials-card-header-title"},At={style:{"margin-right":"10px"}},Wt={class:"purchase-order-materials-card-header"},jt={class:"purchase-order-materials-card-header-title"},Gt={key:0,"mr-10px":""},Ht={style:{"margin-right":"10px"}},Kt={class:"purchase-order-materials-card-header-quantity"},Jt={key:1},Xt={style:{"margin-left":"10px"}},Yt={key:0,class:"purchase-order-materials-card-header-action"},Zt={key:1,class:"purchase-order-create-step-tow"},ea={class:"purchase-order-create-header"},ta={key:0,flex:"~ justify-end items-center"},aa={key:0},la=["onClick"],ra=De(()=>_("div",{class:"flex items-center justify-center"},[_("div",{class:"mr-5px"}),p(" 可抵扣库存 ")],-1)),oa={key:2,class:"purchase-order-create-step-three"},na={class:"purchase-order-create-header"},ua={key:0,flex:"~ justify-end items-center"},ia={key:3},da={class:"dialog-footer"},sa={class:"dialog-footer"},ca=qe({name:"undefined"}),pa=qe({...ca,setup(H){const{dict:Ne}=Ot(),{router:S}=St(),{height:ve}=gt(),re=h({}),me=h({}),oe=h(!0),{getAllUsedColors:Ue,getBomMaterialCellStyle:Ee,getBomMaterialRowStyle:Le}=Ct(me),K=h(!1),J=h(!1),A=h(),B=h([]),N=h([]),o=h({id:0,productionOrderId:0,productionOrderSn:"",purchaseOrder:{id:0,orderNo:"",parentOrderId:0,productMaterials:N.value,customMaterials:[]}}),w=Se(()=>!o.value.productionOrderId),X=h(!0),he=h(),$=h([]),V=h([]),Y=h([]),O=h(1),U=h(0),Z=h(!1),ee=h(!1),ne=Se({get:()=>V.value.every(e=>e.isDeductInventory),set:e=>{V.value.forEach(t=>{t.isDeductInventory=e})}}),fe=h([]),Re=Ne.get("color"),te=h(!1),ye=h(1),L=h(!1),Fe=h(null),ue=h([]);function Ae(){let e=ue.value;if(e.length===0)return b.warning("请选择物料");e=e.filter(t=>!o.value.purchaseOrder.customMaterials.some(r=>r.materialId===t.id));for(const t of e)be(t);Z.value=!1,L.value=!1}function _e(e){var r;const t=(r=Re.value)==null?void 0:r.find(u=>u.value===e.color);return t?`${e.name} ${t.label}`:e.name}function ge(e,t,r){return N.value.find(i=>i.generateId===e)?Promise.resolve("操作成功"):new Promise((i,m)=>{z.pms.bom.GetBomMaterialByProductId({productId:e}).then(d=>{var y,Q,E,s,T;if(!d||!(d!=null&&d.materials)||!d.materials.length)return m(new Error(`产品【${(y=d.product)==null?void 0:y.name} - ${d.product.sku}】未维护BOM`));if((d==null?void 0:d.status)!==2)return m(new Error(`产品【${(Q=d.product)==null?void 0:Q.name} - ${d.product.sku}】BOM未审核，请等待审核通过后再生成`));{re.value[e]=d;const ae=(E=d.materials)==null?void 0:E.map(I=>{const W=I.quantity*t;let q=I.inventory+I.expectedInbound-I.lockedInventory;q=q>0?q:0;let R=W-q;R=R>0?R:0;const F={bomId:d.id,bomMaterialId:I.id,materialId:I.materialId,name:`${I.name}`,code:`${I.code}`,model:`${I.model}`,size:`${I.size}`,material:`${I.material}`,process:`${I.process}`,coverColor:`${I.coverColor}`,quantity:I.quantity,inventory:I.inventory,availableInventory:q,needQuantity:W,unit:I.unit,prepareQuantity:0,isDeductInventory:!1,total:R,lockedInventory:I.lockedInventory,expectedInbound:I.expectedInbound};return r&&r.length>0&&r.forEach(j=>{j.materialId===F.materialId&&(F.isDeductInventory=j.isDeductInventory===1,F.prepareQuantity=j.prepareQuantity)}),F});return N.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),generateId:e,generateType:1,generateQuantity:t,generateName:_e(d==null?void 0:d.product)||e.toString(),generateSku:(s=d==null?void 0:d.product)==null?void 0:s.sku,materials:ae,isAllDeductInventory:!0}),(d==null?void 0:d.id)>0&&((T=d==null?void 0:d.changeLog)==null?void 0:T.length)>0&&(me.value[d.id]=d==null?void 0:d.changeLog),i("操作成功")}}).catch(d=>m(d))})}function We(e,t){if(t.length===0)return b.error("导入的物料为空");o.value.purchaseOrder.customMaterials=e==null?void 0:e.filter(r=>!o.value.purchaseOrder.customMaterials.some(u=>u.materialId===r.materialId)).map(r=>{const u=t.find(i=>i.code.toUpperCase().replace(/\s/g,"")===r.code.toUpperCase().replace(/\s/g,""));return u&&(r.needQuantity=u.quantity,r.quantity=u.quantity),{...r,needQuantity:r.quantity,materialId:r.id}})}function be(e,t=0){var u;if(!e||!e.id)return;const r=e.id;o.value.purchaseOrder.customMaterials.unshift({bomId:0,bomMaterialId:e.id,materialId:e.id,name:`${e.name}`,code:`${e.code}`,model:`${e.model}`,size:`${e.size}`,material:`${e.material}`,process:`${e.process}`,coverColor:`${e.coverColor}`,quantity:t,needQuantity:t,unit:e.unit,inventory:e.inventory,avaliableInventory:e.inventory,prepareQuantity:0,isDeductInventory:!1,total:0}),$.value=(u=$.value)==null?void 0:u.map(i=>(i.value===r&&(i.disabled=!0),i))}async function je(e,t=0){const r=$.value.find(u=>u.value===e);return r?be(r,t):b.error("未找到物料信息")}function Ge(e){const{materialId:t,quantity:r}=e,u=o.value.purchaseOrder.customMaterials.find(i=>i.materialId===t);u&&(u.needQuantity=r,u.quantity=r)}function He(e){var r;const{materialId:t}=e;o.value.purchaseOrder.customMaterials=o.value.purchaseOrder.customMaterials.filter(u=>u.materialId!==t),$.value=(r=$.value)==null?void 0:r.map(u=>(u.value===t&&(u.disabled=!1),u))}function Ke(){$e.confirm("确认清空自选物料？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{var e;o.value.purchaseOrder.customMaterials=[],$.value=(e=$.value)==null?void 0:e.map(t=>(t.disabled=!1,t))}).catch(()=>{})}async function Je(){try{const e=await z.pms.material.list();$.value=(e==null?void 0:e.map(t=>({label:`${t.name} - ${t.model}`,value:t.id,disabled:o.value.purchaseOrder.customMaterials.some(r=>r.id===t.id),...t})))||[]}catch(e){b.error(e.message||"获取物料列表失败")}}const Xe=xt.useForm();function Ye(){te.value=!0}function Ze(){var e,t,r;if(B.value=(e=A.value)==null?void 0:e.getSelectionRows().map(u=>u.productId),B.value.length===0){b.warning("请选择产品");return}(t=A.value)==null||t.getSelectionRows().map(u=>{const{productId:i,quantity:m}=u;return i&&m&&ge(i,m).then(()=>{}).catch(d=>{b.error(d.message||"生成失败")}),u}),te.value=!1,(r=A.value)==null||r.clearSelection()}function et(e){var t;e._checked||(t=A.value)==null||t.toggleRowSelection(e)}function tt(e){B.value=B.value.filter(t=>t!==e.generateId),o.value.purchaseOrder.productMaterials=o.value.purchaseOrder.productMaterials.filter(t=>t.index!==e.index)}const at=h(!1);function lt(e){if(e)return z.pms.product.list({keyWord:e,unit:0}).then(t=>{t=t||[],fe.value=t==null?void 0:t.map(r=>{const u=_e(r),i=N.value.some(m=>m.generateType===1&&m.generateId===r.id);return{value:r.id,label:u,disabled:i}})})}function rt(e){const{generateId:t,generateQuantity:r}=e,u=N.value.find(i=>i.generateId===t);u&&(u.generateQuantity=r,u.materials.forEach(i=>{i.needQuantity=i.quantity*r}))}function ot(){V.value.forEach(e=>{e.isDeductInventory=ne.value})}const ie=h(),de=h(!1);async function nt(){ie.value&&await ie.value.validate(e=>{var t;if(e){if(w.value){const u=o.value.purchaseOrder.customMaterials.filter(m=>m.needQuantity===0);if(Z.value=!0,u.length){b.error("自选物料中有数量为0的物料，请检查");return}if(o.value.purchaseOrder.customMaterials.reduce((m,d)=>m+d.needQuantity,0)===0){b.error("物料总数量为0，无法提交，请检查");return}}Z.value=!1;const r=(t=o.value.purchaseOrder.customMaterials)==null?void 0:t.map(u=>({materialId:u.materialId,quantity:u.quantity}));he.value={id:o.value.id,productionOrderId:o.value.productionOrderId,productionOrderProductIds:B.value,customMaterials:r,purchaseOrderNo:o.value.purchaseOrder.orderNo},de.value=!0,z.pms.production.purchase.order.add(he.value).then(u=>{o.value.id=u.id,o.value.purchaseOrder.id=u.purchaseId;const i=u.id;let m=O.value+1;w.value&&(m=3),S.push({query:{id:i,step:m,is_edit:U.value}}),O.value=m}).catch(u=>{b.error(u.message||"保存采购订单失败，请重试")}).finally(()=>{de.value=!1})}})}function ut(){K.value=!0,z.pms.production.purchase.order.updateExtra({id:o.value.id,extras:Y.value}).then(e=>{if(K.value=!1,e&&e.success){b.success("更新采购订单成功");const t=O.value+1;O.value=t,S.push({query:{id:o.value.purchaseOrder.id,step:t,is_edit:U.value}})}else b.error("更新采购订单失败，请重试")}).catch(e=>{K.value=!1,b.error(e.message||"更新采购订单失败，请重试")})}function it(){if(Y.value.length===0){b.error("物料列表为空无法提交");return}let e="确认提交采购需求？ <br />";e+="<b>提交后无法继续编辑，确认无误后提交审核</b>",$e.confirm(e,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",dangerouslyUseHTMLString:!0,type:"warning"}).then(()=>{J.value=!0,z.pms.production.purchase.order.submit({id:o.value.id,extras:Y.value}).then(t=>{J.value=!1;const r=O.value+1;O.value=r,ye.value=(t==null?void 0:t.status)||1,S.push({query:{id:o.value.purchaseOrder.id,step:r,is_edit:U.value}}),b.success("提交采购需求成功")}).catch(t=>{J.value=!1,b.error(t.message||"提交采购需求失败，请重试")})}).catch(()=>{})}async function dt(e){z.pms.production.purchase.order.info({id:e}).then(t=>{if(o.value.id=e,o.value.productionOrderId=t.orderId||0,o.value.productionOrderSn=t.orderSn||"",o.value.purchaseOrder.id=t.purchaseId||0,o.value.purchaseOrder.orderNo=t.orderNo||"",t.orderStatus>=0){X.value=!1,O.value=3;return}if(!t.objects||t.objects.length===0){b.warning("当前获取到的采购订单物料清单为空, 请重新生成");return}const u=Number.parseInt(S.currentRoute.value.query.step);if(st(t.objects),S.currentRoute.value.query.step)O.value=u;else{const i=t.step||1;O.value=i,S.push({query:{id:o.value.id,step:i,is_edit:U.value}})}}).catch(t=>{b.error(t.message||"获取采购订单失败，请重试")})}function st(e){var t;(t=e==null?void 0:e.reverse())==null||t.map(r=>(r.generateType===1?(ge(r.generateId,r.generateQuantity,r.materialExtras),B.value.push(r.generateId)):r.generateType===0&&je(r.generateId,r.generateQuantity),r))}const se=h([]);async function ct(e){o.value.productionOrderId=e,z.pms.production.purchase.order.getProductByProductionOrder({orderId:e}).then(async t=>{(!t||!t.orderSn)&&b.error("获取订单信息错误"),(!t.products||!t.products.length)&&b.error("该订单已经没有可以生成的产品"),o.value.productionOrderSn=t.orderSn;for(const r of t.products)try{se.value.push(r)}catch(u){b.error(u.message||"生成失败");return}}).catch(t=>{b.error(t.message||"获取订单信息失败，请重试")})}function we(){const e=O.value-1;S.push({query:{id:o.value.id,step:e,is_edit:U.value}}),O.value=w.value?1:e}function pt(){var t;const e={};(t=N.value)==null||t.map(r=>(r.materials.forEach(i=>{e[i.materialId]===void 0&&(e[i.materialId]=i.inventory+i.expectedInbound-i.lockedInventory);let m=e[i.materialId];i.availableInventory=m;const d=i.quantity*r.generateQuantity;let y=d;i.isDeductInventory&&(y=d-m,m-=d,m=m>0?m:0,e[i.materialId]=m),y=y>0?y:0,y+=i.prepareQuantity,i.needQuantity=d,i.total=y>0?y:0}),r))}function vt(){S.push({path:"/pms/production/purchase/order",query:{expand:o.value.purchaseOrder.id,status:ye.value}})}async function Ie(e=!1,t){ee.value=!0,z.pms.purchase.order.summary({id:o.value.purchaseOrder.id,filterZero:e,checkBom:t}).then(r=>{r.length>0&&(V.value=r)}).catch(r=>{b.error(r.message||"获取采购订单汇总失败")}).finally(()=>{ee.value=!1})}Ve(N,()=>{pt()},{deep:!0}),Ve(O,async e=>{e===2?w.value?O.value=3:await Ie(!1,U.value!==1):e===3&&await Ie(!0,!1)});function mt(){V.value.forEach(e=>{e.inventory=e.inventory>0?e.inventory:0,e.total=e.isDeductInventory?e.purchaseTotal-e.inventory:e.purchaseTotal,e.total=e.total>0?e.total:0,e.total=e.total+e.prepareQuantity}),Y.value=V.value.map(e=>({...e,materialId:e.materialId,isDeductInventory:e.isDeductInventory,prepareQuantity:e.prepareQuantity}))}function ht(){return o.value.purchaseOrder.customMaterials.map(e=>e.materialId)}bt(()=>{mt()}),wt(async()=>{U.value=S.currentRoute.value.query.is_edit?Number.parseInt(S.currentRoute.value.query.is_edit):0,o.value.productionOrderId=Number.parseInt(S.currentRoute.value.query.orderId);const e=Number.parseInt(S.currentRoute.value.query.id);e?(w.value&&await Je(),await dt(e)):o.value.productionOrderId&&await ct(o.value.productionOrderId)});function ke(e){e.canEdit=!e.canEdit}function ft(e){return e.row.originPurchaseTotal!==e.row.purchaseTotal||e.row.bomChanged?"background-color: #C8C8F0;":""}return(e,t)=>{var Ce,Me;const r=k("el-step"),u=k("el-steps"),i=k("el-input"),m=k("el-form-item"),d=k("el-text"),y=k("el-button"),Q=k("el-tag"),E=k("el-input-number"),s=k("el-table-column"),T=k("el-table"),ae=k("el-card"),I=k("el-form"),W=k("el-switch"),q=k("el-tooltip"),R=k("el-result"),F=k("el-option"),j=k("el-select"),yt=k("cl-form"),Oe=k("el-dialog"),ce=ze("permission"),xe=ze("loading");return c(),M("div",null,[_("div",Bt,[_("div",Qt,[l(u,{active:O.value,"finish-status":"success","align-center":"",style:{margin:"10px 0"}},{default:n(()=>[l(r,{title:"生成物料列表",description:"通过成品生成或自选物料生成物料清单"}),D(l(r,{title:"设置物料信息",description:"修改备料/抵扣信息"},null,512),[[pe,!w.value]]),l(r,{title:"汇总物料信息",description:"检查物料清单，确认物料信息"}),l(r,{title:"提交订单采购需求",description:"将采购需求单提交并等待审核"})]),_:1},8,["active"]),O.value===1?(c(),M("div",$t,[l(I,{ref_key:"purchaseOrderForm",ref:ie,model:o.value.purchaseOrder,"label-width":"120px",size:"large","status-icon":""},{default:n(()=>[Tt,w.value?f("",!0):(c(),g(m,{key:0,"label-width":"90",label:"生产订单号",prop:"productionOrderSn","mb-0px":"","mb-10px":"","mt-14px":"","w-300px":""},{default:n(()=>[l(i,{modelValue:o.value.productionOrderSn,"onUpdate:modelValue":t[0]||(t[0]=a=>o.value.productionOrderSn=a),disabled:"",readonly:""},null,8,["modelValue"])]),_:1})),f("",!0),w.value?(c(),g(m,{key:2,"label-width":"70",label:"采购单号",prop:"purchaseOrder.orderNo","mb-0px":"","mb-10px":"","mt-14px":"","w-300px":""},{default:n(()=>[l(i,{modelValue:o.value.purchaseOrder.orderNo,"onUpdate:modelValue":t[1]||(t[1]=a=>o.value.purchaseOrder.orderNo=a),disabled:!!o.value.purchaseOrder.id},null,8,["modelValue","disabled"])]),_:1})):f("",!0),_("div",qt,[_("div",null,[p(" 物料信息"),D(_("span",null,"： 共计 "+x(B.value.length)+" 个产品",513),[[pe,B.value.length>0]])]),X.value?D((c(),g(y,{key:0,type:"success",size:"default",disabled:B.value.length===0&&o.value.purchaseOrder.customMaterials.length===0,loading:de.value,onClick:nt},{default:n(()=>[p(" 下一步 ")]),_:1},8,["disabled","loading"])),[[ce,C(z).pms.production.purchase.order.permission.add]]):f("",!0)]),_("div",Dt,[_("div",Nt,[o.value.productionOrderId>0&&o.value.id===0?(c(),g(y,{key:0,mr2:"",disabled:se.value.length===0,size:"default",type:"primary",onClick:Ye},{default:n(()=>[p(" 选择生产订单产品 ")]),_:1},8,["disabled"])):f("",!0),w.value?(c(),M("div",Ut,[l(y,{mr2:"",type:"primary",size:"default",onClick:t[2]||(t[2]=a=>L.value=!0)},{default:n(()=>[p(" 选择物料生成 ")]),_:1}),l(zt,{onSuccess:We})])):f("",!0)]),w.value?(c(),g(y,{key:0,disabled:o.value.purchaseOrder.customMaterials.length===0,size:"default",type:"danger",onClick:Ke},{default:n(()=>[p(" 清空列表 ")]),_:1},8,["disabled"])):f("",!0),w.value?f("",!0):(c(),M("div",Et,[l(Mt,{modelValue:oe.value,"onUpdate:modelValue":t[3]||(t[3]=a=>oe.value=a),colors:C(Ue)()},null,8,["modelValue","colors"])]))]),_("div",Lt,[w.value?D((c(),g(ae,{key:0,class:"purchase-order-materials-card"},{header:n(()=>[_("div",Rt,[_("div",Ft,[_("div",At,[l(Q,{size:"small",type:"danger"},{default:n(()=>[p(" 自选备货物料 ")]),_:1})]),p(" 共 "+x(o.value.purchaseOrder.customMaterials.length)+" 种物料 总数量 "+x(C(P)(o.value.purchaseOrder.customMaterials.reduce((a,v)=>a+v.needQuantity,0))),1)])])]),default:n(()=>[l(T,{data:o.value.purchaseOrder.customMaterials,border:"",size:"small",style:{width:"100%"}},{default:n(()=>[l(s,{prop:"needQuantity",label:"数量",align:"center",width:"200"},{default:n(({row:a})=>[l(E,{modelValue:a.quantity,"onUpdate:modelValue":v=>a.quantity=v,class:Pe(Z.value&&!(a.quantity>0)?"custom-material-input-error":""),min:0,size:"small",onChange:v=>Ge(a)},null,8,["modelValue","onUpdate:modelValue","class","onChange"])]),_:1}),l(Te),l(s,{prop:"unit",label:"单位",width:"200",align:"center"}),l(s,{label:"操作",width:"200",align:"center"},{default:n(({row:a})=>[l(y,{type:"danger",size:"small",onClick:v=>He(a)},{default:n(()=>[p(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},512)),[[pe,o.value.purchaseOrder.customMaterials.length>0]]):f("",!0),(c(!0),M(Be,null,Qe(o.value.purchaseOrder.productMaterials,a=>{var v;return c(),g(ae,{key:a.index,class:Pe((v=re.value[a.generateId])!=null&&v.isWarning?"purchase-order-materials-card b-red":"purchase-order-materials-card")},{header:n(()=>{var le;return[_("div",Wt,[_("div",jt,[(le=re.value[a.generateId])!=null&&le.isWarning?(c(),M("div",Gt,[l(Q,{size:"small",type:"danger"},{default:n(()=>[p(" BOM变化 ")]),_:1})])):f("",!0),_("div",Ht,[a.generateType===1?(c(),g(Q,{key:0,size:"small",type:"success"},{default:n(()=>[p(" 成品生成 ")]),_:1})):f("",!0),a.generateSku?(c(),g(Q,{key:1,size:"small",style:{"margin-left":"10px"}},{default:n(()=>[p(x(a.generateSku),1)]),_:2},1024)):f("",!0)]),p(" "+x(a.generateName)+" x ",1),_("div",Kt,[w.value?(c(),g(E,{key:0,modelValue:a.generateQuantity,"onUpdate:modelValue":G=>a.generateQuantity=G,min:1,size:"small",onChange:G=>rt(a)},null,8,["modelValue","onUpdate:modelValue","onChange"])):f("",!0),w.value?f("",!0):(c(),M("span",Jt,x(a.generateQuantity),1))]),_("span",Xt,"共 "+x(a.materials.length)+" 种物料 总数量 "+x(C(P)(a.materials.reduce((G,_t)=>G+_t.total,0))),1)]),!w.value&&o.value.id===0?(c(),M("div",Yt,[l(y,{type:"danger",size:"small",onClick:G=>tt(a)},{default:n(()=>[p(" 删除 ")]),_:2},1032,["onClick"])])):f("",!0)])]}),default:n(()=>[l(T,{class:"bom-material-table","row-style":oe.value?C(Le):()=>{},"cell-style":C(Ee),data:a.materials,"max-height":"400",border:"",size:"small",style:{width:"100%"}},{default:n(()=>[l(Te),l(s,{prop:"quantity",label:"BOM数量",align:"center",width:"100"}),l(s,{prop:"needQuantity",label:"需求数量",align:"center",width:"100"},{default:n(({row:le})=>[p(x(C(P)(le.needQuantity)),1)]),_:1}),l(s,{prop:"unit",label:"单位",width:"50",align:"center"})]),_:2},1032,["row-style","cell-style","data"])]),_:2},1032,["class"])}),128))])]),_:1},8,["model"])])):f("",!0),O.value===2?D((c(),M("div",Zt,[_("div",ea,[p(" 物料汇总 - 共 "+x(V.value.length)+" 种物料 - 总数量 "+x(C(P)((Ce=V.value)==null?void 0:Ce.reduce((a,v)=>a+v.total,0)))+" ",1),X.value?(c(),M("div",ta,[l(W,{modelValue:ne.value,"onUpdate:modelValue":t[4]||(t[4]=a=>ne.value=a),size:"small",mr10px:"","active-text":"全部抵扣","inactive-text":"全部不抵扣",onChange:ot},null,8,["modelValue"]),l(y,{type:"warning",size:"default",onClick:we},{default:n(()=>[p(" 上一步 ")]),_:1}),D((c(),g(y,{loading:K.value,type:"success",size:"default",onClick:ut},{default:n(()=>[p(" 汇总物料信息 ")]),_:1},8,["loading"])),[[ce,C(z).pms.production.purchase.order.permission.add]])])):f("",!0)]),l(T,{class:"bom-material-table",data:V.value,border:"","row-style":ft,size:"small",style:{width:"100%"},height:Math.max(C(ve)-390,100)},{default:n(()=>[l(s,{prop:"code",label:"编码",width:"150","show-overflow-tooltip":""}),l(s,{prop:"name",label:"名称",align:"left",width:"150","show-overflow-tooltip":""}),l(s,{prop:"model",label:"型号",align:"left","show-overflow-tooltip":""}),l(s,{prop:"size",label:"尺寸",align:"left",width:"150","show-overflow-tooltip":""}),l(s,{prop:"material",label:"物料材质",align:"left",width:"100","show-overflow-tooltip":""}),l(s,{prop:"process",label:"工艺",align:"left",width:"100","show-overflow-tooltip":""}),l(s,{prop:"coverColor",label:"颜色",align:"left",width:"100","show-overflow-tooltip":""}),l(s,{prop:"total",label:"需求数量",align:"center",width:"140"},{default:n(({row:a})=>[a.canEdit?(c(),g(E,{key:1,modelValue:a.purchaseTotal,"onUpdate:modelValue":v=>a.purchaseTotal=v,min:0,size:"small",onBlur:v=>ke(a)},null,8,["modelValue","onUpdate:modelValue","onBlur"])):(c(),M("div",aa,[l(q,{effect:"dark",disabled:a.originPurchaseTotal===a.purchaseTotal,content:`${a.originPurchaseTotal}`,placement:"top-start"},{default:n(()=>[_("div",{"cursor-pointer":"",onClick:v=>ke(a)},x(C(P)(a.purchaseTotal)),9,la)]),_:2},1032,["disabled","content"])]))]),_:1}),w.value?f("",!0):(c(),g(s,{key:0,prop:"prepareQuantity",label:"生产备料数量",align:"center",width:"150"},{default:n(({row:a})=>[l(E,{modelValue:a.prepareQuantity,"onUpdate:modelValue":v=>a.prepareQuantity=v,min:0,size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})),l(s,{prop:"lockedInventory",label:"订单需求数量",align:"center",width:"100"}),l(s,{prop:"expectedInbound",label:"在途",align:"center",width:"100"}),l(s,{prop:"inventory",label:"现有库存",align:"center",width:"100"}),l(s,{label:"可用库存",align:"center",width:"100"},{header:n(()=>[l(q,{effect:"dark",content:"可用库存 = 现有库存+在途-订单需求",placement:"top"},{default:n(()=>[ra]),_:1})]),default:n(({row:a})=>[p(x(C(P)(a.expectedInbound+a.inventory-a.lockedInventory)),1)]),_:1}),w.value?f("",!0):(c(),g(s,{key:1,prop:"isDeductInventory",label:"是否抵扣库存",align:"center",width:"100"},{default:n(({row:a})=>[l(W,{modelValue:a.isDeductInventory,"onUpdate:modelValue":v=>a.isDeductInventory=v,size:"small","active-value":!0,"inactive-value":!1},null,8,["modelValue","onUpdate:modelValue"])]),_:1})),l(s,{prop:"total",label:"合计需采购",align:"center",width:"100"},{default:n(({row:a})=>[p(x(C(P)(a.total)),1)]),_:1}),l(s,{prop:"unit",label:"单位",width:"100",align:"center"})]),_:1},8,["data","height"])])),[[xe,ee.value]]):f("",!0),O.value===3?D((c(),M("div",oa,[_("div",na,[p(" 物料汇总 - 共 "+x(V.value.length)+" 种物料 - 总数量 "+x(C(P)((Me=V.value)==null?void 0:Me.reduce((a,v)=>a+v.total,0)))+" ",1),X.value?(c(),M("div",ua,[l(y,{type:"warning",size:"default",onClick:we},{default:n(()=>[p(" 上一步 ")]),_:1}),D((c(),g(y,{loading:J.value,type:"success",size:"default",onClick:it},{default:n(()=>[p(" 提交采购需求 ")]),_:1},8,["loading"])),[[ce,C(z).pms.production.purchase.order.permission.add]])])):f("",!0)]),l(T,{class:"bom-material-table",data:V.value,border:"",size:"small",style:{width:"100%"},height:Math.max(C(ve)-390,100)},{default:n(()=>[l(s,{prop:"code",label:"编码",width:"150","show-overflow-tooltip":""}),l(s,{prop:"name",label:"名称",align:"left",width:"150","show-overflow-tooltip":""}),l(s,{prop:"model",label:"型号",align:"left","show-overflow-tooltip":""}),l(s,{prop:"size",label:"尺寸",align:"left",width:"150","show-overflow-tooltip":""}),l(s,{prop:"material",label:"物料材质",align:"left",width:"100","show-overflow-tooltip":""}),l(s,{prop:"process",label:"工艺",align:"left",width:"100","show-overflow-tooltip":""}),l(s,{prop:"coverColor",label:"颜色",align:"left",width:"100","show-overflow-tooltip":""}),l(s,{prop:"total",label:"需求数量",align:"center",width:"100"},{default:n(({row:a})=>[p(x(C(P)(a.purchaseTotal)),1)]),_:1}),w.value?f("",!0):(c(),g(s,{key:0,prop:"prepareQuantity",label:"生产备料数量",align:"center",width:"150"})),w.value?f("",!0):(c(),g(s,{key:1,prop:"isDeductInventory",label:"是否抵扣库存",align:"center",width:"100"},{default:n(({row:a})=>[a.isDeductInventory?(c(),g(Q,{key:0,size:"small",type:"success"},{default:n(()=>[p(" 是 ")]),_:1})):(c(),g(Q,{key:1,size:"small",type:"danger"},{default:n(()=>[p(" 否 ")]),_:1}))]),_:1})),w.value?f("",!0):(c(),g(s,{key:2,prop:"deductInventory",label:"抵扣数量",align:"center",width:"100"})),l(s,{prop:"total",label:"合计需采购",align:"center",width:"100"},{default:n(({row:a})=>[p(x(C(P)(a.total)),1)]),_:1}),l(s,{prop:"unit",label:"单位",width:"100",align:"center"})]),_:1},8,["data","height"])])),[[xe,ee.value]]):f("",!0),O.value===4?(c(),M("div",ia,[l(R,{icon:"success",title:"当前采购需求已提交","sub-title":"采购需求已确认，暂无法进行编辑"},{extra:n(()=>[l(y,{type:"primary",onClick:vt},{default:n(()=>[p(" 返回到采购需求列表 ")]),_:1})]),_:1})])):f("",!0)])]),l(yt,{ref_key:"PurchaseOrderSelectProductForm",ref:Xe},{"slot-product-selector":n(({scope:a})=>[l(j,{modelValue:a.productId,"onUpdate:modelValue":v=>a.productId=v,filterable:"","reserve-keyword":"",loading:at.value,placeholder:"请选择产品",style:{width:"400px"},remote:"","remote-method":lt},{default:n(()=>[(c(!0),M(Be,null,Qe(fe.value,v=>(c(),g(F,{key:v.value,disabled:v.disabled,label:v.label,value:v.value},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])]),_:1},512),l(Oe,{modelValue:te.value,"onUpdate:modelValue":t[5]||(t[5]=a=>te.value=a),title:"选择产品",width:"500"},{footer:n(()=>[_("span",da,[l(y,{type:"primary",onClick:Ze},{default:n(()=>[p(" 确认选择 ")]),_:1})])]),default:n(()=>[l(T,{ref_key:"PurchaseOrderProductTable",ref:A,data:se.value.sort((a,v)=>a.sku-v.sku),border:"","max-height":"600",size:"small",style:{width:"100%"},"row-key":a=>a.productId,"row-class-name":"cursor-pointer",onRowClick:et},{default:n(()=>[l(s,{type:"selection",width:"55",align:"center"}),l(s,{prop:"sku",label:"SKU",align:"center"}),l(s,{prop:"name",label:"产品名称",align:"center"}),l(s,{prop:"quantity",label:"数量",align:"center"})]),_:1},8,["data","row-key"])]),_:1},8,["modelValue"]),l(Oe,{modelValue:L.value,"onUpdate:modelValue":t[8]||(t[8]=a=>L.value=a),title:"选择物料",width:"80%"},{footer:n(()=>[_("span",sa,[l(y,{onClick:t[7]||(t[7]=a=>L.value=!1)},{default:n(()=>[p("取消")]),_:1}),l(y,{type:"primary",onClick:Ae},{default:n(()=>[p(" 确认选择 ")]),_:1})])]),default:n(()=>[L.value?(c(),g(Vt,{key:0,ref_key:"materialSelector",ref:Fe,modelValue:ue.value,"onUpdate:modelValue":t[6]||(t[6]=a=>ue.value=a),"can-add":"","has-stock":!1,"disabled-materials":ht()},null,8,["modelValue","disabled-materials"])):f("",!0)]),_:1},8,["modelValue"])])}}}),ka=Pt(pa,[["__scopeId","data-v-fd9d3b44"]]);export{ka as default};
