import{i as c}from"./index-BuqCFB-b.js";import{c as _,q as y,w as n,h as e,i as t,o as C}from"./.pnpm-Kv7TmmH8.js";import{a as g}from"./index-BAHxID_w.js";const w=_({name:"pms-product-group"}),B=_({...w,setup(q){const{service:s}=g(),p=c.useUpsert({items:[{label:"分组名称",prop:"name",required:!0,component:{name:"el-input"}},{label:"分组英文名",prop:"nameEn",required:!0,component:{name:"el-input"}}]}),a=c.useTable({columns:[{type:"selection"},{label:"分组名称",prop:"name"},{label:"分组英文名",prop:"nameEn"},{type:"op",buttons:["edit","delete"]}]}),u=c.useCrud({service:s.pms.product.group},l=>{l.refresh()});return(l,v)=>{const m=t("cl-refresh-btn"),d=t("cl-add-btn"),i=t("cl-multi-delete-btn"),r=t("cl-flex1"),f=t("cl-search-key"),o=t("el-row"),b=t("cl-table"),h=t("cl-pagination"),k=t("cl-upsert"),x=t("cl-crud");return C(),y(x,{ref_key:"Crud",ref:u},{default:n(()=>[e(o,null,{default:n(()=>[e(m),e(d),e(i),e(r),e(f)]),_:1}),e(o,null,{default:n(()=>[e(b,{ref_key:"Table",ref:a},null,512)]),_:1}),e(o,null,{default:n(()=>[e(r),e(h)]),_:1}),e(k,{ref_key:"Upsert",ref:p},null,512)]),_:1},512)}}});export{B as default};
