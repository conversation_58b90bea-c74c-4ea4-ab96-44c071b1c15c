import{c as M,b as z,e as A,f as V,q as $,B as F,h as j,i as v,w as d,j as k,F as q,s as D,t as L,o as y,V as G,A as U}from"./.pnpm-Kv7TmmH8.js";const H={flex:"~ justify-center items-center"},J=M({name:"undefined"}),P=M({...J,props:{modelValue:{type:Boolean},colors:{}},emits:["update:modelValue"],setup(f,{emit:i}){const p=f,h=i,u=z(p.modelValue),b=A(()=>p.colors);function C(){u.value=!u.value,h("update:modelValue",u.value)}return(_,I)=>{const g=v("el-tag"),e=v("el-button"),a=v("el-popover");return y(),V("div",H,[u.value?(y(),$(a,{key:0,title:"行颜色代表的意义",width:225,trigger:"hover"},{default:d(()=>[(y(!0),V(q,null,D(b.value,(r,o)=>(y(),$(g,{key:o,size:"small",style:G({backgroundColor:r}),"mb-5px":"","ml-5px":"","color-dark-1":""},{default:d(()=>[k(L(o),1)]),_:2},1032,["style"]))),128))]),reference:d(()=>[j(e,{size:"small",type:"primary",my2:"",mr0:""},{default:d(()=>[k(" 颜色说明 ")]),_:1})]),_:1})):F("",!0),j(e,{size:"small",type:u.value?"danger":"success",my2:"",ml0:"",mr10px:"",onClick:C},{default:d(()=>[k(L(u.value?"关闭":"显示")+"差异高亮 ",1)]),_:1},8,["type"])])}}});function Q(f){const i=z({});function p(e){const a=e==null?void 0:e.bomId,r=(e==null?void 0:e.materialId)||(e==null?void 0:e.id);return!a||!r||!i.value[a]?null:i.value[a][r]}function h({row:e,column:a}){var o;const r=(o=p(e))==null?void 0:o.filter(n=>n.operationType===0);for(const n of r||[])if(n.path===a.property)return{color:"#ef4444","font-weight":"bold"};return""}function u({row:e}){const a=e.materialId;if(!a)return"";const r=[];for(const o in i.value){const n=i.value[o];if(!n)continue;const t=n[a];t&&r.push(...t)}return g(r)}function b({row:e,column:a}){if(!e.materialId)return"";for(const o in i.value){const n=i.value[o];if(n)for(const t in n){const s=e.materialId;if(!t||t!==s.toString())continue;const c=n[s];if(c){for(const l of c||[])if(l.path===a.property)return"text-red-500"}}}return""}function C(){const e="图纸",a="物料信息",r="物料用量",o={};o.BOM新增物料="#2dd4bf";for(let s=0;s<=1;s++)for(let c=0;c<=1;c++)for(let l=0;l<=1;l++){const B=!!s,T=!!c,x=!!l,{r:E,g:N,b:R}=_(B,T,x),O=`rgb(${E},${N},${R})`,m=[];if(B&&m.push(e),T&&m.push(a),x&&m.push(r),m.length>0){const S=`${m.join(" + ")}变更`;o[S]||(o[S]=O)}}const n=Object.keys(o).sort(),t={};for(const s of n)t[s]=o[s];return t}function _(e,a,r){return{r:e?240:200,g:a?240:200,b:r?240:200}}function I({row:e},a){const r=p(e);return g(r,a)}function g(e,a=!0){if(!a)return"";if(e&&e.length>0){if(e.length===1&&e[0].type==="add")return"background-color: #2dd4bf; color: #3c3c3c;";{const r=e.some(l=>l.operationType===1),o=e.some(l=>l.operationType===0),n=e.some(l=>l.operationType===2),{r:t,g:s,b:c}=_(r,o,n);return`background-color: rgb(${t},${s},${c}); color: #3c3c3c;`}}return""}return U(()=>{if(f.value&&Object.keys(f.value).length>0)for(const e in f.value){const a=f.value[e];if(a&&a.length>0){const r={};a.forEach(o=>{var n;(n=o.operationContent)==null||n.forEach(t=>{var l;let s=0;if(o.operationType===0||o.operationType===1?s=o.materialId||0:o.operationType===2&&(s=((l=t==null?void 0:t.after)==null?void 0:l.materialId)||0),s<=0)return;const c={operationType:o==null?void 0:o.operationType,type:t==null?void 0:t.type,path:(t==null?void 0:t.path)||"",from:t==null?void 0:t.from,to:t==null?void 0:t.to};r[s]||(r[s]=[]),r[s].push(c)})}),i.value[e]=r}}}),{changeLogMap:i,getBomMaterialCellStyle:h,getBomMaterialRowStyle:I,getAllUsedColors:C,getBomSummaryCellClassName:b,getBomSummaryRowStyle:u}}export{P as _,Q as u};
