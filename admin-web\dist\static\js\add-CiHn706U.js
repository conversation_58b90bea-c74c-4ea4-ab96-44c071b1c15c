import{g as ae,j as x}from"./index-BuqCFB-b.js";import{a as le}from"./index-BAHxID_w.js";import{_ as ne,a as oe}from"./product-excel-import.vue_vue_type_script_setup_true_lang-DIL3NeAC.js";import{c as $,b as s,A as ue,f as S,y as p,h as o,w as r,i as c,j as f,F as de,s as re,t as D,B as ie,W as se,E as _,o as k,q as ce,af as me,ag as pe}from"./.pnpm-Kv7TmmH8.js";import{_ as ve}from"./_plugin-vue_export-helper-DlAUqK2U.js";const N=P=>(me("data-v-3445cd8b"),P=P(),pe(),P),fe={class:"cl-crud schedule-create"},_e={class:"order-create-body"},he=N(()=>p("div",{class:"schedule-create-header"}," 基础信息 ",-1)),ye=N(()=>p("div",{class:"schedule-create-header"}," 产品信息 ",-1)),be={key:0,style:{display:"flex","align-items":"center"}},ke={key:1,style:{display:"flex","align-items":"center"}},ge={key:0,style:{display:"flex","align-items":"center"}},we=N(()=>p("span",{style:{color:"var(--el-color-danger)"}},"*",-1)),xe={flex:"~ row justify-end items-center"},Se={class:"dialog-footer"},Pe=$({name:"undefined"}),Ve=$({...Pe,setup(P){const{service:V,router:C}=le(),{dict:F}=ae(),T=s(!1),i=s({id:null,sn:"",expectedDeliveryTime:"",products:[]}),L=[{label:"单品",value:0},{label:"内盒/展示盒",value:1},{label:"箱装",value:2}],h=s(!1),B=s(!1),I=s(),g=s(!1),u=s([]),q=s([]),w=F.get("color"),y=s([]),O=s(!1);async function j(){I.value&&await I.value.validate(a=>{if(a){if(B.value=!0,u.value.length===0){_.error({message:"请添加产品"});return}if(u.value.find(e=>e.id===null||e.id===void 0)){_.error({message:"请选择正确的产品"});return}if(u.value.find(e=>e.quantity===null||e.quantity===void 0||e.quantity===0)){_.error({message:"请填写正确的产品数量"});return}i.value.products=u.value.map(e=>({productId:e.id,quantity:e.quantity})),h.value=!0,T.value&&i.value.id&&i.value.id>0?V.pms.production.schedule.update(i.value).then(e=>{const d=e==null?void 0:e.id;C.push(`/pms/production/order?tab=0&expand=${d}`),_.success({message:"保存成功",onClose:()=>{h.value=!1}})}).catch(e=>{_.error({message:e.message}),h.value=!1}):(delete i.value.id,V.pms.production.schedule.add(i.value).then(e=>{const d=e==null?void 0:e.id;C.push(`/pms/production/order?tab=0&expand=${d}`),_.success({message:"保存成功",onClose:()=>{h.value=!1}})}).catch(e=>{_.error({message:e.message}),h.value=!1}))}})}function z(a){if(a)return V.pms.product.list({keyWord:a}).then(t=>{t=t||[],y.value=t.map(l=>({stock:l.stock,value:l.id,label:`${l.sku}`,name:l.name,nameEn:l.nameEn,color:l.color,disabled:u.value.some(e=>e.id===l.id),unit:l.unit,unitProductSku:l.unitProductSku,quantity:l.quantity}))})}const M=s();function A(){var a;q.value=[],g.value=!0,(a=M.value)==null||a.resetData()}function Q(a){const t=y.value.find(e=>e.value===a);if(!t)return;const l=u.value.findIndex(e=>e.id===a);l!==-1?(u.value[l].sku=t.label,u.value[l].name=t.name,u.value[l].nameEn=t.nameEn,u.value[l].unit=t.unit,u.value[l].color=x(w.value,Number.parseInt(t.color))):u.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),id:t.value,sku:t.label,name:t.name,nameEn:t.nameEn,color:x(w.value,Number.parseInt(t.color)),quantity:null,unit:t.unit,unitProductSku:t.unitProductSku}),y.value=y.value.map(e=>(e.value===a&&(e.disabled=!0),e))}function W(){g.value=!1,q.value.forEach(a=>{u.value.find(l=>l.id===a.id)||u.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),id:a.id,sku:a.sku,name:a.name,nameEn:a.nameEn,color:x(w.value,Number.parseInt(a.color)),quantity:null,unit:a.unit,unitProductSku:a.unitProductSku})})}function K(a){var t;u.value=u.value.filter(l=>l.index!==a),(t=M.value)==null||t.resetData()}function R(){y.value=[],u.value.push({index:new Date().getTime()+Math.floor(Math.random()*1e3),id:null,sku:"",name:"",nameEn:"",color:"",quantity:null,unit:null,unitProductSku:""})}function G(a){const t=L.find(l=>l.value===a);return(t==null?void 0:t.label)||""}async function H(a){await V.pms.production.schedule.info({id:a}).then(t=>{var e;const l=(t==null?void 0:t.products)||[];i.value.expectedDeliveryTime=((e=t==null?void 0:t.expectedDeliveryTime)==null?void 0:e.toString())||"",i.value.sn=(t==null?void 0:t.sn)||"",u.value=l.map(d=>({index:new Date().getTime()+Math.floor(Math.random()*1e3),id:d.productId,sku:d.sku,name:d.name,nameEn:d.nameEn,color:x(w.value,Number.parseInt(d.color)),quantity:d.quantity,unit:d.unit}))})}function J(a,t){const l=a.map(e=>{var E;const d=(E=t.find(U=>U.sku===e.sku))==null?void 0:E.quantity;return{index:new Date().getTime()+Math.floor(Math.random()*1e3),id:e.id,sku:e.sku,name:e.name,nameEn:e.nameEn,color:x(w.value,Number.parseInt(e==null?void 0:e.color)),quantity:d,unit:e==null?void 0:e.unit,unitProductSku:e==null?void 0:e.unitProductSku}});u.value.push(...l)}return ue(()=>{const a=C.currentRoute.value.query.id;a&&(T.value=!0,i.value.id=Number.parseInt(a),H(a))}),(a,t)=>{const l=c("el-input"),e=c("el-form-item"),d=c("el-button"),E=c("el-row"),U=c("el-option"),X=c("el-select"),b=c("el-table-column"),Y=c("el-input-number"),Z=c("el-table"),ee=c("el-form"),te=c("el-dialog");return k(),S("div",null,[p("div",fe,[p("div",_e,[o(ee,{ref_key:"scheduleForm",ref:I,model:i.value,size:"large","status-icon":""},{default:r(()=>[he,o(e,{label:"订单号",prop:"sn",rules:{required:!0,message:"订单号不能为空"}},{default:r(()=>[o(l,{modelValue:i.value.sn,"onUpdate:modelValue":t[0]||(t[0]=n=>i.value.sn=n),placeholder:"请输入订单号",width:"150",style:{"max-width":"300px"}},null,8,["modelValue"])]),_:1}),ye,o(E,null,{default:r(()=>[o(d,{type:"primary",size:"default",onClick:A},{default:r(()=>[f(" 选择产品 ")]),_:1}),o(ne,{onSuccess:J})]),_:1}),o(Z,{data:u.value,style:{width:"100%"},border:""},{default:r(()=>[o(b,{prop:"sku",label:"SKU",width:"200"},{default:r(n=>{var v;return[n.row.id===0||n.row.sku===""?(k(),S("div",be,[o(X,{modelValue:n.row.id,"onUpdate:modelValue":m=>n.row.id=m,filterable:"","reserve-keyword":"",loading:O.value,placeholder:"请选择产品",style:{width:"400px"},remote:"","remote-method":z,onChange:Q},{default:r(()=>[(k(!0),S(de,null,re(y.value,m=>(k(),ce(U,{key:m.value,disabled:m.disabled,label:m.label,value:m.value},null,8,["disabled","label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","loading"])])):(k(),S("div",ke,D((v=n.row)==null?void 0:v.sku),1))]}),_:1}),o(b,{prop:"name",label:"名称"},{default:r(n=>{var v,m;return[n.row.name===""&&n.row.nameEn===""?ie("",!0):(k(),S("div",ge,D((v=n.row)==null?void 0:v.name)+" / "+D((m=n.row)==null?void 0:m.nameEn),1))]}),_:1}),o(b,{prop:"unit",label:"单位",width:"150"},{default:r(n=>[p("span",null,D(G(n.row.unit)),1)]),_:1}),o(b,{prop:"color",label:"颜色",width:"250"}),o(b,{prop:"quantity",label:"*数量",width:"200"},{header:r(()=>[we,f(" 数量 ")]),default:r(n=>[p("div",{style:{display:"flex","align-items":"center"},class:se(B.value&&!(n.row.quantity>0)?"quantity-input-error":"")},[o(Y,{modelValue:n.row.quantity,"onUpdate:modelValue":v=>n.row.quantity=v,min:1,placeholder:"请输出产品数量"},null,8,["modelValue","onUpdate:modelValue"])],2)]),_:1}),o(b,{label:"操作",width:"120",align:"center"},{default:r(n=>[o(d,{type:"danger",onClick:v=>K(n.row.index)},{default:r(()=>[f(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),o(d,{style:{width:"100%"},class:"btn-product-add",onClick:R},{default:r(()=>[f(" + 添加产品 ")]),_:1}),p("div",xe,[o(d,{type:"success",style:{"margin-top":"20px"},loading:h.value,onClick:j},{default:r(()=>[f(" 保存为草稿 ")]),_:1},8,["loading"])])]),_:1},8,["model"])])]),o(te,{modelValue:g.value,"onUpdate:modelValue":t[3]||(t[3]=n=>g.value=n),title:"选择产品",width:"80%"},{footer:r(()=>[p("span",Se,[o(d,{onClick:t[2]||(t[2]=n=>g.value=!1)},{default:r(()=>[f("取消")]),_:1}),o(d,{type:"primary",onClick:W},{default:r(()=>[f(" 确认选择 ")]),_:1})])]),default:r(()=>[o(oe,{ref_key:"productSelector",ref:M,modelValue:q.value,"onUpdate:modelValue":t[1]||(t[1]=n=>q.value=n)},null,8,["modelValue"])]),_:1},8,["modelValue"])])}}}),Me=ve(Ve,[["__scopeId","data-v-3445cd8b"]]);export{Me as default};
