import{c as le,b as c,e as B,z as J,q as b,w as a,h as l,G as Ee,i as d,H as Ne,v as w,j as k,f as O,s as X,F as Z,y as p,t as y,B as S,Y as U,E as v,o as f,T as Y,K as ee,U as Re}from"./.pnpm-Kv7TmmH8.js";import{g as $e,c as ze,i as E,r as C}from"./index-BuqCFB-b.js";import{_ as te}from"./select-dict.vue_vue_type_script_setup_true_name_select-dict_lang-rWvH4Sy8.js";import{g as M}from"./index-wptcDEeL.js";import{u as Ye}from"./table-ops-B_rNWTRT.js";/* empty css              */import{a as Me}from"./index-BAHxID_w.js";const Ae={style:{"margin-bottom":"20px"}},Pe={style:{"margin-bottom":"8px"}},We=p("span",{style:{color:"red","margin-right":"5px"}},"*",-1),je=p("span",{style:{"font-weight":"500"}},"请上传送货单：",-1),Qe={key:0,style:{color:"#f56c6c","font-size":"12px","margin-left":"8px"}},Le={key:1,style:{color:"#67c23a","font-size":"12px","margin-left":"8px"}},He={flex:"~ items-center"},Fe=le({name:"pms-bill-payment"}),ot=le({...Fe,setup(Ge){const{dict:oe}=$e();oe.get("inbound_outbound_key");const{service:m}=Me(),A=c([{label:"草稿",value:0,count:0},{label:"已提交",value:1,count:0},{label:"已确认",value:2,count:0}]),_=c(0),{user:h}=ze(),N=c(!1),P=c(0),g=c("");async function W(){var i;h.info=h.info||{};const e=c(""),t=(i=h==null?void 0:h.info)==null?void 0:i.id;m.pms.supplier_account.request({url:"/getUserRole",method:"POST",data:{user_id:t}}).then(u=>{e.value=u.name}).catch(u=>{v.error("查询角色信息失败！")}),e.value==="供应商"?(await ae(),N.value=!1):N.value=!0}async function ae(){var e;try{const t=await m.pms.supplier_account.request({url:"/getUserBindSupplier",method:"GET",params:{user_id:(e=h==null?void 0:h.info)==null?void 0:e.id}});P.value=t.supplier_id}catch{v.error("获取供应商信息失败")}}const j=c({"slot-btn-confirm":{width:80,permission:m.pms.delivery_note.permission.submit,show:B(()=>_.value===0)},"slot-btn-edit":{width:80,permission:m.pms.delivery_note.permission.update,show:B(()=>_.value===0)},"slot-btn-delete":{width:80,permission:m.pms.delivery_note.permission.delete,show:B(()=>_.value===0)},"slot-btn-revoke":{width:80,permission:m.pms.delivery_note.permission.revoke,show:B(()=>_.value!==0)},"slot-btn-success":{width:80,permission:m.pms.delivery_note.permission.revoke,show:B(()=>_.value===1)},"slot-btn-print":{width:80,permission:m.pms.delivery_note.permission.page,show:!0}}),{getOpWidth:ne,checkOpButtonIsAvaliable:T,getOpIsHidden:re}=Ye(j),Q=c(),L=c(!1);J(_,()=>{Q.value=ne(),L.value=re()},{immediate:!0});const se=E.useUpsert({props:{class:"delivery-note-form",labelWidth:"120px"},items:[]}),H=E.useTable({columns:[{label:"#",prop:"products",type:"expand",width:50},{label:"创建时间",prop:"createTime",width:220},{label:"送货单号",prop:"no",width:220},{label:"PO",prop:"po",width:220},{label:"订单号",prop:"orderNo",width:220},{label:"送货总数",prop:"totalQuantity",width:220},{label:"供应商名称",prop:"supplier_name"},{label:"供应商订单号",prop:"supplier_order_no"},{label:"送货单",prop:"voucher",width:220,component:{name:"cl-image",props:{fit:"cover",lazy:!0,size:[50,50]}}},{label:"备注",prop:"remark"},{type:"op",label:"操作",width:Q,hidden:L,buttons:Object.keys(j.value)}]}),R=E.useCrud({service:m.pms.delivery_note,async onRefresh(e,{next:t,render:i}){const{count:u,list:x,pagination:z}=await t(e);A.value.forEach(I=>{I.count=u[I.value]||0}),i(x,z)}});function V(e={}){var i;const t={status:_.value,...N.value?{}:{supplier_id:P.value},...e};(i=R.value)==null||i.refresh(t)}const ie=E.useSearch({items:[{label:"关键字",prop:"keyWord",props:{labelWidth:"120px"},component:{name:"el-input",props:{clearable:!0,style:"width: 250px",placeholder:"请输入po/供应商订单号/送货单号",onChange(e){V({keyWord:e.trim(),page:1})}}}}]});function ue(){C.push("/pms/delivery_note/add")}function de(e){_.value=e,W().then(()=>{V()})}const F=c([]);J(()=>[C.currentRoute.value.query.tab,C.currentRoute.value.query.expand],([e,t])=>{W().then(()=>{e!=null?(_.value=Number.parseInt(e.toString()),V(),C.replace({query:{...C.currentRoute.value.query,tab:void 0}})):V(),t!=null&&(F.value=[Number.parseInt(t.toString())],C.replace({query:{...C.currentRoute.value.query,expand:void 0}}))})},{immediate:!0});function ce(e,t){var i;(t==null?void 0:t.type)==="expand"||(t==null?void 0:t.type)==="op"||(i=H.value)==null||i.toggleRowExpansion(e)}const s=c({delete:{},confirm:{},revoke:{},complete:{},dialogConfirm:!1});function pe(e){if(!e)return!1;Y.confirm("确认删除送货单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{s.value.delete[e]=!0,m.pms.delivery_note.delete({ids:[e]}).then(()=>{var t;v.success("送货单删除成功"),(t=R.value)==null||t.refresh()}).catch(t=>{v.error(t.message)}).finally(()=>{s.value.delete[e]=!1})})}function me(e){if(!e)return!1;C.push(`/pms/delivery_note/add?id=${e}`)}const D=c(!1),$=c([]);c([]);const q=c(0),G=c([]);function _e(e){if(!e){v.error("未找到该送货单数据");return}g.value="",q.value=e.id,g.value=e.voucher,$.value=(e.products||[]).map(t=>({...t,warehouseId:t.warehouseId||"",inbound_outbound_key:t.inbound_outbound_key||"",address:Array.isArray(t.address)?t.address:t.address?t.address.split(","):[],id:t.id||"",address_arr:t.contract.material.address_name!==""&&t.contract.material.address_name!==void 0?t.contract.material.address_name.split(","):[]})),D.value=!0}function fe(){if(!g.value||g.value.trim()===""){v.error("请先上传送货单文件");return}s.value.dialogConfirm=!0,G.value=($.value||[]).map(e=>(Array.isArray(e.address)&&(e.address=e.address.join(",")),{warehouseId:e.warehouseId||0,inbound_outbound_key:e.inbound_outbound_key||0,address:e.address,id:e.id||0})),m.pms.delivery_note.request({url:"/submit",method:"POST",data:{id:q.value,materials:G.value,voucher:g.value}}).then(e=>{v.success("提交成功"),_.value=2,D.value=!1,V()}).catch(e=>{v.error("提交送货单失败，请检查物料信息是否完整"),D.value=!1}).finally(()=>{s.value.dialogConfirm=!1,s.value.confirm[q.value]=!1})}function ve(){D.value=!1,s.value.confirm[q.value]=!1}function he(){s.value.confirm[q.value]=!1}function be(e){const t={id:e.id,no:e.no,po:e.po,order_id:e.orderId,total_quantity:e.totalQuantity,supplier_id:e.supplier_id,supplier_order_no:e.supplier_order_no,voucher:e.voucher,remark:e.remark,status:e.status};Y.confirm("撤销后物料入库的相关数据也会删除，确认撤销送货单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{s.value.revoke[e.id]=!0,m.pms.delivery_note.request({url:"/revoke",method:"POST",data:{delivery_note:t}}).then(i=>{v.success("撤销成功"),_.value=0,V()}).catch(i=>{v.error("撤销送货单失败")}).finally(()=>{s.value.revoke[e.id]=!1})})}function ye(e){Y.confirm("确认完成送货单,并同步入库数据吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{s.value.complete[e]=!0,m.pms.delivery_note.request({url:"/complete",method:"POST",data:{id:e}}).then(t=>{v.success("确认成功"),_.value=2,V()}).catch(t=>{v.error("确认失败")}).finally(()=>{s.value.complete[e]=!1})})}function ge(e){var i,u;const t={};t.inboundTime=e.inboundTime?ee(e.inboundTime).format("YYYY/MM/DD"):"",t.date=ee(new Date).format("YYYY/MM/DD"),t.user=(i=h==null?void 0:h.info)==null?void 0:i.name,t.supplier_name=e.supplier_name,t.po=e.po,t.orderNo=e.orderNo,t.no=e.no,t.supplier_order_no=e.supplier_order_no,t.totalQuantity=e.totalQuantity,t.voucher=e.voucher,((u=e.products)==null?void 0:u.length)>0&&(t.list=Re(e.products),t.list.forEach(x=>{console.log("item.warehouseId",x.warehouseId),x.warehouse=M("warehouse_name",x.warehouseId)})),sessionStorage.setItem("printData",JSON.stringify([t])),window.open(`${window.location.origin}/printDeliveNote.html`,"_blank")}function we(e){g.value=e.url||e}return(e,t)=>{const i=d("cl-refresh-btn"),u=d("el-button"),x=d("cl-flex1"),z=d("cl-search"),I=d("el-row"),ke=d("el-tab-pane"),r=d("el-table-column"),K=d("el-table"),xe=d("cl-table"),Ce=d("el-tabs"),Ve=d("cl-pagination"),Te=d("cl-upsert"),De=d("cl-upload"),Ie=d("el-option"),Se=d("el-select"),Ue=d("el-dialog"),qe=d("cl-crud"),Be=Ne("permission");return f(),b(qe,{ref_key:"Crud",ref:R},{default:a(()=>[l(I,null,{default:a(()=>[l(i),Ee((f(),b(u,{text:"",bg:"",type:"success",onClick:ue},{default:a(()=>[k(" 创建送货单 ")]),_:1})),[[Be,w(m).pms.delivery_note.permission.add]]),l(x),l(z,{ref_key:"Search",ref:ie},null,512)]),_:1}),l(Ce,{modelValue:_.value,"onUpdate:modelValue":t[0]||(t[0]=o=>_.value=o),type:"border-card",onTabChange:de},{default:a(()=>[(f(!0),O(Z,null,X(A.value,o=>(f(),b(ke,{key:o.value,label:`${o.label}(${o.count})`,name:o.value},null,8,["label","name"]))),128)),l(I,null,{default:a(()=>[l(xe,{ref_key:"Table",ref:H,"row-key":"id","expand-row-keys":F.value,class:"table-row-pointer",onRowClick:ce},{"slot-btn-edit":a(({scope:o})=>[w(T)("slot-btn-edit")?(f(),b(u,{key:0,text:"",bg:"",type:"primary",onClick:U(n=>me(o.row.id),["stop"])},{default:a(()=>[k(" 编辑 ")]),_:2},1032,["onClick"])):S("",!0)]),"slot-btn-delete":a(({scope:o})=>[w(T)("slot-btn-delete")?(f(),b(u,{key:0,text:"",bg:"",type:"danger",loading:s.value.delete[o.row.id],onClick:U(n=>pe(o.row.id),["stop"])},{default:a(()=>[k(" 删除 ")]),_:2},1032,["loading","onClick"])):S("",!0)]),"slot-btn-confirm":a(({scope:o})=>[w(T)("slot-btn-confirm")?(f(),b(u,{key:0,text:"",bg:"",type:"success",loading:s.value.confirm[o.row.id],onClick:U(n=>{s.value.confirm[o.row.id]=!0,_e(o.row)},["stop"])},{default:a(()=>[k(" 提交 ")]),_:2},1032,["loading","onClick"])):S("",!0)]),"slot-btn-revoke":a(({scope:o})=>[w(T)("slot-btn-revoke")?(f(),b(u,{key:0,text:"",bg:"",type:"success",loading:s.value.revoke[o.row.id],onClick:U(n=>be(o.row),["stop"])},{default:a(()=>[k(" 撤销 ")]),_:2},1032,["loading","onClick"])):S("",!0)]),"slot-btn-success":a(({scope:o})=>[w(T)("slot-btn-success")?(f(),b(u,{key:0,text:"",bg:"",type:"warning",loading:s.value.complete[o.row.id],onClick:U(n=>ye(o.row.id),["stop"])},{default:a(()=>[k(" 确认 ")]),_:2},1032,["loading","onClick"])):S("",!0)]),"slot-btn-print":a(({scope:o})=>[w(T)("slot-btn-print")?(f(),b(u,{key:0,text:"",bg:"",type:"info",loading:s.value.complete[o.row.id],onClick:U(n=>ge(o.row),["stop"])},{default:a(()=>[k(" 打印 ")]),_:2},1032,["loading","onClick"])):S("",!0)]),"column-products":a(({scope:o})=>[l(K,{data:o.row.products,style:{width:"100%"},border:""},{default:a(()=>[l(r,{prop:"id",label:"ID",width:"60",align:"center"}),l(r,{prop:"quantity",label:"送货数量",width:"90",align:"center"}),l(r,{label:"采购数量",width:"180",align:"center"},{default:a(n=>[p("span",null,y(n.row.contract.quantity),1)]),_:2},1024),l(r,{label:"已收数量",width:"180",align:"center"},{default:a(n=>[p("span",null,y(n.row.contract.receivedQuantity),1)]),_:2},1024),l(r,{label:"供应商",width:"220",align:"center"},{default:a(n=>[p("span",null,y(n.row.contract.supplier.name),1)]),_:2},1024),l(r,{label:"仓位",width:"180",align:"center"},{default:a(n=>[p("span",null,y(w(M)("warehouse_name",n.row.warehouseId)),1)]),_:2},1024),l(r,{label:"关键字",width:"180",align:"center"},{default:a(n=>[p("span",null,y(w(M)("inbound_outbound_key",n.row.inbound_outbound_key)),1)]),_:2},1024),l(r,{prop:"address",label:"位置",align:"center",width:"120","show-overflow-tooltip":""}),l(r,{label:"物料名称",width:"220",align:"center"},{default:a(n=>[p("span",null,y(n.row.contract.material.name),1)]),_:2},1024),l(r,{label:"物料编码",width:"180",align:"center"},{default:a(n=>[p("span",null,y(n.row.contract.material.code),1)]),_:2},1024),l(r,{label:"型号",align:"center"},{default:a(n=>[p("span",null,y(n.row.contract.material.model),1)]),_:2},1024),l(r,{label:"尺寸",width:"180",align:"center"},{default:a(n=>[p("span",null,y(n.row.contract.material.size),1)]),_:2},1024),l(r,{label:"单位",width:"180",align:"center"},{default:a(n=>[p("span",null,y(n.row.contract.material.unit),1)]),_:2},1024),l(r,{prop:"remark",label:"备注",width:"90",align:"center"})]),_:2},1032,["data"])]),_:1},8,["expand-row-keys"])]),_:1})]),_:1},8,["modelValue"]),l(I,null,{default:a(()=>[l(x),l(Ve)]),_:1}),l(Te,{ref_key:"Upsert",ref:se},null,512),l(Ue,{modelValue:D.value,"onUpdate:modelValue":t[2]||(t[2]=o=>D.value=o),title:"提交前确认物料信息",width:"80%",onClose:he},{footer:a(()=>[l(u,{onClick:ve},{default:a(()=>[k(" 取消 ")]),_:1}),l(u,{type:"primary",loading:s.value.dialogConfirm,onClick:fe},{default:a(()=>[k(" 确定 ")]),_:1},8,["loading"])]),default:a(()=>[p("div",Ae,[p("div",Pe,[We,je,g.value?(f(),O("span",Le," ✓ 文件已上传 ")):(f(),O("span",Qe," （必填项，请先上传文件再提交） "))]),l(De,{modelValue:g.value,"onUpdate:modelValue":t[1]||(t[1]=o=>g.value=o),type:"image",limit:1,"is-private":!1,multiple:!1,onSuccess:we},null,8,["modelValue"])]),l(K,{data:$.value,border:"",style:{"margin-bottom":"20px"}},{default:a(()=>[l(r,{label:"物料名称",prop:"contract.material.name"}),l(r,{label:"物料编码",prop:"contract.material.code"}),l(r,{label:"型号",prop:"contract.material.model"}),l(r,{label:"送货数量",prop:"quantity"}),l(r,{label:"仓位",align:"center",width:"220"},{default:a(o=>[l(te,{modelValue:o.row.warehouseId,"onUpdate:modelValue":n=>o.row.warehouseId=n,code:"warehouse_name",size:"small",width:"150px"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(r,{label:"关键字",align:"center",width:"150"},{default:a(o=>[l(te,{modelValue:o.row.inbound_outbound_key,"onUpdate:modelValue":n=>o.row.inbound_outbound_key=n,code:"inbound_outbound_key",size:"small",width:"100px"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(r,{label:"位置",align:"left",width:"220","show-overflow-tooltip":""},{default:a(o=>[p("div",He,[l(Se,{modelValue:o.row.address,"onUpdate:modelValue":n=>o.row.address=n,filterable:"",multiple:"",size:"small",style:{width:"220px"},placeholder:"请选择位置"},{default:a(()=>[(f(!0),O(Z,null,X(o.row.address_arr,(n,Oe)=>(f(),b(Ie,{key:Oe,label:n,value:n},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])]),_:1},512)}}});export{ot as default};
