import{c as Y,b as c,K as b,q as g,w as i,h as o,i as n,y as k,j as x,v as u,f as K,F as L,s as P,L as S,o as h}from"./.pnpm-Kv7TmmH8.js";import{s as U,i as V}from"./index-BuqCFB-b.js";const j={style:{"margin-right":"20px"}},E=Y({name:"MaterialInbound"}),Q=Y({...E,props:{api:{default:U.pms.outsource.inbound}},setup(T){const M=T,f=c(!1);c(!1);const t=c({date:[],keyWord:"",status:void 0,useStatus:!1,useType:!1}),a=V.useCrud({dict:{api:{page:"outsourceInboundPage"}},service:M.api,async onRefresh(e,{next:l,done:s,render:p}){s(),W(e);const{list:_,pagination:v}=await l(e);p(_,v)}},e=>{e.refresh()});function W(e){if(t.value.date&&t.value.date.length>0){const l=b(t.value.date[0]).format("YYYY-MM-DD"),s=b(t.value.date[1]).format("YYYY-MM-DD");e.date=`${l},${s}`}return t.value.keyWord?e.keyWord=t.value.keyWord:e.useType=!1,t.value.status!==void 0?(e.status=t.value.status,e.useStatus=!0,e.status===0&&(e.status=-1)):e.useStatus=!1,e}const w=c([{label:"草稿",value:0,type:"info"},{label:"已完成",value:3,type:"success"}]),D=V.useTable({columns:[{label:"入库单号",prop:"no",width:210},{label:"物料编码",prop:"code",width:180},{label:"物料名称",prop:"materialName",width:280},{label:"规格/型号",prop:"model",showOverflowTooltip:!0},{label:"单位",prop:"unit",width:80},{label:"入库类型",prop:"type",width:120,formatter(e){return"委外入库"}},{label:"状态",prop:"status",width:120,dict:w.value},{label:"入库凭证",prop:"voucher",width:180,component:{name:"cl-image",props:{fit:"cover",lazy:!0,size:[50,50]}}},{label:"入库数量",prop:"quantity",width:180},{label:"单据时间",prop:"inboundTime",width:280,formatter(e){return e.inboundTime?b(e.inboundTime).format("YYYY-MM-DD"):""}},{label:"创建时间",prop:"createTime",width:280}]});function d(){var e,l,s;try{(l=(e=a==null?void 0:a.value)==null?void 0:e.params)!=null&&l.page&&(a.value.params.page=1),f.value=!0,(s=a==null?void 0:a.value)==null||s.refresh()}catch(p){console.error(p)}finally{f.value=!1}}function m(){var e,l;t.value.keyWord="",t.value.date=[],(e=a==null?void 0:a.value)!=null&&e.params&&(a.value.params.page=1,a.value.params.size=20),(l=a==null?void 0:a.value)==null||l.refresh()}function q(e){e?d():m()}return(e,l)=>{const s=n("el-button"),p=n("cl-flex1"),_=n("el-option"),v=n("el-select"),B=n("el-date-picker"),C=n("el-input"),y=n("el-row"),N=n("cl-table"),z=n("cl-pagination"),I=n("cl-crud");return h(),g(I,{ref_key:"Crud",ref:a},{default:i(()=>[o(y,null,{default:i(()=>[o(s,{onClick:m},{default:i(()=>[x(" 刷新 ")]),_:1}),o(p),k("div",null,[o(v,{modelValue:u(t).status,"onUpdate:modelValue":l[0]||(l[0]=r=>u(t).status=r),placeholder:"请选择状态",clearable:"",class:"mr-20px w-200px",onChange:d},{default:i(()=>[(h(!0),K(L,null,P(u(w),r=>(h(),g(_,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),k("div",j,[o(B,{modelValue:u(t).date,"onUpdate:modelValue":l[1]||(l[1]=r=>u(t).date=r),type:"daterange",clearable:"","range-separator":"~","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:q},null,8,["modelValue"])]),o(C,{modelValue:u(t).keyWord,"onUpdate:modelValue":l[2]||(l[2]=r=>u(t).keyWord=r),placeholder:"请输入物料编号或物料名称或入库单号",style:{width:"500px"},clearable:"",onClear:m,onKeyup:S(d,["enter"])},null,8,["modelValue"]),o(s,{type:"primary",mx:"10px",loading:u(f),onClick:d},{default:i(()=>[x(" 搜索 ")]),_:1},8,["loading"])]),_:1}),o(y,null,{default:i(()=>[o(N,{ref_key:"Table",ref:D,"row-key":"rowIndex"},null,512)]),_:1}),o(y,null,{default:i(()=>[o(p),o(z)]),_:1})]),_:1},512)}}});export{Q as _};
