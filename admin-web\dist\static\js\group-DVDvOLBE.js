import{c as u,b as c,A as d,f as r,W as i,F as v,s as m,i as n,o,q as z,w as k,h as y,V as h}from"./.pnpm-Kv7TmmH8.js";import{_ as x}from"./_plugin-vue_export-helper-DlAUqK2U.js";const g=u({name:"cl-avatar-group"}),B=u({...g,props:{data:{type:Array,default:()=>[]},size:{type:Number,default:36},stack:{type:Boolean,default:!1}},setup(p){const a=p,s=c(a.size),t=c(a.stack);return d(()=>{s.value=a.size,t.value=a.stack,a.size<30&&(t.value=!1)}),(C,w)=>{const f=n("cl-avatar"),_=n("el-tooltip");return o(),r("div",{class:i(["cl-avatar-group",{stack:t.value}]),"cursor-default":""},[(o(!0),r(v,null,m(a.data,(e,l)=>(o(),z(_,{key:e.name,content:(e==null?void 0:e.name)||"系统管理员"},{default:k(()=>[y(f,{src:e.headImg,title:e.name,size:s.value,style:h(t.value?{zIndex:100-l,left:`${l*s.value/1.4}px`}:{})},null,8,["src","title","size","style"])]),_:2},1032,["content"]))),128))],2)}}}),b=x(B,[["__scopeId","data-v-4c855167"]]);export{b as default};
