import{c as H,q as p,w as c,h as i,i as u,j as _,o,b as C,S as le,f as g,y as n,G as h,H as ne,v as m,F as O,s as Q,B as J,aR as oe,E as d,W as X,Y as L,t as M,T as ae,af as ce,ag as ie}from"./.pnpm-Kv7TmmH8.js";import{i as G}from"./index-BuqCFB-b.js";import{a as Z}from"./index-BAHxID_w.js";import{_ as de}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ue=H({name:"user-selector"}),re=H({...ue,emits:["selected"],setup(I,{emit:a}){const B=a,{service:T}=Z(),W=G.useCrud({service:T.base.sys.user},y=>{y.refresh({status:1})}),f=G.useTable({autoHeight:!1,columns:[{prop:"name",label:"姓名",minWidth:150},{prop:"nickName",label:"昵称",minWidth:150},{prop:"departmentName",label:"部门名称",minWidth:150},{prop:"roleName",label:"角色",headerAlign:"center",minWidth:120},{type:"op",buttons:["slot-btn-select"],width:100}]});function k(y){B("selected",y)}return(y,D)=>{const N=u("cl-refresh-btn"),q=u("cl-flex1"),z=u("cl-search-key"),V=u("cl-row"),P=u("el-button"),A=u("cl-table"),U=u("cl-pagination"),j=u("cl-crud");return o(),p(j,{ref_key:"Crud",ref:W},{default:c(()=>[i(V,null,{default:c(()=>[i(N),i(q),i(z,{placeholder:"搜索用户名、姓名"})]),_:1}),i(V,null,{default:c(()=>[i(A,{ref_key:"Table",ref:f,"max-height":"600"},{"slot-btn-select":c(({scope:F})=>[i(P,{type:"success",size:"small",onClick:K=>k(F.row)},{default:c(()=>[_(" 选择 ")]),_:2},1032,["onClick"])]),_:1},512)]),_:1}),i(V,null,{default:c(()=>[i(q),i(U)]),_:1})]),_:1},512)}}}),pe=I=>(ce("data-v-a6a84fc0"),I=I(),ie(),I),me={flex:"~ row wrap "},_e={mb3:"","w-full":""},fe=["onClick"],ve={flex:"~ col justify-between items-center",relative:"","h-full":"","w-full":""},he={"h-full":"","w-full":"","flex-grow":"","overflow-auto":"",flex:"~ items-center col justify-center"},be={class:"absolute left-2 top-2 cursor-pointer"},ge=["onClick"],ye={class:"absolute right-2 top-2 cursor-pointer"},ke=["onClick"],xe={h10:"","line-height-10":""},Ce={flex:"~ justify-between items-center","h-10":"","w-full":"","b-rd-3":""},Ne=["onClick"],Ve=["onClick"],Pe={mb5:"","w-full":"",flex:"~ justify-between items-center"},Ue={key:0},Fe={"w-full":"",class:"process-node-table"},Se=pe(()=>n("thead",null,[n("tr",null,[n("th",null,"节点名称"),n("th",null,"执行类型"),n("th",null,"执行对象"),n("th",{width:"110"}," 操作 ")])],-1)),$e={class:"sort-target"},Ie=H({name:"undefined"}),qe=H({...Ie,setup(I){const{service:a}=Z(),B=[{label:"用户",value:0}],T=C([]),W=G.useForm(),f=C([]),k=C(!1);function y(e){var l;const s=!!(e!=null&&e.id);(l=W.value)==null||l.open({title:s?"更新":"新增审核流程",width:"50%",labelWidth:"100px",items:[{prop:"name",label:"名称",value:e==null?void 0:e.name,required:!0,component:{name:"el-input"}},{prop:"key",label:"代号",value:e==null?void 0:e.key,required:!0,component:{name:"el-input",props:{disabled:s}}},{prop:"status",label:"状态",value:e==null?void 0:e.status,required:!0,component:{name:"el-switch",props:{clearable:!0,activeValue:1,inactiveValue:0}}},{prop:"icon",label:"图标",value:e==null?void 0:e.icon,required:!0,component:{name:"cl-menu-icon"}}],on:{submit:(v,{close:x,done:S})=>{e!=null&&e.id&&(v.id=e.id),(s?a.pms.audit.process.update(v):a.pms.audit.process.add(v)).then(()=>{d.success(s?"更新流程成功":"新增流程成功"),P(),x(),S()}).catch($=>{d.error($.message||(s?"更新流程失败":"新增流程失败"))})}}})}const D=C(!1),N=C(),q=C(!1);function z(){var s;const e=(s=N.value)==null?void 0:s.id;e||d.warning("请先选择流程"),q.value=!0,a.pms.audit.process.nodes({processId:e}).then(l=>{f.value=l==null?void 0:l.map(v=>v)}).catch(l=>{d.error(l.message||"获取流程节点列表失败")}).finally(()=>{q.value=!1})}function V(e){e.id||d.warning("请先选择流程"),N.value=e,z(),D.value=!0}function P(){a.pms.audit.process.list().then(e=>{T.value=e==null?void 0:e.map(s=>s)}).catch(e=>{d.error(e.message||"获取流程列表失败")})}function A(e){if(!e.id){d.warning("请先选择流程");return}ae.confirm("确定删除该流程吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{a.pms.audit.process.delete({ids:[e.id]}).then(()=>{d.success("删除流程成功"),P()}).catch(s=>{d.error(s.message||"删除流程失败")})}).catch(()=>{})}const U=G.useForm();function j(e){var x,S;const s=(x=N.value)==null?void 0:x.id;s||d.warning("请先选择流程");const l=!!(e!=null&&e.id),v=e==null?void 0:e.value;(S=U.value)==null||S.open({title:l?"更新":"新增流程节点",width:"400px",dialog:{controls:["close"]},labelWidth:"100px",items:[{prop:"name",label:"节点名称",value:e==null?void 0:e.name,required:!0,component:{name:"el-input"}},{prop:"type",label:"执行类型",value:(e==null?void 0:e.type)||0,required:!0,component:{name:"el-select",props:{clearable:!0},options:B}},{prop:"displayName",label:"执行对象",value:e==null?void 0:e.displayName,component:{name:"slot-user-select"}}],on:{submit(E,{close:$,done:R}){a.pms.audit.process.updateNode({value:v,...E,processId:s,id:e==null?void 0:e.id}).then(()=>{d.success(l?"更新流程节点成功":"新增流程节点成功"),z(),$()}).catch(r=>{d.error(r.message||(l?"更新流程节点失败":"新增流程节点失败"))}).finally(()=>{R()})}}})}const F=C(!1);function K(){F.value=!0}function w(e){var s,l;(s=U.value)==null||s.setForm("value",e.id),(l=U.value)==null||l.setForm("displayName",e.name),F.value=!1}function ee(e){f.value.splice(f.value.findIndex(s=>s.id===e),1),k.value=!0,d.success("删除流程节点成功，记得保存哦！！！")}function se(){var s;const e=(s=N.value)==null?void 0:s.id;e||d.warning("请先选择流程"),a.pms.audit.process.saveNodes({processId:e,nodes:f.value}).then(()=>{d.success("保存流程节点成功")}).catch(l=>{d.error(l.message||"保存流程节点失败")}),k.value=!1}function te(){k.value=!0}return le(()=>{P()}),(e,s)=>{const l=u("el-button"),v=u("cl-svg"),x=u("cl-form"),S=u("el-tag"),E=u("el-empty"),$=u("cl-dialog"),R=u("el-input"),r=ne("permission");return o(),g("div",me,[n("div",_e,[h((o(),p(l,{onClick:P},{default:c(()=>[_(" 刷新 ")]),_:1})),[[r,m(a).pms.audit.process.permission.list]]),h((o(),p(l,{type:"success",onClick:s[0]||(s[0]=t=>y())},{default:c(()=>[_(" 新增 ")]),_:1})),[[r,m(a).pms.audit.process.permission.add]])]),(o(!0),g(O,null,Q(T.value,t=>(o(),g("div",{key:t.id,mb4:"",mr4:"","h-50":"","w-50":"",border:"~ 1 solid",class:X(t.status?"b-green-5":"b-gray-5"),"b-rd-3":"","bg-white":"",onClick:b=>V(t)},[n("div",ve,[n("div",he,[n("div",be,[n("div",{class:X(t.status?"i-material-symbols:check-circle-rounded text-green-5":"i-material-symbols:bid-landscape-disabled text-gray-5"),onClick:L(b=>y(t),["stop"])},null,10,ge)]),h((o(),g("div",ye,[n("div",{class:"i-material-symbols:close-rounded hover:text-red",onClick:L(b=>A(t),["stop"])},null,8,ke)])),[[r,m(a).pms.audit.process.permission.delete]]),t.icon?(o(),p(v,{key:0,name:t.icon,"text-size-24":"","c-blue":""},null,8,["name"])):J("",!0),t.icon?J("",!0):(o(),p(v,{key:1,name:"icon-question","text-size-24":"","c-blue":""})),n("div",xe,[n("div",null,M(t.name),1)])]),n("div",Ce,[h((o(),g("div",{class:"w-50% text-center",hover:"","h-full":"","cursor-pointer":"","rounded-bl-3":"","bg-gray-1":"","text-size-14px":"","font-450":"","line-height-10":"","text-blue-5":"",onClick:L(b=>y(t),["stop"])},[_(" 编辑 ")],8,Ne)),[[r,m(a).pms.audit.process.permission.delete]]),h((o(),g("div",{class:"ml-1px w-50% text-center","h-full":"","cursor-pointer":"","rounded-br-3":"","bg-gray-1":"","text-size-14px":"","font-450":"","line-height-10":"","text-green-5":"",onClick:L(b=>V(t),["stop"])},[_(" 节点列表 ")],8,Ve)),[[r,m(a).pms.audit.process.permission.nodes]])])])],10,fe))),128)),i(x,{ref_key:"ProcessForm",ref:W},null,512),i($,{modelValue:D.value,"onUpdate:modelValue":s[3]||(s[3]=t=>D.value=t),width:"50%",controls:["close"],title:"配置审核流程节点","close-on-click-modal":!1,"close-on-press-escape":!1},{default:c(()=>[n("div",Pe,[n("div",null,[h((o(),p(l,{onClick:z},{default:c(()=>[_(" 刷新 ")]),_:1})),[[r,m(a).pms.audit.process.permission.nodes]]),h((o(),p(l,{type:"success",onClick:s[1]||(s[1]=t=>j())},{default:c(()=>[_(" 新增 ")]),_:1})),[[r,m(a).pms.audit.process.permission.updateNode]])]),n("div",null,[k.value?(o(),p(S,{key:0,type:"danger","mr-3":""},{default:c(()=>[_(" 节点已修改，请保存 ")]),_:1})):J("",!0),h((o(),p(l,{type:"warning",disabled:!k.value,onClick:se},{default:c(()=>[_(" 保存 ")]),_:1},8,["disabled"])),[[r,m(a).pms.audit.process.permission.saveNodes]])])]),f.value.length?(o(),g("div",Ue,[i(m(oe),{modelValue:f.value,"onUpdate:modelValue":s[2]||(s[2]=t=>f.value=t),target:".sort-target",animation:150,onChange:te},{default:c(()=>[n("table",Fe,[Se,n("tbody",$e,[(o(!0),g(O,null,Q(f.value,t=>{var b;return o(),g("tr",{key:t.id,class:"cursor-move"},[n("td",null,M(t.name),1),n("td",null,M((b=B.find(Y=>Y.value===t.type))==null?void 0:b.label),1),n("td",null,M(t.displayName),1),n("td",null,[h((o(),p(l,{type:"primary",size:"small",onClick:Y=>j(t)},{default:c(()=>[_(" 编辑 ")]),_:2},1032,["onClick"])),[[r,m(a).pms.audit.process.permission.saveNodes]]),h((o(),p(l,{type:"danger",size:"small",onClick:Y=>ee(t.id)},{default:c(()=>[_(" 删除 ")]),_:2},1032,["onClick"])),[[r,m(a).pms.audit.process.permission.saveNodes]])])])}),128))])])]),_:1},8,["modelValue"])])):(o(),p(E,{key:1,description:"暂无数据"}))]),_:1},8,["modelValue"]),i(x,{ref_key:"ProcessNodeForm",ref:U},{"slot-user-select":c(({scope:t})=>[i(R,{modelValue:t.displayName,"onUpdate:modelValue":b=>t.displayName=b,readonly:"",placeholder:"请选择执行对象","cursor-pointer":"",onClick:K},null,8,["modelValue","onUpdate:modelValue"])]),_:1},512),i($,{modelValue:F.value,"onUpdate:modelValue":s[4]||(s[4]=t=>F.value=t),title:"选择用户",width:"50%",controls:["close"]},{default:c(()=>[i(re,{onSelected:w})]),_:1},8,["modelValue"])])}}}),De=de(qe,[["__scopeId","data-v-a6a84fc0"]]);export{De as default};
